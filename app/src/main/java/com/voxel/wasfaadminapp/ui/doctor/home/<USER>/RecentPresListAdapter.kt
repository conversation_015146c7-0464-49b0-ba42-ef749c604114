package com.voxel.wasfaadminapp.ui.doctor.home.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.voxel.wasfaadminapp.databinding.ItemRecentPresBinding
import com.voxel.wasfaadminapp.network.response.PresHome
import com.voxel.wasfaadminapp.ui.home.model.Cat

class RecentPresListAdapter(
    private val CatList: List<PresHome>?,
    private val listener: (PresHome, String) -> Unit
) :
    RecyclerView.Adapter<RecentPresListAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = ItemRecentPresBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindItem(CatList!![position])

        holder.itemView.setOnClickListener{
            listener(CatList!![position],"view")
        }

    }

    override fun getItemCount(): Int {
        return CatList!!.size
    }

    class ViewHolder(var itemBinding: ItemRecentPresBinding) :
        RecyclerView.ViewHolder(itemBinding.root) {
        fun bindItem(data: PresHome) {
            itemBinding.txtCustName.text = data?.name
            itemBinding.txtMedPres.text = data?.medicationsPrescribed
            itemBinding.txtPhone.text =
                if (data?.patientInfo?.get(0)?.phone.isNullOrBlank() || data?.patientInfo?.get(0)?.phone == "null") {
                    ""
                } else {
                    data?.patientInfo?.get(0)?.phone
                }


            Glide.with(itemBinding.root.context)
                .load(data?.patientInfo?.get(0)?.profilePic)
                .into(itemBinding.imgCust)
        }
    }
}