package com.voxel.wasfaadminapp.ui.sales.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import androidx.recyclerview.widget.RecyclerView
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.ItemOrderListBinding
import com.voxel.wasfaadminapp.databinding.ItemPosListBinding
import com.voxel.wasfaadminapp.databinding.ItemProductListBinding
import com.voxel.wasfaadminapp.databinding.ItemSalesListBinding
import com.voxel.wasfaadminapp.network.response.Orders
import com.voxel.wasfaadminapp.network.response.Products
import com.voxel.wasfaadminapp.ui.home.model.Cat

class OrderListAdapter(
    private val data: MutableList<Orders>,
    private val listener: (Orders, String) -> Unit,
    private val listenerDelivery: (Orders, String) -> Unit
) :
    RecyclerView.Adapter<OrderListAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = ItemOrderListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindItem(data!![position])
        holder.itemBinding.cardView.setOnClickListener { view ->
          listener(data!![position], "view")

        }
        holder.itemBinding.cardChangeStatus.setOnClickListener {
            val isVisible = holder.itemBinding.lytStatus.visibility == View.VISIBLE
            holder.itemBinding.lytStatus.visibility = if (isVisible) View.GONE else View.VISIBLE
        }

        holder.itemBinding.txtPending.setOnClickListener {
            holder.itemBinding.txtOrderStatus.text = "Pending"
            holder.itemBinding.lytStatus.visibility = View.GONE
            listenerDelivery(data!![position], "Pending")

        }
        holder.itemBinding.txtConfirmed.setOnClickListener {
            holder.itemBinding.txtOrderStatus.text = "Confirmed"
            holder.itemBinding.lytStatus.visibility = View.GONE
            listenerDelivery(data!![position], "Confirmed")

        }
        holder.itemBinding.txtPickedUp.setOnClickListener {
            holder.itemBinding.txtOrderStatus.text = "Picked Up"
            holder.itemBinding.lytStatus.visibility = View.GONE
            listenerDelivery(data!![position], "Picked Up")
        }
        holder.itemBinding.txtOnTheWay.setOnClickListener {
            holder.itemBinding.txtOrderStatus.text = "On The Way"
            holder.itemBinding.lytStatus.visibility = View.GONE
            listenerDelivery(data!![position], "On The Way")
        }
    }

    override fun getItemCount(): Int {
        return data!!.size
    }

    class ViewHolder(var itemBinding: ItemOrderListBinding) :
        RecyclerView.ViewHolder(itemBinding.root) {
        fun bindItem(data: Orders) {

            itemBinding.txtOrderId.text = data?.orderCode
            itemBinding.txtPatientName.text = data?.customername
            itemBinding.txtPatientNumber.text = data?.mobileNumber
            itemBinding.txtPatientAddress.text = data?.customerAddress
            itemBinding.txtOrderStatus.text = data?.deliveryStatus
            itemBinding.txtOrderAmount.text = data?.orderAmount
            itemBinding.txtOrderQuantity.text = data?.productCount
            itemBinding.txtPickUpPoint.text = data?.pickupPoints

        }
    }
    fun setOrder(newProducts: List<Orders>) {
        data.clear()
        data.addAll(newProducts)
        notifyDataSetChanged()
    }

    fun addOrder(newProducts: List<Orders>) {
        val startPosition = data.size
        data.addAll(newProducts)
        notifyItemRangeInserted(startPosition, newProducts.size)
    }
}