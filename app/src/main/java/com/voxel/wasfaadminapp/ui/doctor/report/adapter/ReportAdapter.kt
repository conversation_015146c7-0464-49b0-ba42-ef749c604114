package com.voxel.wasfaadminapp.ui.doctor.report.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.voxel.wasfaadminapp.databinding.ItemCustomersBinding
import com.voxel.wasfaadminapp.databinding.ItemInfluencerListBinding
import com.voxel.wasfaadminapp.databinding.ItemPosListBinding
import com.voxel.wasfaadminapp.databinding.ItemPresBinding
import com.voxel.wasfaadminapp.databinding.ItemProductListBinding
import com.voxel.wasfaadminapp.databinding.ItemReportBinding
import com.voxel.wasfaadminapp.databinding.ItemSalesListBinding
import com.voxel.wasfaadminapp.databinding.ItemSellerListBinding
import com.voxel.wasfaadminapp.network.response.Prescriptions
import com.voxel.wasfaadminapp.network.response.Products
import com.voxel.wasfaadminapp.network.response.Report
import com.voxel.wasfaadminapp.ui.home.model.Cat

class ReportAdapter(
    private val data: MutableList<Report>,
    private val listener: (Report, String) -> Unit,
) :
    RecyclerView.Adapter<ReportAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = ItemReportBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindItem(data!![position])


    }

    override fun getItemCount(): Int {
        return data!!.size
    }

    class ViewHolder(var itemBinding: ItemReportBinding) :
        RecyclerView.ViewHolder(itemBinding.root) {
        fun bindItem(data: Report) {
            itemBinding.txtOrderId.text = data?.orderCode
            itemBinding.txtDoctorAppreciation.text = data?.doctorAppreciationAmount
            itemBinding.txtSellingPrice.text = data?.sellingPriceAfterAllDiscount
            itemBinding.txtPharmaStatus.text = data?.isPharmacutecal
            itemBinding.txtItemName.text = data?.itenname
            itemBinding.txtOrderDate.text = data?.date


        }
    }

    fun setProducts(newProducts: List<Report>) {
        data.clear()
        data.addAll(newProducts)
        notifyDataSetChanged()
    }

    fun addProducts(newProducts: List<Report>) {
        val startPosition = data.size
        data.addAll(newProducts)
        notifyItemRangeInserted(startPosition, newProducts.size)
    }
}