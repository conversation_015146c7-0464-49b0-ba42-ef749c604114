package com.voxel.wasfaadminapp.ui.sales.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.ItemOrderDetailsBinding
import com.voxel.wasfaadminapp.network.response.ItemsList
import com.voxel.wasfaadminapp.ui.home.model.Cat

class OrderDetailsAdapter(
    private val CatList: List<ItemsList>?,
    private val listener: (ItemsList, Int) -> Unit
) :
    RecyclerView.Adapter<OrderDetailsAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = ItemOrderDetailsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindItem(CatList!![position])

    }

    override fun getItemCount(): Int {
        return CatList!!.size
    }

    class ViewHolder(var itemBinding: ItemOrderDetailsBinding) :
        RecyclerView.ViewHolder(itemBinding.root) {
        fun bindItem(data: ItemsList) {

            Glide.with(itemBinding.root.context)
                .load(data.thumbnailImage)
                .error(R.drawable.wasfa_logo)
                .into(itemBinding.imgProduct)

            itemBinding.txtProductName.text = data?.productName
            itemBinding.txtPrize.text =  data?.price
            itemBinding.txtSale.text =  data?.quantity

        }
    }
}