package com.voxel.wasfaadminapp.ui.customers.type

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.card.MaterialCardView
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.FragmentSegmentBinding
import com.voxel.wasfaadminapp.databinding.FragmentTypeBinding
import com.voxel.wasfaadminapp.ui.customers.adapter.SegmentListAdapter
import com.voxel.wasfaadminapp.ui.customers.adapter.TypeListAdapter
import com.voxel.wasfaadminapp.ui.home.model.Cat


class TypeFragment : Fragment() {

    private var _binding: FragmentTypeBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentTypeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        manageType()
        createList()
        handleClick()
    }

    private fun handleClick() {
        binding.imgCart.setOnClickListener {
            findNavController().navigate(R.id.nav_cart)
        }
        binding.imgBack.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.cardAdd.setOnClickListener {
            binding.pageSegmentAdd.lytAddress.visibility = View.VISIBLE
        }
        binding.pageSegmentAdd.imgCloseDate.setOnClickListener {
            binding.pageSegmentAdd.lytAddress.visibility = View.GONE
        }
        binding.pageSegmentAdd.cardAddAddress.setOnClickListener {
            binding.pageSegmentAdd.lytAddress.visibility = View.GONE
        }
        binding.pageSegmentEdit.imgCloseDate.setOnClickListener {
            binding.pageSegmentEdit.lytAddress.visibility = View.GONE
        }
        binding.pageSegmentEdit.cardAddAddress.setOnClickListener {
            binding.pageSegmentEdit.lytAddress.visibility = View.GONE
        }
    }

    private fun manageType() {
        binding.recyclerList.layoutAnimation =
            android.view.animation.AnimationUtils.loadLayoutAnimation(
                context,
                R.anim.layout_animation_fall_down
            )
        binding.recyclerList.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            val catAdapter = TypeListAdapter(createList()) { data, type ->

                if (type == "edit") {
                    binding.pageSegmentEdit.lytAddress.visibility = View.VISIBLE
                } else if (type == "delete") {
                    showDeletePopup()
                }
            }
            adapter = catAdapter
        }
    }
    private fun showDeletePopup() {
        val builder = AlertDialog.Builder(requireContext())
        val customLayout: View = layoutInflater.inflate(R.layout.delete_product_popup, null)
        builder.setView(customLayout)

        val btnCancel = customLayout.findViewById<MaterialCardView>(R.id.card_cancel)
        val btnDelete = customLayout.findViewById<MaterialCardView>(R.id.card_delete)
        lateinit var dialog: AlertDialog
        btnCancel.setOnClickListener {
            dialog?.dismiss()

        }
        btnDelete.setOnClickListener {
            dialog?.dismiss()

        }
        dialog = builder.create()
        dialog.show()
    }

    private fun createList(): ArrayList<Cat> {
        return arrayListOf<Cat>(
            Cat(
                "All Orders",
                R.drawable.dummy_image
            ),
            Cat(
                "Seller Orders",
                R.drawable.dummy_image
            ),
            Cat(
                "Inhouse orders",
                R.drawable.dummy_image
            ),
            Cat(
                "Pending Orders",
                R.drawable.dummy_image
            ),
            Cat(
                "Cancelled Orders",
                R.drawable.dummy_image
            )
        )
    }


}