package com.voxel.wasfaadminapp.ui.home

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.util.TypedValue
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.FragmentHomeBinding
import com.voxel.wasfaadminapp.helper.AppPreferences
import com.voxel.wasfaadminapp.ui.home.adapter.BestSeller2HomeAdapter
import com.voxel.wasfaadminapp.ui.home.adapter.BestSellerHomeAdapter
import com.voxel.wasfaadminapp.ui.home.adapter.DropdownPopupArea
import com.voxel.wasfaadminapp.ui.home.adapter.InfluencerHomeAdapter
import com.voxel.wasfaadminapp.ui.home.adapter.LastSearchHomeAdapter
import com.voxel.wasfaadminapp.ui.home.adapter.NewCustHomeAdapter
import com.voxel.wasfaadminapp.ui.home.adapter.NewVendorHomeAdapter
import com.voxel.wasfaadminapp.ui.home.adapter.RecentOrderHomeAdapter
import com.voxel.wasfaadminapp.ui.home.model.Cat
import com.voxel.wasfaadminapp.ui.main.MainActivity


class HomeFragment : Fragment() {
    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)


        AppPreferences.getInstance(requireContext()).saveAddressId(null)
        AppPreferences.getInstance(requireContext()).saveCustName(null)
        AppPreferences.getInstance(requireContext()).clearAddress()
        val appPreferences = AppPreferences.getInstance(requireContext())
        appPreferences.saveCatImgId(null)
        appPreferences.saveOrderType(null)
        appPreferences.saveOrderID(null)
        appPreferences.saveCatImgName(null)
        appPreferences.saveCatIconImgId(null)
        appPreferences.saveCatIconImgName(null)
        appPreferences.saveImgStatus(null)
        appPreferences.saveCategoryId(null)
        appPreferences.saveCustId(null)
        appPreferences.saveCustName(null)
        appPreferences.clearAddProduct()

        setUpLineChart()
        setUpSalesNoChart()
        setupDocterChart()
        setupSalesHourlyChart()
        setConversionChart()
        setUpBarChart()
        setUpRevenueBar()
        setUpMonthly()
        setUpPieChart()
        setUpMonthly2()
        createList()
        manageInfluencer()
        manageNewVendor()
        manageBestSeller()
        manageRecentOrder()
        manageNewCust()
        manageTopSearch()
        manageLastSearch()
        manageBestSeller2()
        handleClick()

        setBgGradient()
        manageFilter(createList())
    }

    private fun setUpRevenueBar() {
        val entries = listOf(
            BarEntry(0f, floatArrayOf(30f, 20f, 50f)),
            BarEntry(1f, floatArrayOf(40f, 30f, 30f)),
            BarEntry(2f, floatArrayOf(25f, 50f, 25f))
        )

        val dataSet = BarDataSet(entries, "")
        dataSet.colors = listOf(
            Color.parseColor("#d947bf"),
            Color.parseColor("#684440"),
            Color.parseColor("#be83ab")
        )
        dataSet.setDrawValues(false)

        val data = BarData(dataSet)
        data.barWidth = 0.3f
        binding.chartBarRevenue.data = data

        binding.chartBarRevenue.description.isEnabled = false
        binding.chartBarRevenue.setFitBars(true)
        binding.chartBarRevenue.animateY(1000)
        binding.chartBarRevenue.legend.isEnabled = true

        binding.chartBarRevenue.invalidate()
    }

    private fun setUpPieChart() {
        val entries = listOf(
            PieEntry(75f),
            PieEntry(8.33f),
            PieEntry(5.33f),
            PieEntry(3f)
        )

        val dataSet = PieDataSet(entries, "")
        dataSet.colors = listOf(
            Color.parseColor("#d947bf"),
            Color.parseColor("#684440"),
            Color.parseColor("#be83ab"),
            Color.parseColor("#d81f9c")
        )
        dataSet.setDrawValues(false)


        val data = PieData(dataSet)
        binding.pieChart.data = data
        binding.pieChart.description.isEnabled = false
        binding.pieChart.isDrawHoleEnabled = false
        binding.pieChart.setDrawEntryLabels(false)
        binding.pieChart.legend.isEnabled = false
        binding.pieChart.animateY(1000)
    }

    private fun setupSalesHourlyChart() {
        val greenEntries = mutableListOf<Entry>()
        greenEntries.add(Entry(1f, 0f))


        val greenDataSet = LineDataSet(greenEntries, "")
        greenDataSet.color = Color.parseColor("#90CDBB")
        greenDataSet.setDrawValues(false)
        greenDataSet.setCircleColor(Color.parseColor("#90CDBB"))
        greenDataSet.circleRadius = 4f

        val lineData = LineData(greenDataSet)
        binding.chartSalesHourlyBasics.data = lineData


        binding.chartSalesHourlyBasics.description.isEnabled = false
        binding.chartSalesHourlyBasics.axisRight.isEnabled = false
        binding.chartSalesHourlyBasics.xAxis.isEnabled = true


        val leftAxis = binding.chartSalesHourlyBasics.axisLeft
        leftAxis.axisMinimum = 0f
        leftAxis.axisMaximum = 100f
        leftAxis.labelCount = 5
        leftAxis.granularity = 25f


        val xAxis = binding.chartSalesHourlyBasics.xAxis
        xAxis.axisMinimum = 0f
        xAxis.axisMaximum = 6f
        xAxis.labelCount = 7
        xAxis.position = com.github.mikephil.charting.components.XAxis.XAxisPosition.BOTTOM
        xAxis.granularity = 1f
        binding.chartSalesHourlyBasics.legend.isEnabled = false
        binding.chartSalesHourlyBasics.invalidate()
    }

    private fun manageFilter(createList: ArrayList<Cat>) {
        val dropdownPopup =
            DropdownPopupArea(requireContext(), binding.cardFilter, createList) { ->

            }

        binding.cardFilter.setOnClickListener {
            dropdownPopup.show()
        }
    }

    private fun setBgGradient() {


        applyGradientBackground(
            binding.pageDailyOperation.lytTotalSales,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFF875FC0.toInt(), 0xFF5346BA.toInt())
        )

        applyGradientBackground(
            binding.pageDailyOperation.lytTotalOrders,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFF47C5F4.toInt(), 0xFF6791D9.toInt())
        )

        applyGradientBackground(
            binding.pageDailyOperation.lytPendingDelivery,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFFEB4786.toInt(), 0xFFB854A6.toInt())
        )

        applyGradientBackground(
            binding.pageDailyOperation.lytTotalDelivery,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFFFFB72C.toInt(), 0xFFF57F59.toInt())
        )

        applyGradientBackground(
            binding.pageDailyOperation.lytGrossMargin,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFF56FF2C.toInt(), 0xFF1D761F.toInt())
        )

        applyGradientBackground(
            binding.pageDailyOperation.lytNetMargin,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFF00008B.toInt(), 0xFF00008B.toInt())
        )

        applyGradientBackground(
            binding.pageDailyOperation.lytRefundRequest,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFF1E8B89.toInt(), 0xFF1E8B89.toInt())
        )

        applyGradientBackground(
            binding.pageDailyOperation.lytApproveRequest,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFF1E5611.toInt(), 0xFF1E5611.toInt())
        )

        applyGradientBackground(
            binding.pageDailyOperation.lytCancelledOrder,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFFFF4500.toInt(), 0xFFFF4500.toInt())
        )
        applyGradientBackground(
            binding.pageDailyOperation.lytClosedOrder,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFF3D5959.toInt(), 0xFF3D5959.toInt())
        )


        /* data management */

        applyGradientBackground(
            binding.pageDataManagement.lytSellerOne,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFF1B0A48.toInt(), 0xFF1B0A48.toInt())
        )
        binding.pageDataManagement.lytSellerTwo.setBackgroundColor(Color.parseColor("#540327"))
        binding.pageDataManagement.lytInfluencerOne.setBackgroundColor(Color.parseColor("#45139c"))
        binding.pageDataManagement.lytInfluencerTwo.setBackgroundColor(Color.parseColor("#111f1f"))


        binding.pageGeneral.lytTotalSales.setBackgroundColor(Color.parseColor("#ffc107"))

        applyGradientBackground(
            binding.pageGeneral.lytTotalPatient,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFF875FC0.toInt(), 0xFF5346BA.toInt())
        )
        binding.pageGeneral.lytTotalSalesAmount.setBackgroundColor(Color.parseColor("#ff0781"))

        applyGradientBackground(
            binding.pageGeneral.lytTotalVendors,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFF56FF2C.toInt(), 0xFF1D761F.toInt())
        )
        applyGradientBackground(
            binding.pageGeneral.lytTotalOrders,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFF47C5F4.toInt(), 0xFF6791D9.toInt())
        )
        applyGradientBackground(
            binding.pageGeneral.lytOrderAmount,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFFEB4786.toInt(), 0xFFB854A6.toInt())
        )
        applyGradientBackground(
            binding.pageGeneral.lytTotalInfluencer,
            GradientDrawable.Orientation.RIGHT_LEFT,
            intArrayOf(0xFFFFB72C.toInt(), 0xFFF57F59.toInt())
        )

        binding.pageGeneral.lytStockCount.setBackgroundColor(Color.parseColor("#1e8b89"))
        binding.pageGeneral.lytInfluencerOutstanding.setBackgroundColor(Color.parseColor("#dc3545"))
        binding.pageGeneral.lytPaidToInfluencer.setBackgroundColor(Color.parseColor("#0c3b50"))
        binding.pageGeneral.lytSellerOutstanding.setBackgroundColor(Color.parseColor("#007bff"))
        binding.pageGeneral.lytPaidToSeller.setBackgroundColor(Color.parseColor("#ea2cff"))
        binding.pageGeneral.lytGrossMargin.setBackgroundColor(Color.parseColor("#1e5611"))
        binding.pageGeneral.lytNetMargin.setBackgroundColor(Color.parseColor("#00008b"))
        binding.pageGeneral.lytPendingDelivery.setBackgroundColor(Color.parseColor("#ff69b4"))
        binding.pageGeneral.lytSalesReturnRequest.setBackgroundColor(Color.parseColor("#7e2513"))
        binding.pageGeneral.lytApprovedRequest.setBackgroundColor(Color.parseColor("#132d7b"))
        binding.pageGeneral.lytTotalDeliveryCost.setBackgroundColor(Color.parseColor("#0c0d10"))
        binding.pageGeneral.lytDeliveryCount.setBackgroundColor(Color.parseColor("#4f3b3b"))
        binding.pageGeneral.lytTotalCancelledOrders.setBackgroundColor(Color.parseColor("#ff4500"))
        binding.pageGeneral.lytClosedOrder.setBackgroundColor(Color.parseColor("#3d5959"))

    }

   private fun applyGradientBackground(view: View, orientation: GradientDrawable.Orientation, colors: IntArray) {
        val gradientDrawable = GradientDrawable(orientation, colors).apply {
            gradientType = GradientDrawable.LINEAR_GRADIENT
        }
        view.background = gradientDrawable
    }
    private fun setupDocterChart() {

        val greenEntries = mutableListOf<Entry>()
        greenEntries.add(Entry(1f, 0f))


        val greenDataSet = LineDataSet(greenEntries, "")
        greenDataSet.color = Color.parseColor("#90CDBB")
        greenDataSet.setDrawValues(false)
        greenDataSet.setCircleColor(Color.parseColor("#90CDBB"))
        greenDataSet.circleRadius = 4f

        val lineData = LineData(greenDataSet)
        binding.chartDocter.data = lineData


        binding.chartDocter.description.isEnabled = false
        binding.chartDocter.axisRight.isEnabled = false
        binding.chartDocter.xAxis.isEnabled = true


        val leftAxis = binding.chartDocter.axisLeft
        leftAxis.axisMinimum = 0f
        leftAxis.axisMaximum = 100f
        leftAxis.labelCount = 5
        leftAxis.granularity = 25f


        val xAxis = binding.chartDocter.xAxis
        xAxis.axisMinimum = 0f
        xAxis.axisMaximum = 6f
        xAxis.labelCount = 7
        xAxis.position = com.github.mikephil.charting.components.XAxis.XAxisPosition.BOTTOM
        xAxis.granularity = 1f
        binding.chartDocter.legend.isEnabled = false
        binding.chartDocter.invalidate()
    }
    private fun setUpSalesNoChart() {

        val greenEntries = mutableListOf<Entry>()
        greenEntries.add(Entry(1f, 30f))
        greenEntries.add(Entry(2f, 50f))
        greenEntries.add(Entry(3f, 25f))
        greenEntries.add(Entry(4f, 70f))
        greenEntries.add(Entry(5f, 90f))

        val greenDataSet = LineDataSet(greenEntries, "")
        greenDataSet.color = Color.parseColor("#90CDBB")
        greenDataSet.setDrawValues(false)
        greenDataSet.setCircleColor(Color.parseColor("#90CDBB"))
        greenDataSet.circleRadius = 4f

        val lineData = LineData(greenDataSet)
        binding.chartNoOfSales.data = lineData


        binding.chartNoOfSales.description.isEnabled = false
        binding.chartNoOfSales.axisRight.isEnabled = false
        binding.chartNoOfSales.xAxis.isEnabled = true


        val leftAxis = binding.chartNoOfSales.axisLeft
        leftAxis.axisMinimum = 0f
        leftAxis.axisMaximum = 100f
        leftAxis.labelCount = 5
        leftAxis.granularity = 25f


        val xAxis = binding.chartNoOfSales.xAxis
        xAxis.axisMinimum = 0f
        xAxis.axisMaximum = 6f
        xAxis.labelCount = 7
        xAxis.position = com.github.mikephil.charting.components.XAxis.XAxisPosition.BOTTOM
        xAxis.granularity = 1f
        binding.chartNoOfSales.legend.isEnabled = false
        binding.chartNoOfSales.invalidate()
    }

    private fun handleClick() {
        binding.imgHomeMenu.setOnClickListener {
            (activity as MainActivity).toggleMenuOverlay(true)
        }

        binding.cardFilter.setOnClickListener {

        }
        binding.newVendorViewAll.setOnClickListener {
            findNavController().navigate(R.id.nav_seller)
        }
        binding.influencerViewAll.setOnClickListener {
            findNavController().navigate(R.id.nav_influencer)
        }
    }
    private fun manageInfluencer() {
        binding.recyclerInfluencer.apply {
            layoutManager = GridLayoutManager(context, 2)
            val catAdapter = InfluencerHomeAdapter(createList()) { data, position -> }
            adapter = catAdapter
        }
    }
    private fun manageNewVendor() {
        binding.recyclerNewVendor.apply {
            layoutManager = GridLayoutManager(context, 2)
            val catAdapter = NewVendorHomeAdapter(createList()) { data, position -> }
            adapter = catAdapter
        }
    }
    private fun manageLastSearch() {
        binding.recyclerLastSearch.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)

            val catAdapter = LastSearchHomeAdapter(createList()) { data, position ->

            }
            adapter = catAdapter
        }
    }
    private fun manageTopSearch() {
        binding.recyclerTopSearch.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)

            val catAdapter = LastSearchHomeAdapter(createList()) { data, position ->

            }
            adapter = catAdapter
        }
    }
    private fun manageNewCust() {
        binding.recyclerNewCustomers.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            val catAdapter = NewCustHomeAdapter(createList()) { data, position ->

            }
            adapter = catAdapter
        }
    }

    private fun manageRecentOrder() {
        binding.recyclerRecentOrders.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            val catAdapter = RecentOrderHomeAdapter(createList()) { data, position ->

            }
            adapter = catAdapter
        }
    }
    private fun manageBestSeller2() {
        binding.recyclerBestSellingProduct2.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)

            val catAdapter = BestSeller2HomeAdapter(createList()) { data, position ->

            }
            adapter = catAdapter
        }
    }
    private fun manageBestSeller() {
        binding.recyclerBestSellingProduct.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)

            val catAdapter = BestSellerHomeAdapter(createList()) { data, position ->

            }
            adapter = catAdapter
        }
    }

    private fun createList(): ArrayList<Cat> {
        return arrayListOf<Cat>(
            Cat(
                "Today",
                R.drawable.dummy_image
            ),
            Cat(
                "Yesterday",
                R.drawable.dummy_image
            ),
            Cat(
                "Last 7 Days",
                R.drawable.dummy_image
            ),
            Cat(
                "Last 30 Days",
                R.drawable.dummy_image
            ),
            Cat(
                "This Month",
                R.drawable.dummy_image
            ),
            Cat(
                "Last Month",
                R.drawable.dummy_image
            ),
            Cat(
                "Custom Range",
                R.drawable.dummy_image
            )
        )
    }
    private fun setUpBarChart() {

        val blueEntries = mutableListOf<BarEntry>()
        blueEntries.add(BarEntry(1f, 50f))
        blueEntries.add(BarEntry(2.30f, 60f))
        blueEntries.add(BarEntry(4.5f, 90f))

        val greenEntries = mutableListOf<BarEntry>()
        greenEntries.add(BarEntry(1.60f, 70f))
        greenEntries.add(BarEntry(2.85f, 50f))
        greenEntries.add(BarEntry(5.15f, 90f))


        val blueDataSet = BarDataSet(blueEntries, "")
        blueDataSet.color = Color.parseColor("#0966FF")
        blueDataSet.setDrawValues(false)

        val greenDataSet = BarDataSet(greenEntries, "")
        greenDataSet.color = Color.parseColor("#90CDBB")
        greenDataSet.setDrawValues(false)

        val barData = BarData(blueDataSet, greenDataSet)
        barData.barWidth = 0.50f
        binding.barChart.data = barData

        binding.barChart.description.isEnabled = false
        binding.barChart.axisRight.isEnabled = false
        binding.barChart.xAxis.isEnabled = true

        val leftAxis = binding.barChart.axisLeft
        leftAxis.axisMinimum = 0f
        leftAxis.axisMaximum = 100f
        leftAxis.labelCount = 5
        leftAxis.granularity = 25f

        val xAxis = binding.barChart.xAxis
        xAxis.axisMinimum = 0f
        xAxis.axisMaximum = 6f
        xAxis.labelCount = 7
        xAxis.position =
            com.github.mikephil.charting.components.XAxis.XAxisPosition.BOTTOM
        xAxis.granularity = 1f

        binding.barChart.legend.isEnabled = false

        binding.barChart.invalidate()


    }
    private fun setUpMonthly() {

        val blueEntries = mutableListOf<BarEntry>()
        blueEntries.add(BarEntry(1f, 50f))
        blueEntries.add(BarEntry(2.30f, 60f))
        blueEntries.add(BarEntry(4.5f, 90f))

        val greenEntries = mutableListOf<BarEntry>()

        greenEntries.add(BarEntry(2.85f, 50f))
        greenEntries.add(BarEntry(5.15f, 90f))

        val maroonEntries = mutableListOf<BarEntry>()
        maroonEntries.add(BarEntry(1.70f, 80f))


        val blueDataSet = BarDataSet(blueEntries, "")
        blueDataSet.color = Color.parseColor("#0060f0")
        blueDataSet.setDrawValues(false)

        val greenDataSet = BarDataSet(greenEntries, "")
        greenDataSet.color = Color.parseColor("#fa7000")
        greenDataSet.setDrawValues(false)

        val maroonDataSet = BarDataSet(maroonEntries, "")
        maroonDataSet.color = Color.parseColor("#dc3545")
        maroonDataSet.setDrawValues(false)

        val barData = BarData(blueDataSet, greenDataSet,maroonDataSet)
        barData.barWidth = 0.50f
        binding.chartMonthlySales.data = barData

        binding.chartMonthlySales.description.isEnabled = false
        binding.chartMonthlySales.axisRight.isEnabled = false
        binding.chartMonthlySales.xAxis.isEnabled = true

        val leftAxis = binding.chartMonthlySales.axisLeft
        leftAxis.axisMinimum = 0f
        leftAxis.axisMaximum = 100f
        leftAxis.labelCount = 5
        leftAxis.granularity = 25f

        val xAxis = binding.chartMonthlySales.xAxis
        xAxis.axisMinimum = 0f
        xAxis.axisMaximum = 6f
        xAxis.labelCount = 7
        xAxis.position =
            com.github.mikephil.charting.components.XAxis.XAxisPosition.BOTTOM
        xAxis.granularity = 1f

        binding.chartMonthlySales.legend.isEnabled = false

        binding.chartMonthlySales.invalidate()


    }
    private fun setUpMonthly2() {

        val blueEntries = mutableListOf<BarEntry>()
        blueEntries.add(BarEntry(1f, 50f))
        blueEntries.add(BarEntry(2.30f, 60f))
        blueEntries.add(BarEntry(4.5f, 90f))

        val maroonEntries = mutableListOf<BarEntry>()
        maroonEntries.add(BarEntry(1.70f, 80f))


        val blueDataSet = BarDataSet(blueEntries, "")
        blueDataSet.color = Color.parseColor("#0060f0")
        blueDataSet.setDrawValues(false)

        val maroonDataSet = BarDataSet(maroonEntries, "")
        maroonDataSet.color = Color.parseColor("#dc3545")
        maroonDataSet.setDrawValues(false)

        val barData = BarData(blueDataSet,maroonDataSet)
        barData.barWidth = 0.50f
        binding.chartMonthlySales2.data = barData

        binding.chartMonthlySales2.description.isEnabled = false
        binding.chartMonthlySales2.axisRight.isEnabled = false
        binding.chartMonthlySales2.xAxis.isEnabled = true

        val leftAxis = binding.chartMonthlySales2.axisLeft
        leftAxis.axisMinimum = 0f
        leftAxis.axisMaximum = 100f
        leftAxis.labelCount = 5
        leftAxis.granularity = 25f

        val xAxis = binding.chartMonthlySales2.xAxis
        xAxis.axisMinimum = 0f
        xAxis.axisMaximum = 6f
        xAxis.labelCount = 7
        xAxis.position =
            com.github.mikephil.charting.components.XAxis.XAxisPosition.BOTTOM
        xAxis.granularity = 1f

        binding.chartMonthlySales2.legend.isEnabled = false

        binding.chartMonthlySales2.invalidate()


    }
    private fun dpToPx(dp: Float): Float {
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, resources.displayMetrics)
    }

    private fun setUpLineChart() {
        // Data for the first line (blue)
        val blueEntries = mutableListOf<Entry>()
        blueEntries.add(Entry(1f, 20f))
        blueEntries.add(Entry(2f, 40f))
        blueEntries.add(Entry(3f, 10f))
        blueEntries.add(Entry(4f, 60f))
        blueEntries.add(Entry(5f, 80f))

        // Data for the second line (green)
        val greenEntries = mutableListOf<Entry>()
        greenEntries.add(Entry(1f, 30f))
        greenEntries.add(Entry(2f, 50f))
        greenEntries.add(Entry(3f, 25f))
        greenEntries.add(Entry(4f, 70f))
        greenEntries.add(Entry(5f, 90f))


        val blueDataSet = LineDataSet(blueEntries, "")
        blueDataSet.color = Color.parseColor("#0966FF")
        blueDataSet.setDrawValues(false)
        blueDataSet.setCircleColor(Color.parseColor("#0966FF"))
        blueDataSet.circleRadius = 4f


        val greenDataSet = LineDataSet(greenEntries, "")
        greenDataSet.color = Color.parseColor("#90CDBB")
        greenDataSet.setDrawValues(false)
        greenDataSet.setCircleColor(Color.parseColor("#90CDBB"))
        greenDataSet.circleRadius = 4f

        val lineData = LineData(blueDataSet, greenDataSet)
        binding.lineChart.data = lineData


        binding.lineChart.description.isEnabled = false
        binding.lineChart.axisRight.isEnabled = false
        binding.lineChart.xAxis.isEnabled = true


        val leftAxis = binding.lineChart.axisLeft
        leftAxis.axisMinimum = 0f
        leftAxis.axisMaximum = 100f
        leftAxis.labelCount = 5
        leftAxis.granularity = 25f


        val xAxis = binding.lineChart.xAxis
        xAxis.axisMinimum = 0f
        xAxis.axisMaximum = 6f
        xAxis.labelCount = 7
        xAxis.position = com.github.mikephil.charting.components.XAxis.XAxisPosition.BOTTOM
        xAxis.granularity = 1f
        binding.lineChart.legend.isEnabled = false
        binding.lineChart.invalidate()
    }
    private fun setConversionChart() {
        // Data for the first line (blue)
        val blueEntries = mutableListOf<Entry>()
        blueEntries.add(Entry(1f, 20f))
        blueEntries.add(Entry(2f, 40f))
        blueEntries.add(Entry(3f, 10f))
        blueEntries.add(Entry(4f, 60f))
        blueEntries.add(Entry(5f, 80f))

        // Data for the second line (green)
        val greenEntries = mutableListOf<Entry>()
        greenEntries.add(Entry(1f, 30f))
        greenEntries.add(Entry(2f, 50f))
        greenEntries.add(Entry(3f, 25f))
        greenEntries.add(Entry(4f, 70f))
        greenEntries.add(Entry(5f, 90f))


        val blueDataSet = LineDataSet(blueEntries, "")
        blueDataSet.color = Color.parseColor("#0966FF")
        blueDataSet.setDrawValues(false)
        blueDataSet.setCircleColor(Color.parseColor("#0966FF"))
        blueDataSet.circleRadius = 4f


        val greenDataSet = LineDataSet(greenEntries, "")
        greenDataSet.color = Color.parseColor("#90CDBB")
        greenDataSet.setDrawValues(false)
        greenDataSet.setCircleColor(Color.parseColor("#90CDBB"))
        greenDataSet.circleRadius = 4f

        val lineData = LineData(blueDataSet, greenDataSet)
        binding.lineChartConversion.data = lineData

        binding.lineChartConversion.description.isEnabled = false
        binding.lineChartConversion.axisRight.isEnabled = false
        binding.lineChartConversion.xAxis.isEnabled = true


        val leftAxis = binding.lineChartConversion.axisLeft
        leftAxis.axisMinimum = 0f
        leftAxis.axisMaximum = 100f
        leftAxis.labelCount = 5
        leftAxis.granularity = 25f


        val xAxis = binding.lineChartConversion.xAxis
        xAxis.axisMinimum = 0f
        xAxis.axisMaximum = 6f
        xAxis.labelCount = 7
        xAxis.position = com.github.mikephil.charting.components.XAxis.XAxisPosition.BOTTOM
        xAxis.granularity = 1f
        binding.lineChartConversion.legend.isEnabled = false
        binding.lineChartConversion.invalidate()
    }
}