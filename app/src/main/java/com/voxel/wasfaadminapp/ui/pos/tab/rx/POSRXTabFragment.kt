package com.voxel.wasfaadminapp.ui.pos.tab.rx

import android.app.DatePickerDialog
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextWatcher
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.card.MaterialCardView
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.FragmentPOSRXTabBinding
import com.voxel.wasfaadminapp.helper.AppPreferences
import com.voxel.wasfaadminapp.network.ApiService
import com.voxel.wasfaadminapp.network.response.Orders
import com.voxel.wasfaadminapp.network.response.UserDetails
import com.voxel.wasfaadminapp.ui.pos.adapter.POSRXListAdapter
import com.voxel.wasfaadminapp.ui.pos.tab.adapter.OrderListAdapter
import com.voxel.wasfaadminapp.viewmodel.HomeViewModel
import com.voxel.wasfaadminapp.viewmodel.HomeViewModelFactory
import java.util.Calendar

class POSRXTabFragment : Fragment() {
    private var _binding: FragmentPOSRXTabBinding? = null
    private val binding get() = _binding!!
    private lateinit var viewModel: HomeViewModel
    private lateinit var productAdapter: POSRXListAdapter
    private var scrollListener: ViewTreeObserver.OnScrollChangedListener? = null
    private var searchHandler: Handler = Handler(Looper.getMainLooper())
    private var searchRunnable: Runnable? = null
    private var selectedInfluencerId: String = ""
    var searchValue: String = ""
    var patientId = ""
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentPOSRXTabBinding.inflate(inflater, container, false)
        return binding.root

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        handleClick()
        setViewModel()
        handleEditTexts()
    }

    private fun callOrderListApi() {
        binding.cardNext.visibility = View.VISIBLE
        binding.progressBarOrder.visibility = View.VISIBLE
        val request = ApiService.OrderListRequest(
            area = "",
            per_page = "3",
            page_no = "1",
            search = "",
            date = "",
            order_type = "",
            collected_by_seller = "",
            zones = "",
            payment_change = "",
            payment_method = "",
            payment_status = "",
            collected_by = "",
            delivery_boy = "",
            delivery_status = "",
            userId = patientId,
            IsPrescription = "1"
        )
        viewModel.getOrderList(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
    }

    private fun handleEditTexts() {
        binding.editPhone.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtPhoneEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
                val phoneNumber = s.toString()
                if (phoneNumber.length == 8) {
                    checkCustomerExistCall(phoneNumber)
                }
            }
        })
        binding.editCivilId.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtCivilEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.editFullName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtNameEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.editEmail.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtEmailEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
    }

    private fun checkCustomerExistCall(phoneNumber: String) {

        binding.progressBar.visibility = View.VISIBLE
        val appPreferences = AppPreferences.getInstance(requireContext())
        val request = ApiService.CheckPatientRequest(
            keyword = "+965$phoneNumber"
        )
        viewModel.checkCustomerExist(appPreferences.getToken().toString(),request)
        viewModel.customerListData.observe(viewLifecycleOwner) { data ->
            binding.progressBar.visibility = View.GONE
            manageCustomer(data?.users)
        }

    }



    private fun setViewModel() {
        val appPreferences = AppPreferences.getInstance(requireContext())
        viewModel = ViewModelProvider(
            this,
            HomeViewModelFactory(requireContext())
        ).get(HomeViewModel::class.java)

        viewLifecycleOwner.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                _binding = null
            }
        })
        viewModel.addUnitFailStatus.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = View.GONE
            binding.progressBarOrder.visibility = View.GONE

        }
        viewModel.deleteCartShopStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.VISIBLE
            println("me =--------------")
            viewModel.getCart(appPreferences.getToken().toString())

        }
        viewModel.cartUpdateStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            viewModel.getCart(appPreferences.getToken().toString())

        }
        viewModel.orderListData.observe(viewLifecycleOwner) { data ->

            binding.progressBarOrder.visibility = View.GONE
            manageOrderList(data?.orders)

        }
        viewModel.loadingState.observe(viewLifecycleOwner) { isLoading ->

            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.INVISIBLE

        }
        viewModel.showAlertEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            showAlertCustom(message)

        }
        viewModel.createdStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            if (message == "Patient saved successfully"){
                binding.cardCustBox.strokeColor = Color.parseColor("#00BCD7")
                binding.rltBtn.visibility = View.VISIBLE
                binding.cardNext.visibility = View.VISIBLE
                binding.cardCreate.visibility = View.GONE
                binding.cardUpdate.visibility = View.VISIBLE

            }else{
                binding.cardNext.visibility = View.GONE
                binding.rltBtn.visibility = View.VISIBLE
            }
           Toast.makeText(requireContext(),message,Toast.LENGTH_LONG).show()
            //56567876

        }
        viewModel.patientIdStatus.observe(viewLifecycleOwner) { patient_id ->
            binding.progressBar.visibility = View.GONE
           patientId = patient_id
            callOrderListApi()

        }
        viewModel.addCartShopStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            Toast.makeText(requireContext(),message,Toast.LENGTH_LONG).show()
            viewModel.getCart(appPreferences.getToken().toString())

        }
        viewModel.productEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE



        }
        viewModel.influencerList.observe(viewLifecycleOwner) { data ->
            //manageInfluencer(data)
        }
        viewModel.cartCount.observe(viewLifecycleOwner) { data ->

            binding.txtCartCount.text = data?.cartCount
            AppPreferences.getInstance(requireContext()).saveCartCount(data?.cartCount)

            if (data?.cartCount == "0"){
                binding.rltCartCount.visibility = View.INVISIBLE
            }else{
                binding.rltCartCount.visibility = View.VISIBLE
            }
        }

        viewModel.getCartCount(appPreferences.getToken().toString())
        viewModel.getInfluencerList(appPreferences.getToken().toString())

    }
    private fun manageOrderList(data: List<Orders>?){
        if (data.isNullOrEmpty()) {
            binding.txtNoDataOrder.visibility = View.VISIBLE
            binding.cardViewAllOrder.visibility = View.GONE
        } else {
            binding.cardViewAllOrder.visibility = View.VISIBLE
            binding.txtNoDataOrder.visibility = View.GONE
            binding.recyclerAllOrders.apply {
                layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

                val catAdapter = OrderListAdapter(data!!) { data, position ->

                }
                adapter = catAdapter
            }
        }
    }

    private fun manageCustomer(data: List<UserDetails>?) {


        if (data.isNullOrEmpty()){
            binding.cardCustBox.strokeColor = Color.parseColor("#A61C5C")
            binding.rltBtn.visibility = View.VISIBLE
            binding.cardCreate.visibility = View.VISIBLE
            binding.cardUpdate.visibility = View.GONE
            binding.cardNext.visibility = View.GONE

            binding.editCivilId.setText(null)
            binding.editAltPhone.setText(null)
            binding.editFullName.setText(null)
            binding.editEmail.setText(null)
            binding.txtDob.setText(null)
        }else{
            binding.cardCustBox.strokeColor = Color.parseColor("#00BCD7")
            patientId = data?.get(0)?.id.toString()
            callOrderListApi()
            binding.rltBtn.visibility = View.VISIBLE
            binding.cardCreate.visibility = View.GONE
            binding.cardUpdate.visibility = View.VISIBLE

            binding.editCivilId.setText(data.get(0)?.civilId)
            binding.editAltPhone.setText(data.get(0)?.alternateNumber)
            binding.editFullName.setText(data.get(0)?.name)
            binding.editEmail.setText(data.get(0)?.email)
            binding.txtDob.setText(data.get(0)?.dob)

        }
    }

    fun showAlertCustom(message: String) {
        val builder = AlertDialog.Builder(requireContext())


        // set the custom layout
        val customLayout: View = layoutInflater.inflate(R.layout.validation_alert, null)
        builder.setView(customLayout)
        val text_validation = customLayout.findViewById<TextView>(R.id.text_validation)
        text_validation.text = message
        val continueShoppingButton = customLayout.findViewById<MaterialCardView>(R.id.view_cart)
        lateinit var dialog: AlertDialog
        continueShoppingButton.setOnClickListener {
            if (message == "Internal Server Error"){
                findNavController().popBackStack()
                dialog?.dismiss()
            }else{
                dialog?.dismiss()
            }

        }

        dialog = builder.create()
        dialog.show()
    }





    private fun handleClick() {
        binding.cardNext.setOnClickListener {
            AppPreferences.getInstance(requireContext()).savePatientId(patientId)
            findNavController().navigate(R.id.nav_tab_pos_rx_2_product)
        }
        binding.cardViewAllOrder.setOnClickListener {
            findNavController().navigate(R.id.nav_sale_details)
        }

        binding.imgBack.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.imgCart.setOnClickListener {
            findNavController().navigate(R.id.nav_cart)
        }

        binding.cardCreate.setOnClickListener {
            validateFields("create")
        }

        binding.cardUpdate.setOnClickListener {
            validateFields("update")
        }
        binding.lytCalendar.setOnClickListener {
            showDatePicker()
        }
    }

    private fun showDatePicker() {
        val calendar = Calendar.getInstance()
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        val datePickerDialog = DatePickerDialog(requireContext(), { _, selectedYear, selectedMonth, selectedDay ->
            val formattedDate = String.format("%02d-%02d-%04d", selectedDay, selectedMonth + 1, selectedYear)
            binding.txtDob.text = formattedDate
            binding.txtDobEmpty.visibility = View.GONE
        }, year, month, day)

        datePickerDialog.show()
    }

    private fun validateFields(type: String) {
        if (binding.editPhone.text.isEmpty()){
            binding.txtPhoneEmpty.visibility = View.VISIBLE
        }else if (binding.editCivilId.text.isEmpty()){
            binding.txtCivilEmpty.visibility = View.VISIBLE
        }else if (binding.editFullName.text.isEmpty()){
            binding.txtNameEmpty.visibility = View.VISIBLE
        }else if (binding.editEmail.text.isEmpty()){
            binding.txtEmailEmpty.visibility = View.VISIBLE
        }else if (binding.txtDob.text.isEmpty()){
            binding.txtDobEmpty.visibility = View.VISIBLE
        }else{

            if (type == "create"){
                callCreatePatientApi()
            }else{
                callUpdatePatientApi()
            }

        }
    }
    private fun callUpdatePatientApi() {

        binding.progressBar.visibility = View.VISIBLE
        val request = ApiService.CreatePatientRequest(
            email = binding.editEmail.text.toString(),
            phone ="+965"+ binding.editPhone.text.toString(),
            name = binding.editFullName.text.toString(),
            dob = binding.txtDob.text.toString(),
            altMobileNo = binding.editAltPhone.text.toString(),
            civilId = binding.editCivilId.text.toString(),
            id = patientId
        )
        viewModel.createPatient(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
    }
    private fun callCreatePatientApi() {

        binding.progressBar.visibility = View.VISIBLE
        val request = ApiService.CreatePatientRequest(
            email = binding.editEmail.text.toString(),
            phone = "+965"+binding.editPhone.text.toString(),
            name = binding.editFullName.text.toString(),
            dob = binding.txtDob.text.toString(),
            altMobileNo = binding.editAltPhone.text.toString(),
            civilId = binding.editCivilId.text.toString(),
            id = ""
        )
        viewModel.createPatient(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
    }


}