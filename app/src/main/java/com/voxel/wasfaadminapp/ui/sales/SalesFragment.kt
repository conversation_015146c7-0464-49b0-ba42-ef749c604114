package com.voxel.wasfaadminapp.ui.sales

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.FragmentProductBinding
import com.voxel.wasfaadminapp.databinding.FragmentSalesBinding
import com.voxel.wasfaadminapp.helper.AppPreferences
import com.voxel.wasfaadminapp.ui.home.model.Cat
import com.voxel.wasfaadminapp.ui.products.adapter.ProductListAdapter
import com.voxel.wasfaadminapp.ui.sales.adapter.SalesListAdapter

class SalesFragment : Fragment() {
    private var _binding: FragmentSalesBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentSalesBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        manageSales()
        createList()
        handleClick()
    }

    private fun handleClick() {
        binding.imgCart.setOnClickListener {
            findNavController().navigate(R.id.nav_cart)
        }
        binding.imgBack.setOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun manageSales() {
        binding.recyclerSales.layoutAnimation =
            android.view.animation.AnimationUtils.loadLayoutAnimation(
                context,
                R.anim.layout_animation_fall_down
            )
        binding.recyclerSales.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            val catAdapter = SalesListAdapter(createList()) { data, position ->
                AppPreferences.getInstance(requireContext()).saveOrderType(data?.name)
                findNavController().navigate(R.id.nav_sale_details)
            }
            adapter = catAdapter
        }
    }

    private fun createList(): ArrayList<Cat> {
        return arrayListOf<Cat>(
            Cat(
                "All Orders",
                R.drawable.dummy_image
            ),
            Cat(
                "Pending Orders",
                R.drawable.dummy_image
            ),
            Cat(
                "Cancelled Orders",
                R.drawable.dummy_image
            )
        )
    }
}