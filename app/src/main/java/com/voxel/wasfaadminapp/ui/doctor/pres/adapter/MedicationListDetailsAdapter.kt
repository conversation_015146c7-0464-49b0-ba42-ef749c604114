package com.voxel.wasfaadminapp.ui.doctor.pres.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.voxel.wasfaadminapp.databinding.ItemMedicationPreviewBinding
import com.voxel.wasfaadminapp.network.response.CartItem
import com.voxel.wasfaadminapp.network.response.Med

class MedicationListDetailsAdapter(
    private val CatList: List<Med>?,
    private val listener: (Med, String) -> Unit
) :
    RecyclerView.Adapter<MedicationListDetailsAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = ItemMedicationPreviewBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindItem(CatList!![position])

        holder.itemView.setOnClickListener{
            listener(CatList!![position],"view")
        }

    }

    override fun getItemCount(): Int {
        return CatList!!.size
    }

    class ViewHolder(var itemBinding: ItemMedicationPreviewBinding) :
        RecyclerView.ViewHolder(itemBinding.root) {
        fun bindItem(data: Med) {
            Glide.with(itemBinding.root.context)
                .load(data?.productThumbnailImage)
                .into(itemBinding.imgProduct)
            itemBinding.txtProductName.text = data?.productName
            itemBinding.txtPrize.text = data?.unitPrice
            itemBinding.txtDose.text = formatText(data?.dose, data?.doseday, data?.dose_time)
            itemBinding.txtCourse.text = formatText(data?.course_day, data?.course_duration)
        }

        private fun formatText(vararg parts: String?): String {
            return parts
                .filter { !it.isNullOrBlank() && it.lowercase() != "select" }
                .joinToString(" , ")
        }
    }
}