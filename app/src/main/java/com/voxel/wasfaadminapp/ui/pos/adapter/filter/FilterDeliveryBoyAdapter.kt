package com.voxel.wasfaadminapp.ui.pos.adapter.filter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.voxel.wasfaadminapp.databinding.ItemFilterDeliveryStaffBinding
import com.voxel.wasfaadminapp.network.response.DeliveryBoyResponse
import com.voxel.wasfaadminapp.network.response.MedicalRepListResponse
import com.voxel.wasfaadminapp.ui.home.model.Cat

class FilterDeliveryBoyAdapter(
    private val CatList: List<DeliveryBoyResponse>?,
    private val listener: (DeliveryBoyResponse, Int) -> Unit
) :
    RecyclerView.Adapter<FilterDeliveryBoyAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = ItemFilterDeliveryStaffBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindItem(CatList!![position])

        holder.itemView.setOnClickListener {
            listener(CatList!![position], position)
        }
    }

    override fun getItemCount(): Int {
        return CatList!!.size
    }

    class ViewHolder(var itemBinding: ItemFilterDeliveryStaffBinding) :
        RecyclerView.ViewHolder(itemBinding.root) {
        fun bindItem(data: DeliveryBoyResponse) {
            itemBinding.itemName.text = data?.name
        }
    }
}