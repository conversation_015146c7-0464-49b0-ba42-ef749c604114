package com.voxel.wasfaadminapp.ui.pos.tab.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.TabPosRxBinding
import com.voxel.wasfaadminapp.network.response.OrderListResponse
import com.voxel.wasfaadminapp.network.response.Orders

class OrderListAdapter(
    private val CatList: List<Orders>,
    private val listener: (Orders, Int) -> Unit
) :
    RecyclerView.Adapter<OrderListAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = TabPosRxBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindItem(CatList!![position])

        holder.itemView.setOnClickListener{
            listener(CatList!![position],position)
        }
    }

    override fun getItemCount(): Int {
        return CatList!!.size
    }

    class ViewHolder(var itemBinding: TabPosRxBinding) :
        RecyclerView.ViewHolder(itemBinding.root) {
        fun bindItem(data: Orders) {
            Glide.with(itemBinding.root.context)
                .load(R.drawable.wasfa_logo)
                .into(itemBinding.imgOrder)

            itemBinding.txtOrderPrize.text = "KD "+data?.orderAmount
            itemBinding.txtOrderQuantity.text = "Quantity: "+data?.productCount
            itemBinding.txtOrderId.text = "ORDER ID: "+data?.orderCode


        }
    }
}