package com.voxel.wasfaadminapp.ui.pos.tab.rx

import android.app.DatePickerDialog
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextWatcher
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.card.MaterialCardView
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.FragmentPOSRX2TabBinding
import com.voxel.wasfaadminapp.databinding.FragmentPOSRXTabBinding
import com.voxel.wasfaadminapp.helper.AppPreferences
import com.voxel.wasfaadminapp.network.ApiService
import com.voxel.wasfaadminapp.network.response.CartItem
import com.voxel.wasfaadminapp.network.response.CustomerListResponse
import com.voxel.wasfaadminapp.network.response.InfluencerListResponse
import com.voxel.wasfaadminapp.network.response.Orders
import com.voxel.wasfaadminapp.network.response.ProductListResponse
import com.voxel.wasfaadminapp.ui.cart.adapter.CartRXAdapter
import com.voxel.wasfaadminapp.ui.main.MainActivity
import com.voxel.wasfaadminapp.ui.pos.adapter.DropdownPopupInfluencer
import com.voxel.wasfaadminapp.ui.pos.adapter.POSRXListAdapter
import com.voxel.wasfaadminapp.ui.pos.tab.adapter.OrderListAdapter
import com.voxel.wasfaadminapp.viewmodel.HomeViewModel
import com.voxel.wasfaadminapp.viewmodel.HomeViewModelFactory
import java.util.Calendar

class POSRX2TabFragment : Fragment() {
    private var _binding: FragmentPOSRX2TabBinding? = null
    private val binding get() = _binding!!
    private lateinit var viewModel: HomeViewModel
    private lateinit var productAdapter: POSRXListAdapter
    private var scrollListener: ViewTreeObserver.OnScrollChangedListener? = null
    private var searchHandler: Handler = Handler(Looper.getMainLooper())
    private var searchRunnable: Runnable? = null
    private var selectedInfluencerId: String = ""
    var searchValue: String = ""
    var patientId = ""
    private var isLoading = false
    private val visibleThreshold = 5
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentPOSRX2TabBinding.inflate(inflater, container, false)
        return binding.root

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)


        patientId = AppPreferences.getInstance(requireContext()).getPatientId().toString()
        handleClick()

        setUpRecyclerView()
        setViewModel()
        setUpPagination()
        callProductAPI("first")
        manageSearch()
        callCartApi()
    }

    private fun callCartApi() {
        binding.progressBarCart.visibility = View.VISIBLE
        viewModel.getCart(AppPreferences.getInstance(requireContext()).getToken().toString())
    }


//    private fun setUpPagination() {
//        binding.recyclerProducts.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
//                super.onScrolled(recyclerView, dx, dy)
//
//                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
//                val visibleItemCount = layoutManager.childCount
//                val totalItemCount = layoutManager.itemCount
//                val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()
//
//                if ( !viewModel.isLastPage()) {
//                    if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount
//                        && firstVisibleItemPosition >= 0
//                    ) {
//                        // Reached end — load next page
//                        viewModel.currentPage++
//                        viewModel.loadNextPage(
//                            "", "", searchValue, selectedInfluencerId, "", "", "", ""
//                        )
//                        binding.progressBarSmall.visibility = View.VISIBLE
//                    }
//                }
//            }
//        })
//    }



    private fun setUpPagination() {
        binding.recyclerProducts.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)

                val layoutManager = recyclerView.layoutManager as LinearLayoutManager
                val totalItemCount = layoutManager.itemCount
                val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()

                if (!viewModel.isLastPage() && !isLoading &&
                    totalItemCount <= lastVisibleItemPosition + visibleThreshold) {

                    isLoading = true
                    viewModel.currentPage++
                    binding.progressBarSmall.visibility = View.VISIBLE
                    viewModel.loadNextPage(
                        "", "", searchValue, selectedInfluencerId, "", "", "", ""
                    )
                }
            }
        })
    }

    private fun setUpRecyclerView() {
        productAdapter = POSRXListAdapter(
            mutableListOf()
        ) { product, type ->

            if (type == "add"){

                if (patientId == ""){
                    showAlertCustom("Please Add Patient")
                }else{
                    binding.progressBar.visibility = View.VISIBLE
                    val request = ApiService.AddCartRXRequest(
                        productId = product?.id.toString(),
                        quantity = "1",
                        patientId = patientId,
                        dose_day = "",
                        description = "",
                        dose = "",
                        course_day = "",
                        dose_time = "",
                        course_duration = ""
                    )
                    viewModel.addCartRX(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
                }

            }else{
                AppPreferences.getInstance(requireContext()).saveProductId(product?.id.toString())
                findNavController().navigate(R.id.nav_pos_shop_details)
            }

        }

        binding.recyclerProducts.apply {
            layoutManager = GridLayoutManager(requireContext(), 2)
            adapter = productAdapter
        }
    }

    private fun setViewModel() {
        val appPreferences = AppPreferences.getInstance(requireContext())
        viewModel = ViewModelProvider(
            this,
            HomeViewModelFactory(requireContext())
        ).get(HomeViewModel::class.java)

        viewLifecycleOwner.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                _binding = null
            }
        })
        viewModel.addUnitFailStatus.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = View.GONE

        }
        viewModel.cartList.observe(viewLifecycleOwner) { data ->
            binding.progressBarCart.visibility = View.GONE

            manageCart(data?.cartItems)
            binding.txtCartCount.text = data?.cartItems?.size.toString()

        }
        viewModel.deleteCartShopStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.VISIBLE
            println("me =--------------")
            viewModel.getCart(appPreferences.getToken().toString())

        }
        viewModel.cartUpdateStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            viewModel.getCart(appPreferences.getToken().toString())

        }

        viewModel.loadingState.observe(viewLifecycleOwner) { isLoading ->

            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.INVISIBLE

        }
        viewModel.showAlertEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            showAlertCustom(message)

        }


        viewModel.addCartShopStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            Toast.makeText(requireContext(),message,Toast.LENGTH_LONG).show()
            viewModel.getCart(appPreferences.getToken().toString())

        }
        viewModel.productEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            isLoading = false
            binding.pageShimmer.shimmer.visibility = View.GONE
            binding.pageShimmer.shimmer.stopShimmer()

        }
        viewModel.productListData.observe(viewLifecycleOwner) { data ->

            binding.progressBar.visibility = View.GONE
            isLoading = false
            binding.progressBarSmall.visibility = View.GONE
            try {
                val totalPages = data?.totalPages
                if (!totalPages.isNullOrEmpty()) {
                    viewModel.totalPageCount = totalPages.toInt()
                } else {
                    // Handle the case where totalPages is empty or null
                }
            } catch (e: NumberFormatException) {
                // Handle the exception if totalPages is still an invalid format
            }
            if (viewModel.currentPage == 1) {

                manageProducts(data)
            } else {
                productAdapter.addProducts(data?.products!!)
            }
        }
        viewModel.influencerList.observe(viewLifecycleOwner) { data ->
            manageInfluencer(data)
        }
        viewModel.cartCount.observe(viewLifecycleOwner) { data ->

            binding.txtCartCount.text = data?.cartCount
            AppPreferences.getInstance(requireContext()).saveCartCount(data?.cartCount)

            if (data?.cartCount == "0"){
                binding.rltCartCount.visibility = View.INVISIBLE
            }else{
                binding.rltCartCount.visibility = View.VISIBLE
            }
        }

        viewModel.getCartCount(appPreferences.getToken().toString())
        viewModel.getInfluencerList(appPreferences.getToken().toString())

    }
    private fun manageCart(cartItems: List<CartItem>?) {

        if (cartItems.isNullOrEmpty()) {
            binding.txtNoDataCart.visibility = View.VISIBLE
            binding.cardProceed.visibility = View.GONE
            binding.recyclerCart.visibility = View.GONE
        } else {
            binding.txtNoDataCart.visibility = View.GONE
            binding.cardProceed.visibility = View.VISIBLE
            binding.recyclerCart.visibility = View.VISIBLE
            binding.recyclerCart.apply {
                layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
                val catAdapter = CartRXAdapter(
                    cartItems,
                    { cart, count ->  //decrement
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = count,
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = cart?.description.toString(),
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: "",
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, count -> //increment
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = count,
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = cart?.description.toString(),
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: "",
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, id -> // delete
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.CartRemoveRequest(
                            id = id
                        )
                        viewModel.deleteCartShop(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, doseValue -> //dose

                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = doseValue,
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = cart?.description ?: "",
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: ""
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, doseTime -> //dose time
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = cart?.dose ?: "",
                            dose_time = doseTime,
                            course_day = cart?.course_day ?: "",
                            description = cart?.description ?: "",
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: ""
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, dayValue -> //dose day
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = cart?.description ?: "",
                            course_duration = cart?.course_duration ?: "",
                            dose_day = dayValue
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, courseDurationValue -> //course duration
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = cart?.description ?: "",
                            course_duration = courseDurationValue,
                            dose_day = cart?.doseday ?: "",
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, courseDayValue -> //course day
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = courseDayValue,
                            description = cart?.description ?: "",
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: "",
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, notesValue -> //notes
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = notesValue,
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: "",
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { Cat, position ->

                    },
                )
                adapter = catAdapter
            }
        }

    }
    fun showAlertCustom(message: String) {
        val builder = AlertDialog.Builder(requireContext())


        // set the custom layout
        val customLayout: View = layoutInflater.inflate(R.layout.validation_alert, null)
        builder.setView(customLayout)
        val text_validation = customLayout.findViewById<TextView>(R.id.text_validation)
        text_validation.text = message
        val continueShoppingButton = customLayout.findViewById<MaterialCardView>(R.id.view_cart)
        lateinit var dialog: AlertDialog
        continueShoppingButton.setOnClickListener {
            if (message == "Internal Server Error"){
                findNavController().popBackStack()
                dialog?.dismiss()
            }else{
                dialog?.dismiss()
            }

        }

        dialog = builder.create()
        dialog.show()
    }

    private fun manageProducts(data: ProductListResponse?) {

        binding.recyclerProducts.visibility = View.VISIBLE
        productAdapter.setProducts(data?.products?.toMutableList() ?: mutableListOf())
    }

    private fun manageInfluencer(data: List<InfluencerListResponse>?) {
        val dropdownPopup =
            DropdownPopupInfluencer(
                requireContext(),
                binding.cardInfluencer,
                data
            ) { selectedItem, selectedId, data ->
                binding.txtInfluencerProduct.text = selectedItem
                selectedInfluencerId = data?.id.toString()
                callProductAPI("")
            }

        binding.cardInfluencer.setOnClickListener {
            dropdownPopup.show()
        }
    }

    private fun callProductAPI(status: String) {
        binding.pageShimmer.shimmer.visibility = View.VISIBLE
        binding.pageShimmer.shimmer.startShimmer()

        binding.recyclerProducts.visibility = View.GONE
        binding.txtNoData.visibility = View.GONE
        viewModel.currentPage = 1
        viewModel.clearProductListData()
        val appPreferences = AppPreferences.getInstance(requireContext())
        val request = ApiService.ProductRequest(
            page_no = "1",
            per_page = "4",
            category = "",
            brand = "",
            sku = "",
            seller = "",
            medical_rep_id = "",
            keyword = searchValue,
            influencer_id = "",
            isFavourite = "0",
            listFrom = ""
        )
        binding.progressBar.visibility = View.VISIBLE

        viewModel.getProductList(appPreferences.getToken().toString(), request)
    }

    private fun manageSearch() {
        binding.editSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(
                s: CharSequence?,
                start: Int,
                count: Int,
                after: Int
            ) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable?) {
                searchRunnable?.let { searchHandler.removeCallbacks(it) }
                searchRunnable = Runnable {
                    searchValue = s.toString()
                    callProductAPI("")
                }
                searchHandler.postDelayed(searchRunnable!!, 500)
            }
        })
    }

    private fun handleClick() {
        binding.cardProceed.setOnClickListener {
                findNavController().navigate(R.id.nav_pos_rx_cart_summery)
        }
        binding.imgBack.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.imgCart.setOnClickListener {
            findNavController().navigate(R.id.nav_cart)
        }
    }
}