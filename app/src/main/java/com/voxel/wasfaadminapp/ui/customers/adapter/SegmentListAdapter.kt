package com.voxel.wasfaadminapp.ui.customers.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.voxel.wasfaadminapp.databinding.ItemCustomersBinding
import com.voxel.wasfaadminapp.databinding.ItemInfluencerListBinding
import com.voxel.wasfaadminapp.databinding.ItemPosListBinding
import com.voxel.wasfaadminapp.databinding.ItemProductListBinding
import com.voxel.wasfaadminapp.databinding.ItemSalesListBinding
import com.voxel.wasfaadminapp.databinding.ItemSegmentListBinding
import com.voxel.wasfaadminapp.databinding.ItemSellerListBinding
import com.voxel.wasfaadminapp.ui.home.model.Cat

class SegmentListAdapter(
    private val CatList: List<Cat>,
    private val listener: (Cat, String) -> Unit
) :
    RecyclerView.Adapter<SegmentListAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = ItemSegmentListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindItem(CatList!![position])

        holder.itemView.setOnClickListener{
            listener(CatList!![position],"view")
        }

        holder.itemBinding.btnDelete.setOnClickListener {
            listener(CatList!![position],"delete")
        }
        holder.itemBinding.btnEdit.setOnClickListener {
            listener(CatList!![position],"edit")
        }
    }

    override fun getItemCount(): Int {
        return CatList!!.size
    }

    class ViewHolder(var itemBinding: ItemSegmentListBinding) :
        RecyclerView.ViewHolder(itemBinding.root) {
        fun bindItem(data: Cat) {

        }
    }
}