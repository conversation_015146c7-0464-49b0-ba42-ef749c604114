package com.voxel.wasfaadminapp.ui.pos.rx

import android.app.DatePickerDialog
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextWatcher
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.widget.NestedScrollView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.google.android.material.card.MaterialCardView
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.FragmentPOSRXBinding
import com.voxel.wasfaadminapp.helper.AppPreferences
import com.voxel.wasfaadminapp.network.ApiService
import com.voxel.wasfaadminapp.network.response.InfluencerListResponse
import com.voxel.wasfaadminapp.network.response.ProductListResponse
import com.voxel.wasfaadminapp.network.response.UserDetails
import com.voxel.wasfaadminapp.ui.pos.adapter.DropdownPopupInfluencer
import com.voxel.wasfaadminapp.ui.pos.adapter.POSRXListAdapter
import com.voxel.wasfaadminapp.viewmodel.HomeViewModel
import com.voxel.wasfaadminapp.viewmodel.HomeViewModelFactory
import java.util.Calendar

class POSRXFragment : Fragment() {
    private var _binding: FragmentPOSRXBinding? = null
    private val binding get() = _binding!!
    private lateinit var viewModel: HomeViewModel
    private lateinit var productAdapter: POSRXListAdapter
    private var scrollListener: ViewTreeObserver.OnScrollChangedListener? = null
    private var searchHandler: Handler = Handler(Looper.getMainLooper())
    private var searchRunnable: Runnable? = null
    private var selectedInfluencerId: String = ""
    var searchValue: String = ""
    var patientId = ""
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentPOSRXBinding.inflate(inflater, container, false)
        return binding.root

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        handleClick()

        setUpRecyclerView()
        setViewModel()
        setUpPagination()
        callProductAPI("first")
        manageSearch()
        
        
        handleEditTexts()
    }

    private fun handleEditTexts() {
        binding.editPhone.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtPhoneEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
                val phoneNumber = s.toString()
                if (phoneNumber.length == 8) {
                    checkCustomerExistCall(phoneNumber)
                }
            }
        })
        binding.editCivilId.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtCivilEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.editFullName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtNameEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.editEmail.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtEmailEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
    }

    private fun checkCustomerExistCall(phoneNumber: String) {

        binding.progressBar.visibility = View.VISIBLE
        val appPreferences = AppPreferences.getInstance(requireContext())
        val request = ApiService.CheckPatientRequest(
            keyword = "+965$phoneNumber"
        )



        viewModel.checkCustomerExist(appPreferences.getToken().toString(),request)
        viewModel.customerListData.observe(viewLifecycleOwner) { data ->
            binding.progressBar.visibility = View.GONE
            manageCustomer(data?.users)

        }
    }

    private fun setUpPagination() {
        binding.recyclerProduct.isNestedScrollingEnabled = false

        binding.nestedScrollView.setOnScrollChangeListener { v: NestedScrollView, _, scrollY, _, oldScrollY ->
            if (v.getChildAt(v.childCount - 1) != null) {
                val lastChild = v.getChildAt(v.childCount - 1)
                val diff = lastChild.bottom - (v.height + scrollY)

                if (diff <= 200 && scrollY > oldScrollY) { // Scroll reached bottom (with 200px buffer)
                    if (!viewModel.isLastPage()) {
                        viewModel.currentPage++

                        viewModel.loadNextPage(
                            "",
                            "",
                             searchValue,
                            selectedInfluencerId,
                            "",
                            "",
                            "",
                            ""
                        )
                        binding.progressBarSmall.visibility = View.VISIBLE
                    }
                }
            }
        }
    }


    private fun setUpRecyclerView() {
        productAdapter = POSRXListAdapter(
            mutableListOf()
        ) { product, type ->

            if (type == "add"){

                if (patientId == ""){
                    showAlertCustom("Please Add Patient")
                }else{
                    binding.progressBar.visibility = View.VISIBLE
                    val request = ApiService.AddCartRXRequest(
                        productId = product?.id.toString(),
                        quantity = "1",
                        patientId = patientId,
                        dose_day = "",
                        description = "",
                        dose = "",
                        course_day = "",
                        dose_time = "",
                        course_duration = ""
                    )
                    viewModel.addCartRX(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
                }

            }else{
                AppPreferences.getInstance(requireContext()).saveProductId(product?.id.toString())
                findNavController().navigate(R.id.nav_pos_shop_details)
            }

        }

        binding.recyclerProduct.apply {
            layoutManager = GridLayoutManager(requireContext(), 2)
            adapter = productAdapter
        }
    }

    private fun setViewModel() {
        val appPreferences = AppPreferences.getInstance(requireContext())
        viewModel = ViewModelProvider(
            this,
            HomeViewModelFactory(requireContext())
        ).get(HomeViewModel::class.java)

        viewLifecycleOwner.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                _binding = null
            }
        })
        viewModel.loadingState.observe(viewLifecycleOwner) { isLoading ->

            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.INVISIBLE

        }
        viewModel.showAlertEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            showAlertCustom(message)

        }
        viewModel.createdStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            if (message == "Patient saved successfully"){
                binding.rltBtn.visibility = View.GONE
            }else{
                binding.rltBtn.visibility = View.VISIBLE
            }
           Toast.makeText(requireContext(),message,Toast.LENGTH_LONG).show()
            //56567876

        }
        viewModel.patientIdStatus.observe(viewLifecycleOwner) { patient_id ->
            binding.progressBar.visibility = View.GONE
           patientId = patient_id

        }
        viewModel.addCartShopStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            Toast.makeText(requireContext(),message,Toast.LENGTH_LONG).show()
            viewModel.getCartCount(appPreferences.getToken().toString())

        }
        viewModel.productEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE

            binding.pageShimmer.shimmer.visibility = View.GONE
            binding.pageShimmer.shimmer.stopShimmer()

        }
        viewModel.productListData.observe(viewLifecycleOwner) { data ->

            binding.progressBar.visibility = View.GONE
            binding.progressBarSmall.visibility = View.GONE
            try {
                val totalPages = data?.totalPages
                if (!totalPages.isNullOrEmpty()) {
                    viewModel.totalPageCount = totalPages.toInt()
                } else {
                    // Handle the case where totalPages is empty or null
                }
            } catch (e: NumberFormatException) {
                // Handle the exception if totalPages is still an invalid format
            }
            if (viewModel.currentPage == 1) {

                manageProducts(data)
            } else {
                productAdapter.addProducts(data?.products!!)
            }
        }
        viewModel.influencerList.observe(viewLifecycleOwner) { data ->
            manageInfluencer(data)
        }

        viewModel.cartCount.observe(viewLifecycleOwner) { data ->

            binding.txtCartCount.text = data?.cartCount
            AppPreferences.getInstance(requireContext()).saveCartCount(data?.cartCount)

            if (data?.cartCount == "0"){
                binding.rltCartCount.visibility = View.INVISIBLE
            }else{
                binding.rltCartCount.visibility = View.VISIBLE
            }
        }



        viewModel.getCartCount(appPreferences.getToken().toString())
        viewModel.getInfluencerList(appPreferences.getToken().toString())

    }

    private fun manageCustomer(data: List<UserDetails>?) {


        if (data.isNullOrEmpty()){
            binding.cardCustBox.strokeColor = Color.parseColor("#A61C5C")
            binding.rltBtn.visibility = View.VISIBLE
            binding.cardCreate.visibility = View.VISIBLE
            binding.cardUpdate.visibility = View.GONE

            binding.editCivilId.setText(null)
            binding.editAltPhone.setText(null)
            binding.editFullName.setText(null)
            binding.editEmail.setText(null)
            binding.txtDob.setText(null)
        }else{
            binding.cardCustBox.strokeColor = Color.parseColor("#00BCD7")
            patientId = data?.get(0)?.id.toString()
            binding.rltBtn.visibility = View.VISIBLE
            binding.cardCreate.visibility = View.GONE
            binding.cardUpdate.visibility = View.VISIBLE

            binding.editCivilId.setText(data.get(0)?.civilId)
            binding.editAltPhone.setText(data.get(0)?.alternateNumber)
            binding.editFullName.setText(data.get(0)?.name)
            binding.editEmail.setText(data.get(0)?.email)
            binding.txtDob.setText(data.get(0)?.dob)

        }
    }

    fun showAlertCustom(message: String) {
        val builder = AlertDialog.Builder(requireContext())


        // set the custom layout
        val customLayout: View = layoutInflater.inflate(R.layout.validation_alert, null)
        builder.setView(customLayout)
        val text_validation = customLayout.findViewById<TextView>(R.id.text_validation)
        text_validation.text = message
        val continueShoppingButton = customLayout.findViewById<MaterialCardView>(R.id.view_cart)
        lateinit var dialog: AlertDialog
        continueShoppingButton.setOnClickListener {
            if (message == "Internal Server Error"){
                findNavController().popBackStack()
                dialog?.dismiss()
            }else{
                dialog?.dismiss()
            }

        }

        dialog = builder.create()
        dialog.show()
    }

    private fun manageProducts(data: ProductListResponse?) {

        binding.recyclerProduct.visibility = View.VISIBLE
        productAdapter.setProducts(data?.products?.toMutableList() ?: mutableListOf())
    }

    private fun manageInfluencer(data: List<InfluencerListResponse>?) {
        val dropdownPopup =
            DropdownPopupInfluencer(
                requireContext(),
                binding.cardInfluencer,
                data
            ) { selectedItem, selectedId, data ->
                binding.txtInfluencer.text = selectedItem
                selectedInfluencerId = data?.id.toString()
                callProductAPI("")
            }

        binding.cardInfluencer.setOnClickListener {
            dropdownPopup.show()
        }
    }

    private fun callProductAPI(status: String) {

//        if (status == "first"){
//            viewModel.emptyCart(AppPreferences.getInstance(requireContext()).getToken().toString())
//        }

        binding.pageShimmer.shimmer.visibility = View.VISIBLE
        binding.pageShimmer.shimmer.startShimmer()

        binding.recyclerProduct.visibility = View.GONE
        binding.txtNoData.visibility = View.GONE

        viewModel.currentPage = 1
        viewModel.clearProductListData()
        val appPreferences = AppPreferences.getInstance(requireContext())
        val request = ApiService.ProductRequest(
            page_no = "1",
            per_page = "4",
            category = "",
            brand = "",
            sku = "",
            seller = "",
            medical_rep_id = "",
            keyword = searchValue,
            influencer_id = "",
            isFavourite = "0",
            listFrom = ""
        )
        binding.progressBar.visibility = View.VISIBLE

        viewModel.getProductList(appPreferences.getToken().toString(), request)
    }

    private fun manageSearch() {
        binding.editSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(
                s: CharSequence?,
                start: Int,
                count: Int,
                after: Int
            ) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable?) {
                searchRunnable?.let { searchHandler.removeCallbacks(it) }
                searchRunnable = Runnable {
                    searchValue = s.toString()
                    callProductAPI("")
                }
                searchHandler.postDelayed(searchRunnable!!, 500)
            }
        })
    }

    private fun handleClick() {
        binding.imgBack.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.imgCart.setOnClickListener {
            findNavController().navigate(R.id.nav_cart)
        }

        binding.cardCreate.setOnClickListener {
            validateFields("create")
        }

        binding.cardUpdate.setOnClickListener {
            validateFields("update")
        }
        binding.lytCalendar.setOnClickListener {
            showDatePicker()
        }
    }

    private fun showDatePicker() {
        val calendar = Calendar.getInstance()
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        val datePickerDialog = DatePickerDialog(requireContext(), { _, selectedYear, selectedMonth, selectedDay ->
            val formattedDate = String.format("%02d-%02d-%04d", selectedDay, selectedMonth + 1, selectedYear)
            binding.txtDob.text = formattedDate
            binding.txtDobEmpty.visibility = View.GONE
        }, year, month, day)

        datePickerDialog.show()
    }

    private fun validateFields(type: String) {
        if (binding.editPhone.text.isEmpty()){
            binding.txtPhoneEmpty.visibility = View.VISIBLE
        }else if (binding.editCivilId.text.isEmpty()){
            binding.txtCivilEmpty.visibility = View.VISIBLE
        }else if (binding.editFullName.text.isEmpty()){
            binding.txtNameEmpty.visibility = View.VISIBLE
        }else if (binding.editEmail.text.isEmpty()){
            binding.txtEmailEmpty.visibility = View.VISIBLE
        }else if (binding.txtDob.text.isEmpty()){
            binding.txtDobEmpty.visibility = View.VISIBLE
        }else{

            if (type == "create"){
                callCreatePatientApi()
            }else{
                callUpdatePatientApi()
            }

        }
    }
    private fun callUpdatePatientApi() {

        binding.progressBar.visibility = View.VISIBLE
        val request = ApiService.CreatePatientRequest(
            email = binding.editEmail.text.toString(),
            phone ="+965"+ binding.editPhone.text.toString(),
            name = binding.editFullName.text.toString(),
            dob = binding.txtDob.text.toString(),
            altMobileNo = binding.editAltPhone.text.toString(),
            civilId = binding.editCivilId.text.toString(),
            id = patientId
        )
        viewModel.createPatient(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
    }
    private fun callCreatePatientApi() {

        binding.progressBar.visibility = View.VISIBLE
        val request = ApiService.CreatePatientRequest(
            email = binding.editEmail.text.toString(),
            phone = "+965"+binding.editPhone.text.toString(),
            name = binding.editFullName.text.toString(),
            dob = binding.txtDob.text.toString(),
            altMobileNo = binding.editAltPhone.text.toString(),
            civilId = binding.editCivilId.text.toString(),
            id = ""
        )
        viewModel.createPatient(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
    }


}