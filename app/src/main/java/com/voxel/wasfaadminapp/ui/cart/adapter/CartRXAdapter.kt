package com.voxel.wasfaadminapp.ui.cart.adapter

import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.voxel.wasfaadminapp.databinding.RxCartItemBinding
import com.voxel.wasfaadminapp.network.response.CartItem

class CartRXAdapter(
    private val CatList: List<CartItem>?,
    private val decrementListener: (CartItem, String) -> Unit,
    private val incrementListener: (CartItem, String) -> Unit,
    private val deleteListener: (CartItem, String) -> Unit,
    private val doseListener: (CartItem, String) -> Unit,
    private val doseTimeListener: (CartItem, String) -> Unit,
    private val doseDayListener: (CartItem, String) -> Unit,
    private val courseDurationListener: (CartItem, String) -> Unit,
    private val courseDayListener: (CartItem, String) -> Unit,
    private val notesListener: (CartItem, String) -> Unit,
    private val listener: (CartItem, Int) -> Unit,

) :
    RecyclerView.Adapter<CartRXAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = RxCartItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(v,doseListener,doseDayListener,doseTimeListener,courseDayListener,courseDurationListener,notesListener,deleteListener,decrementListener,incrementListener)
    }



    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindItem(CatList!![position])
        holder.itemView.setOnClickListener {
            listener(CatList!![position],position)
        }


    }




    override fun getItemCount(): Int {
        return CatList!!.size
    }

    class ViewHolder(
        var itemBinding: RxCartItemBinding,
        private val doseListener: (CartItem, String) -> Unit,
        private val doseDayListener: (CartItem, String) -> Unit,
        private val doseTimeListener: (CartItem, String) -> Unit,
        private val courseDayListener: (CartItem, String) -> Unit,
        private val courseDurationListener: (CartItem, String) -> Unit,
        private val notesListener: (CartItem, String) -> Unit,
        private val deleteListener: (CartItem, String) -> Unit,
        private val decrementListener: (CartItem, String) -> Unit,
        private val incrementListener: (CartItem, String) -> Unit
    ) :
        RecyclerView.ViewHolder(itemBinding.root) {
        fun bindItem(data: CartItem) {
            var count = data.quantity ?: 1

            fun updateCountUI() {
                itemBinding.txtCount.text = count.toString()
                itemBinding.cardDecreaseCount.isEnabled = count > 1
                itemBinding.cardDecreaseCount.alpha = if (count > 1) 1.0f else 0.5f
            }

            updateCountUI()

            itemBinding.cardDecreaseCount.setOnClickListener {
                if (count > 1) {
                    count--
                    updateCountUI()
                    decrementListener(data, count.toString())
                }
            }
            itemBinding.cardIncreaseCount.setOnClickListener {
                count++
                updateCountUI()
                incrementListener(data, count.toString())
            }
            itemBinding.lytRemove.setOnClickListener {
                deleteListener(data,data?.id.toString())
            }
            itemBinding.cardCourseDuration.setOnClickListener {
                showCourseDurationDropdown(data)
            }

            itemBinding.cardDose.setOnClickListener {
                showDoseDropdown(data)
            }
            itemBinding.cardDoseDay.setOnClickListener {
                showDoseDayDropdown(data)
            }
            itemBinding.cardDoseTime.setOnClickListener {
                showDoseTimeDropdown(data)
            }
            itemBinding.cardCourseDay.setOnClickListener {
                showCourseDayDropdown(data)
            }
            Glide.with(itemBinding.root.context)
                .load(data?.productThumbnailImage)
                .into(itemBinding.imdProduct)

            itemBinding.txtCount.text = data?.quantity.toString()
            itemBinding.txtSelectedDose.text = data?.dose ?: "Select"
            itemBinding.txtPdName.text = data?.productName
            itemBinding.txtPrize.text = data?.unitPrice.toString()
            itemBinding.editNotes.setText(
                if (data?.description.isNullOrBlank() || data.description == "null") "" else data.description
            )

            itemBinding.txtDoseSelectedDay.text = data?.doseday ?: "Select"
            itemBinding.txtDoseTime.text = data?.dose_time ?: "Select"
            itemBinding.txtCourseDay.text = data?.course_day ?: "Select"
            itemBinding.txtCourseDuration.text = data?.course_duration ?: "Select"

            itemBinding.editNotes.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {

                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

                }

                override fun afterTextChanged(s: Editable?) {
                  notesListener(data,s.toString())
                }
            })
        }
        private fun showDoseDropdown(cartItem: CartItem) {
            val doses = listOf("Daily", "Weekly", "Monthly")
            val popup = android.widget.PopupMenu(itemView.context, itemBinding.cardDose)
            doses.forEachIndexed { index, dose ->
                popup.menu.add(0, index, index, dose)
            }

            popup.setOnMenuItemClickListener { menuItem ->
                val selectedDose = doses[menuItem.itemId]

                itemBinding.txtSelectedDose.text = selectedDose
                doseListener(cartItem,selectedDose)
                true
            }
            popup.show()
        }

        private fun showDoseDayDropdown(cartItem: CartItem) {
            val doses = listOf("1", "2", "3","4","5","6","7","8","9","10")
            val popup = android.widget.PopupMenu(itemView.context, itemBinding.cardDoseDay)
            doses.forEachIndexed { index, dose ->
                popup.menu.add(0, index, index, dose)
            }

            popup.setOnMenuItemClickListener { menuItem ->
                val selectedDose = doses[menuItem.itemId]

                itemBinding.txtDoseSelectedDay.text = selectedDose
                doseDayListener(cartItem,selectedDose)
                true
            }
            popup.show()
        }

        private fun showDoseTimeDropdown(cartItem: CartItem) {
            val doses = listOf("Before Meal", "After Meal", "Morning","Night","Any Time")
            val popup = android.widget.PopupMenu(itemView.context, itemBinding.cardDoseTime)
            doses.forEachIndexed { index, dose ->
                popup.menu.add(0, index, index, dose)
            }

            popup.setOnMenuItemClickListener { menuItem ->
                val selectedDose = doses[menuItem.itemId]

                itemBinding.txtDoseTime.text = selectedDose
                doseTimeListener(cartItem,selectedDose)
                true
            }
            popup.show()
        }

        private fun showCourseDayDropdown(cartItem: CartItem) {
            val doses = listOf("Day", "Weak", "Month")
            val popup = android.widget.PopupMenu(itemView.context, itemBinding.cardCourseDay)
            doses.forEachIndexed { index, dose ->
                popup.menu.add(0, index, index, dose)
            }

            popup.setOnMenuItemClickListener { menuItem ->
                val selectedDose = doses[menuItem.itemId]

                itemBinding.txtCourseDay.text = selectedDose
                courseDayListener(cartItem,selectedDose)
                true
            }
            popup.show()
        }

        private fun showCourseDurationDropdown(cartItem: CartItem) {
            val doses = listOf("1", "2", "3","4","5","6","7","8","9","10")
            val popup = android.widget.PopupMenu(itemView.context, itemBinding.cardCourseDuration)
            doses.forEachIndexed { index, dose ->
                popup.menu.add(0, index, index, dose)
            }

            popup.setOnMenuItemClickListener { menuItem ->
                val selectedDose = doses[menuItem.itemId]

                itemBinding.txtCourseDuration.text = selectedDose
                courseDurationListener(cartItem,selectedDose)
                true
            }
            popup.show()
        }
    }
}