package com.voxel.wasfaadminapp.ui.doctor.report

import android.content.res.Configuration
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.FragmentPrescriptionBinding
import com.voxel.wasfaadminapp.databinding.FragmentReportBinding
import com.voxel.wasfaadminapp.helper.AppPreferences
import com.voxel.wasfaadminapp.network.ApiService
import com.voxel.wasfaadminapp.ui.doctor.main.DoctorHomeActivity
import com.voxel.wasfaadminapp.ui.doctor.pres.PresDetailsFragment
import com.voxel.wasfaadminapp.ui.doctor.pres.adapter.PresListAdapter
import com.voxel.wasfaadminapp.ui.doctor.pres.add.AddPresFragment
import com.voxel.wasfaadminapp.ui.doctor.report.adapter.ReportAdapter
import com.voxel.wasfaadminapp.viewmodel.HomeViewModel
import com.voxel.wasfaadminapp.viewmodel.HomeViewModelFactory


class ReportFragment : Fragment() {
    private var _binding: FragmentReportBinding? = null
    private val binding get() = _binding!!
    private lateinit var viewModel: HomeViewModel
    private lateinit var productAdapter: ReportAdapter
    private var scrollListener: ViewTreeObserver.OnScrollChangedListener? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentReportBinding.inflate(inflater, container, false)
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)


        manageClick()
        setViewModel()
        managePresRecyclerView()
        setUpPagination()
        callReportAPI()
        if (!isTablet()){
            (activity as? DoctorHomeActivity)?.showBottomNav()
        }else{
            (activity as? DoctorHomeActivity)?.hideBottomNav()
        }
    }

    private fun setUpPagination() {
        binding.recyclerPres.isNestedScrollingEnabled = false
        scrollListener = ViewTreeObserver.OnScrollChangedListener {
            if (_binding == null) return@OnScrollChangedListener

            val view = binding.nestedScrollView.getChildAt(binding.nestedScrollView.childCount - 1) as View
            val diff: Int = view.bottom - (binding.nestedScrollView.height + binding.nestedScrollView.scrollY)
            if (diff == 0) {
                if (viewModel.isLastPageReport()) {
                    // No action for the last page
                } else {
                    viewModel.currentPageReport++
                    viewModel.loadNextPageReport("")
                    if (binding.progressBar.visibility == View.VISIBLE){
                        binding.progressBarSmall.visibility = View.GONE
                    }else{
                        binding.progressBarSmall.visibility = View.VISIBLE
                    }
                }
            }
        }

        binding.nestedScrollView.viewTreeObserver.addOnScrollChangedListener(scrollListener)

        // Clean up the listener when the view is destroyed
        viewLifecycleOwner.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                binding.nestedScrollView.viewTreeObserver.removeOnScrollChangedListener(scrollListener)
            }
        })
    }
    fun isTablet(): Boolean {
        val metrics = resources.displayMetrics
        val widthDp = metrics.widthPixels / metrics.density

        return widthDp >= 600 ||
                resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
    }
    private fun manageClick() {
        binding.imgMenu.setOnClickListener {
            (activity as? DoctorHomeActivity)?.toggleBottomNav()
        }

    }
    private fun setViewModel() {
        val appPreferences = AppPreferences.getInstance(requireContext())
        viewModel = ViewModelProvider(
            this,
            HomeViewModelFactory(requireContext())
        ).get(HomeViewModel::class.java)

        viewLifecycleOwner.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                _binding = null
            }
        })


        viewModel.reportEvent.observe(viewLifecycleOwner) { message ->
            if (message == "0"){
                binding.txtNoData.visibility = View.VISIBLE
            }else{
                binding.txtNoData.visibility = View.GONE
            }
            binding.progressBar.visibility = View.GONE

        }
        viewModel.reportData.observe(viewLifecycleOwner) { data ->


            binding.progressBarSmall.visibility = View.GONE

            try {
                val totalPages = data?.totalPages
                if (!totalPages.isNullOrEmpty()) {
                    viewModel.totalPageCountReport= totalPages.toInt()
                } else {
                    // Handle the case where totalPages is empty or null
                }
            } catch (e: NumberFormatException) {
                // Handle the exception if totalPages is still an invalid format
            }
            if (viewModel.currentPageReport == 1) {

                binding.recyclerPres.visibility = View.VISIBLE
                productAdapter.setProducts(data?.prescriptions?.toMutableList() ?: mutableListOf())


            } else {
                productAdapter.addProducts(data?.prescriptions!!)
            }
        }
    }
    private fun managePresRecyclerView() {

        productAdapter = ReportAdapter(
            mutableListOf()
        ) { product, type ->

        }

        binding.recyclerPres.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            adapter = productAdapter
        }

    }

    private fun callReportAPI() {

        binding.recyclerPres.visibility = View.GONE
        binding.txtNoData.visibility = View.GONE

        viewModel.currentPageReport = 1
        viewModel.clearReportListData()
        val appPreferences = AppPreferences.getInstance(requireContext())
        val request = ApiService.ReportRequest(
            page_no = "1",
            per_page = "5",

        )
        binding.progressBar.visibility = View.VISIBLE

        viewModel.getReport(appPreferences.getToken().toString(), request)
    }
}