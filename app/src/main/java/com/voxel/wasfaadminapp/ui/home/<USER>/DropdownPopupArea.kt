package com.voxel.wasfaadminapp.ui.home.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.ListView
import android.widget.PopupWindow
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.ui.home.model.Cat

class DropdownPopupArea(
    private val context: Context,
    private val anchorView: View,
    private val items: List<Cat>?,
    private val onItemClickListener: () -> Unit
) {
    private val popupWindow: PopupWindow = PopupWindow(context)

    init {
        val layoutInflater = LayoutInflater.from(context)
        val contentView = layoutInflater.inflate(R.layout.dropdown_popup, null)
        val listView = contentView.findViewById<ListView>(R.id.listViewDropdown)

        // Assuming items is a list of Governorate objects
        val governorateAdapter = FilterAdapter(context,
            (items ?: emptyList()) as List<Cat>
        )
        listView.adapter = governorateAdapter

        listView.setOnItemClickListener { _, _, position, _ ->
            val selectedItem = governorateAdapter.getItem(position)?.name ?: ""
            val selectedId = ""
            onItemClickListener.invoke()
            popupWindow.dismiss()
        }

        popupWindow.contentView = contentView
        popupWindow.width = LinearLayout.LayoutParams.WRAP_CONTENT
        popupWindow.height = LinearLayout.LayoutParams.WRAP_CONTENT
        popupWindow.isOutsideTouchable = true
    }

    fun show() {
        popupWindow.showAsDropDown(anchorView)
    }

    fun dismiss() {
        popupWindow.dismiss()
    }
}

