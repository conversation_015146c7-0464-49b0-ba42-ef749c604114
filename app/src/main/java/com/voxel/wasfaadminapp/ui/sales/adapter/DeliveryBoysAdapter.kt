package com.voxel.wasfaadminapp.ui.sales.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatRadioButton
import androidx.recyclerview.widget.RecyclerView
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.network.response.DeliveryBoys
import com.voxel.wasfaadminapp.network.response.InfluencerListResponse

class DeliveryBoysAdapter(
    private val influencers: List<DeliveryBoys>,
    private val onItemSelected: (DeliveryBoys) -> Unit,
    private val preSelectedName: String? = null
) : RecyclerView.Adapter<DeliveryBoysAdapter.ViewHolder>() {

    private var selectedPosition = -1

    init {
        selectedPosition = influencers.indexOfFirst { it.name == preSelectedName }
    }

    inner class ViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
        val radioButton: AppCompatRadioButton = view.findViewById(R.id.radio_button)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val itemView = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_influencer, parent, false)
        return ViewHolder(itemView)
    }

    override fun getItemCount(): Int = influencers.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val influencer = influencers[position]
        holder.radioButton.text = influencer.name
        holder.radioButton.isChecked = position == selectedPosition

        holder.radioButton.setOnClickListener {
            val prevSelected = selectedPosition
            selectedPosition = position
            notifyItemChanged(prevSelected)
            notifyItemChanged(selectedPosition)
            onItemSelected(influencer)
        }
    }
}
