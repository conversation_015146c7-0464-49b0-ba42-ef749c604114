package com.voxel.wasfaadminapp.ui.cart

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.card.MaterialCardView
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.FragmentPosRxCartBinding
import com.voxel.wasfaadminapp.helper.AppPreferences
import com.voxel.wasfaadminapp.network.ApiService
import com.voxel.wasfaadminapp.network.response.CartItem
import com.voxel.wasfaadminapp.network.response.PatientInfo
import com.voxel.wasfaadminapp.ui.cart.adapter.CartRXAdapter
import com.voxel.wasfaadminapp.viewmodel.HomeViewModel
import com.voxel.wasfaadminapp.viewmodel.HomeViewModelFactory

class POSRXCartFragment : Fragment() {
    private var _binding: FragmentPosRxCartBinding? = null
    private val binding get() = _binding!!
    private lateinit var viewModel: HomeViewModel
    private var patientInfo: List<PatientInfo>? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentPosRxCartBinding.inflate(inflater, container, false)
        return binding.root

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        handleClick()
        setViewModel()
    }

    private fun setViewModel() {
        val appPreferences = AppPreferences.getInstance(requireContext())
        viewModel = ViewModelProvider(
            this,
            HomeViewModelFactory(requireContext())
        ).get(HomeViewModel::class.java)

        viewLifecycleOwner.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                _binding = null
            }
        })
        viewModel.loadingState.observe(viewLifecycleOwner) { isLoading ->

            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.INVISIBLE

        }
        viewModel.showAlertEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            showAlertCustom(message)

        }
        viewModel.deleteCartShopStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.VISIBLE
            println("me =--------------")
            viewModel.getCart(appPreferences.getToken().toString())

        }
        viewModel.cartUpdateStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            viewModel.getCart(appPreferences.getToken().toString())

        }
        viewModel.cartList.observe(viewLifecycleOwner) { data ->
            println("--------------------------")
            binding.progressBar.visibility = View.GONE

            manageCart(data?.cartItems)
            patientInfo = data?.patientInfo

        }

        binding.progressBar.visibility = View.VISIBLE
        viewModel.getCart(appPreferences.getToken().toString())

    }

    fun showAlertCustom(message: String) {
        val builder = AlertDialog.Builder(requireContext())


        // set the custom layout
        val customLayout: View = layoutInflater.inflate(R.layout.validation_alert, null)
        builder.setView(customLayout)
        val text_validation = customLayout.findViewById<TextView>(R.id.text_validation)
        text_validation.text = message
        val continueShoppingButton = customLayout.findViewById<MaterialCardView>(R.id.view_cart)
        lateinit var dialog: AlertDialog
        continueShoppingButton.setOnClickListener {
            if (message == "Internal Server Error") {
                findNavController().popBackStack()
                dialog?.dismiss()
            } else {
                dialog?.dismiss()
            }

        }

        dialog = builder.create()
        dialog.show()
    }

    private fun handleClick() {
        binding.cardProceed.setOnClickListener {
            if (patientInfo.isNullOrEmpty()) {
                Toast.makeText(requireContext(), "Please select a customer", Toast.LENGTH_SHORT).show()
                findNavController().popBackStack()
            } else {
                findNavController().navigate(R.id.nav_pos_rx_cart_summery)
            }
        }
        binding.imgBack.setOnClickListener {
            findNavController().popBackStack()
        }
    }


    private fun manageCart(cartItems: List<CartItem>?) {

        if (cartItems.isNullOrEmpty()) {
            binding.txtNoData.visibility = View.VISIBLE
        } else {
            binding.txtNoData.visibility = View.GONE
            binding.recyclerRxCart.apply {
                layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
                val catAdapter = CartRXAdapter(
                    cartItems,
                    { cart, count ->  //decrement
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = count,
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = cart?.description.toString(),
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: "",
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, count -> //increment
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = count,
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = cart?.description.toString(),
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: "",
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, id -> // delete
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.CartRemoveRequest(
                            id = id
                        )
                        viewModel.deleteCartShop(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, doseValue -> //dose

                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = doseValue,
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = cart?.description ?: "",
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: ""
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, doseTime -> //dose time
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = cart?.dose ?: "",
                            dose_time = doseTime,
                            course_day = cart?.course_day ?: "",
                            description = cart?.description ?: "",
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: ""
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, dayValue -> //dose day
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = cart?.description ?: "",
                            course_duration = cart?.course_duration ?: "",
                            dose_day = dayValue
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, courseDurationValue -> //course duration
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = cart?.description ?: "",
                            course_duration = courseDurationValue,
                            dose_day = cart?.doseday ?: "",
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, courseDayValue -> //course day
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = courseDayValue,
                            description = cart?.description ?: "",
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: "",
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { cart, notesValue -> //notes
                        binding.progressBar.visibility = View.VISIBLE
                        val request = ApiService.UpdateRXCartRequest(
                            id = cart?.id.toString(),
                            quantity = cart?.quantity.toString(),
                            dose = cart?.dose ?: "",
                            dose_time = cart?.dose_time ?: "",
                            course_day = cart?.course_day ?: "",
                            description = notesValue,
                            course_duration = cart?.course_duration ?: "",
                            dose_day = cart?.doseday ?: "",
                        )
                        viewModel.updateCartRx(
                            AppPreferences.getInstance(requireContext()).getToken().toString(),
                            request
                        )
                    },
                    { Cat, position ->

                    },
                )
                adapter = catAdapter
            }
        }

    }

}