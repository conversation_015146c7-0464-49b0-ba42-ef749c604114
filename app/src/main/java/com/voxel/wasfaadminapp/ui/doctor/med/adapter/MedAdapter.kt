package com.voxel.wasfaadminapp.ui.doctor.med.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.ItemMedBinding
import com.voxel.wasfaadminapp.databinding.ItemMedicationBinding
import com.voxel.wasfaadminapp.databinding.ItemMedicationListBinding
import com.voxel.wasfaadminapp.databinding.ItemPresBinding
import com.voxel.wasfaadminapp.network.response.Products
import com.voxel.wasfaadminapp.ui.home.model.Cat

class MedAdapter(
    private val data: MutableList<Products>,
    private val listener: (Products, String) -> Unit,
) :
    RecyclerView.Adapter<MedAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = ItemMedBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindItem(data!![position])

        holder.itemView.setOnClickListener {
            listener(data!![position],"")
        }
        holder.itemBinding.switchFav.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                listener(data!![position],"fav")
            } else {
                listener(data!![position],"unFav")
            }
        }

    }

    override fun getItemCount(): Int {
        return data!!.size
    }

    class ViewHolder(var itemBinding: ItemMedBinding) :
        RecyclerView.ViewHolder(itemBinding.root) {
        fun bindItem(data: Products) {

            itemBinding.txtPrize.text = data?.basePrice
            itemBinding.txtProductName.text = data?.name
            itemBinding.txtStoreName.text = data?.sellerName

            Glide.with(itemBinding.root.context)
                .load(data?.thumbnail_image)
                .into(itemBinding.imgProduct)

            val logoUrl = data?.sellerLogo
            Glide.with(itemBinding.root.context)
                .load(logoUrl)
                .error(R.drawable.wasfa_logo)
                .into(itemBinding.imgSeller)


            itemBinding.txtStock.text =  data?.currentStock


            itemBinding.switchFav.isChecked = data?.isFavourite ?: false

        }
    }
        fun setProducts(newProducts: List<Products>) {
            data.clear()
            data.addAll(newProducts)
            notifyDataSetChanged()
        }

        fun addProducts(newProducts: List<Products>) {
            val startPosition = data.size
            data.addAll(newProducts)
            notifyItemRangeInserted(startPosition, newProducts.size)
        }

}