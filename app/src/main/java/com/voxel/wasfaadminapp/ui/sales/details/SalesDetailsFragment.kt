package com.voxel.wasfaadminapp.ui.sales.details

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextWatcher
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.Toast
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.FragmentSalesDetailsBinding
import com.voxel.wasfaadminapp.helper.AppPreferences
import com.voxel.wasfaadminapp.network.ApiService
import com.voxel.wasfaadminapp.network.response.DeliveryBoyResponse
import com.voxel.wasfaadminapp.network.response.OrderListResponse
import com.voxel.wasfaadminapp.ui.filter.adapter.FilterNothingSelectedAdapter
import com.voxel.wasfaadminapp.ui.home.model.Cat
import com.voxel.wasfaadminapp.ui.pos.adapter.filter.FilterDeliveryBoyAdapter
import com.voxel.wasfaadminapp.ui.sales.adapter.OrderListAdapter
import com.voxel.wasfaadminapp.viewmodel.HomeViewModel
import com.voxel.wasfaadminapp.viewmodel.HomeViewModelFactory
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale


class SalesDetailsFragment : Fragment() {
    private var _binding: FragmentSalesDetailsBinding? = null
    private val binding get() = _binding!!
    private lateinit var viewModel: HomeViewModel
    private lateinit var orderAdapter: OrderListAdapter
    var searchValue: String = ""
    var area: String = ""
    var date: String = ""
    var order_type: String = ""
    var collected_by_seller: String = ""
    var zones: String = ""
    var payment_change: String = ""
    var payment_method: String = ""
    var payment_status: String = ""
    var collected_by: String = ""
    var delivery_boy: String = ""
    var delivery_status: String = ""
    private var scrollListener: ViewTreeObserver.OnScrollChangedListener? = null
    private var searchHandler: Handler = Handler(Looper.getMainLooper())
    private var searchRunnable: Runnable? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentSalesDetailsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        handleFilterClick()
        handleClick()
        setViewModel()
        setUpRecyclerView()

        setUpPagination()
        manageSearch()
        manageFilter()

        if (AppPreferences.getInstance(requireContext()).getOrderType() == "Pending Orders"){
           order_type = "pending"
            binding.txtHeader.text = "Pending Orders"
            callOrderListApi()
        }else if (AppPreferences.getInstance(requireContext()).getOrderType() == "Cancelled Orders"){
            order_type = "cancelled"
            binding.txtHeader.text = "Cancelled Orders"
            callOrderListApi()
        }else{
            order_type = ""
            binding.txtHeader.text = "All Orders"
            callOrderListApi()
        }
    }

    private fun handleFilterClick() {
        binding.pageFilter.cardApplyFilter.setOnClickListener {
            binding.pageFilter.lytFilter.visibility = View.GONE
            callOrderListApi()
        }
        binding.pageFilter.imgClose.setOnClickListener {
            binding.pageFilter.lytFilter.visibility = View.GONE
        }
        binding.pageFilter.cardFilterByDate.setOnClickListener {
            if (binding.pageFilter.imgFilterByDateArrow.rotation == 0f) {
                binding.pageFilter.imgFilterByDateArrow.rotation = 180f
                binding.pageFilter.cardFilterByDateHide.visibility = View.VISIBLE
            } else {
                binding.pageFilter.imgFilterByDateArrow.rotation = 0f
                binding.pageFilter.cardFilterByDateHide.visibility = View.GONE
            }
        }
        binding.pageFilter.cardFilterPaymentStatus.setOnClickListener {
            if (binding.pageFilter.imgPaymentStatusArrow.rotation == 0f) {
                binding.pageFilter.imgPaymentStatusArrow.rotation = 180f
                binding.pageFilter.cardFilterPaymentStatusHide.visibility = View.VISIBLE
            } else {
                binding.pageFilter.imgPaymentStatusArrow.rotation = 0f
                binding.pageFilter.cardFilterPaymentStatusHide.visibility = View.GONE
            }
        }
        binding.pageFilter.cardNothingSelected.setOnClickListener {
            if (binding.pageFilter.imgNothingSelectedArrow.rotation == 0f) {
                binding.pageFilter.imgNothingSelectedArrow.rotation = 180f
                binding.pageFilter.cardNothingSelectedHide.visibility = View.VISIBLE
            } else {
                binding.pageFilter.imgNothingSelectedArrow.rotation = 0f
                binding.pageFilter.cardNothingSelectedHide.visibility = View.GONE
            }
        }

        binding.pageFilter.cardFilterDeliveryStaff.setOnClickListener {
            if (binding.pageFilter.imgDeliveryStaffArrow.rotation == 0f) {
                binding.pageFilter.imgDeliveryStaffArrow.rotation = 180f
                binding.pageFilter.cardDeliveryStaffHide.visibility = View.VISIBLE
            } else {
                binding.pageFilter.imgDeliveryStaffArrow.rotation = 0f
                binding.pageFilter.cardDeliveryStaffHide.visibility = View.GONE
            }
        }

        binding.pageFilter.cardFilterPaymentChange.setOnClickListener {
            if (binding.pageFilter.imgFilterByPc.rotation == 0f) {
                binding.pageFilter.imgFilterByPc.rotation = 180f
                binding.pageFilter.cardFilterByPCHide.visibility = View.VISIBLE
            } else {
                binding.pageFilter.imgFilterByPc.rotation = 0f
                binding.pageFilter.cardFilterByPCHide.visibility = View.GONE
            }
        }
        binding.pageFilter.cardFilterPaymentMethod.setOnClickListener {
            if (binding.pageFilter.imgFilterPM.rotation == 0f) {
                binding.pageFilter.imgFilterPM.rotation = 180f
                binding.pageFilter.cardFilterByPMHide.visibility = View.VISIBLE
            } else {
                binding.pageFilter.imgFilterPM.rotation = 0f
                binding.pageFilter.cardFilterByPMHide.visibility = View.GONE
            }
        }
        binding.pageFilter.cardClear.setOnClickListener {
            delivery_boy = ""
            delivery_status = ""
            date = ""
            payment_change = ""
            payment_method = ""
            binding.pageFilter.txtFilterDeliveryStaff.text = null
            binding.pageFilter.txtFilterDeliveryStaff.text = null
            binding.pageFilter.txtFilterByDate.text = null
            binding.editSearch.text = null
            searchValue = ""
            binding.pageFilter.txtFilterByPc.text = null
            binding.pageFilter.txtFilterPM.text = null
            binding.pageFilter.lytFilter.visibility = View.GONE

            callOrderListApi()
        }
    }

    private fun manageFilter() {

        manageNothingSelected()
        managePaymentStatus()
        manageFilterByDate()
        manageFilterByPaymentChange()
        manageFilterByPaymentMethod()
    }

    private fun manageFilterByPaymentMethod() {
        binding.pageFilter.recyclerFilterByPM.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            val catAdapter = FilterNothingSelectedAdapter(createPC()) { data, position ->

                binding.pageFilter.txtFilterPM.text = data?.name
                payment_method = data?.name.toString()
                closeFilterByPM()
            }
            adapter = catAdapter
        }
    }

    private fun closeFilterByPM() {
        binding.pageFilter.imgFilterPM.rotation = 0f
        binding.pageFilter.cardFilterByPMHide.visibility = View.GONE
    }

    private fun manageFilterByPaymentChange() {
        binding.pageFilter.recyclerFilterByPC.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            val catAdapter = FilterNothingSelectedAdapter(createPC()) { data, position ->

                binding.pageFilter.txtFilterByPc.text = data?.name
                payment_change = data?.name.toString()
                closeFilterByPC()
            }
            adapter = catAdapter
        }
    }

    private fun closeFilterByPC() {
        binding.pageFilter.imgFilterByPc.rotation = 0f
        binding.pageFilter.cardFilterByPCHide.visibility = View.GONE
    }

    private fun manageFilterByDate() {
        binding.pageFilter.recyclerFilterByDate.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            val catAdapter = FilterNothingSelectedAdapter(createPD()) { data, position ->

                binding.pageFilter.txtFilterByDate.text = data?.name
                handleDateFilter(data?.name.toString())
                closeFilterByDate()
            }
            adapter = catAdapter
        }
    }

    private fun handleDateFilter(type: String) {
        val dateFormat = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
        var calendar = Calendar.getInstance()

        val dateNew: String
        val rangeString: String

        when (type) {
            "Today" -> {
                dateNew = dateFormat.format(calendar.time)
                rangeString = "$dateNew to $dateNew"
                date = rangeString
                binding.pageFilter.txtFilterByDate.text = rangeString
            }

            "Yesterday" -> {
                calendar.add(Calendar.DAY_OF_YEAR, -1)
                dateNew = dateFormat.format(calendar.time)
                rangeString = "$dateNew to $dateNew"
                date = rangeString
                binding.pageFilter.txtFilterByDate.text = rangeString
            }

            "Last 7 days" -> {
                val end = dateFormat.format(calendar.time)
                calendar.add(Calendar.DAY_OF_YEAR, -6)
                val start = dateFormat.format(calendar.time)
                rangeString = "$start to $end"
                date = rangeString
                binding.pageFilter.txtFilterByDate.text = rangeString
            }

            "Last 30 days" -> {
                val end = dateFormat.format(calendar.time)
                calendar.add(Calendar.DAY_OF_YEAR, -29)
                val start = dateFormat.format(calendar.time)
                rangeString = "$start to $end"
                date = rangeString
                binding.pageFilter.txtFilterByDate.text = rangeString
            }

            "This month" -> {
                calendar.set(Calendar.DAY_OF_MONTH, 1)
                val start = dateFormat.format(calendar.time)
                calendar = Calendar.getInstance()
                val end = dateFormat.format(calendar.time)
                rangeString = "$start to $end"
                date = rangeString
                binding.pageFilter.txtFilterByDate.text = rangeString
            }

            "Last month" -> {
                calendar.add(Calendar.MONTH, -1)
                calendar.set(Calendar.DAY_OF_MONTH, 1)
                val start = dateFormat.format(calendar.time)

                calendar.set(
                    Calendar.DAY_OF_MONTH,
                    calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
                )
                val end = dateFormat.format(calendar.time)
                rangeString = "$start to $end"
                date = rangeString
                binding.pageFilter.txtFilterByDate.text = rangeString
            }

            else -> return // unsupported type
        }
    }

    private fun closeFilterByDate() {
        binding.pageFilter.imgFilterByDateArrow.rotation = 0f
        binding.pageFilter.cardFilterByDateHide.visibility = View.GONE
    }

    private fun managePaymentStatus() {
        binding.pageFilter.recyclerPaymentStatus.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            val catAdapter = FilterNothingSelectedAdapter(createPS()) { data, position ->

                binding.pageFilter.txtPaymentStatus.text = data?.name
                payment_status = data?.name.toString()
                closePaymentStatus()
            }
            adapter = catAdapter
        }
    }

    private fun closePaymentStatus() {
        binding.pageFilter.imgPaymentStatusArrow.rotation = 0f
        binding.pageFilter.cardFilterPaymentStatusHide.visibility = View.GONE
    }

    private fun manageNothingSelected() {

        binding.pageFilter.recyclerNothingSelected.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            val catAdapter = FilterNothingSelectedAdapter(createListNothing()) { data, position ->

                binding.pageFilter.txtFilterNothingSelected.text = data?.name
                delivery_status = data?.name.toString()
                closeNothingSelected()
            }
            adapter = catAdapter
        }

    }

    private fun closeNothingSelected() {
        binding.pageFilter.imgNothingSelectedArrow.rotation = 0f
        binding.pageFilter.cardNothingSelectedHide.visibility = View.GONE
    }

    private fun createListNothing(): ArrayList<Cat> {
        return arrayListOf<Cat>(

            Cat(
                "Pending",
                R.drawable.dummy_image
            ),
            Cat(
                "Confirmed",
                R.drawable.dummy_image
            ),
            Cat(
                "Picked Up",
                R.drawable.dummy_image
            ),
            Cat(
                "On The Way",
                R.drawable.dummy_image
            ),
            Cat(
                "Delivered",
                R.drawable.dummy_image
            ),
            Cat(
                "Closed",
                R.drawable.dummy_image
            ),
            Cat(
                "Cancel",
                R.drawable.dummy_image
            )
        )
    }

    private fun createPS(): ArrayList<Cat> {
        return arrayListOf<Cat>(
            Cat(
                "Un-Paid",
                R.drawable.dummy_image
            ),
            Cat(
                "Paid",
                R.drawable.dummy_image
            ),
            Cat(
                "Processed",
                R.drawable.dummy_image
            )
        )
    }

    private fun createPC(): ArrayList<Cat> {
        return arrayListOf<Cat>(
            Cat(
                "Knet",
                R.drawable.dummy_image
            ),
            Cat(
                "Quick Pay",
                R.drawable.dummy_image
            ),
            Cat(
                "Go Tap",
                R.drawable.dummy_image
            ),
            Cat(
                "COD",
                R.drawable.dummy_image
            )
        )
    }

    private fun createPD(): ArrayList<Cat> {
        return arrayListOf<Cat>(
            Cat(
                "Today",
                R.drawable.dummy_image
            ),
            Cat(
                "Yesterday",
                R.drawable.dummy_image
            ),
            Cat(
                "Last 7 Days",
                R.drawable.dummy_image
            ),
            Cat(
                "Last 30 Days",
                R.drawable.dummy_image
            ),
            Cat(
                "This Month",
                R.drawable.dummy_image
            ),
            Cat(
                "Last Month",
                R.drawable.dummy_image
            ),
            Cat(
                "Custom Range",
                R.drawable.dummy_image
            )
        )
    }

    private fun manageSearch() {
        binding.editSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(
                s: CharSequence?,
                start: Int,
                count: Int,
                after: Int
            ) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable?) {
                searchRunnable?.let { searchHandler.removeCallbacks(it) }
                searchRunnable = Runnable {
                    searchValue = s.toString()
                    callOrderListApi()
                }
                searchHandler.postDelayed(searchRunnable!!, 500)
            }
        })

    }

    private fun setUpPagination() {
        binding.recyclerList.isNestedScrollingEnabled = false
        scrollListener = ViewTreeObserver.OnScrollChangedListener {
            if (_binding == null) return@OnScrollChangedListener

            val view =
                binding.nestedScrollView.getChildAt(binding.nestedScrollView.childCount - 1) as View
            val diff: Int =
                view.bottom - (binding.nestedScrollView.height + binding.nestedScrollView.scrollY)
            if (diff == 0) {
                if (viewModel.isLastPageOrder()) {
                    // No action for the last page
                } else {
                    viewModel.currentPageOrder++
                    viewModel.loadNextPageOrder(
                        searchValue,
                        order_type,
                        area,
                        zones,
                        payment_status,
                        payment_change,
                        payment_method,
                        delivery_status,
                        delivery_boy,
                        date,
                        collected_by_seller,
                        collected_by,
                         "",
                         ""
                    )

                    if (binding.progressBar.visibility == View.VISIBLE) {
                        binding.progressBarSmall.visibility = View.GONE
                    } else {
                        binding.progressBarSmall.visibility = View.VISIBLE
                    }
                }
            }
        }

        binding.nestedScrollView.viewTreeObserver.addOnScrollChangedListener(scrollListener)

        // Clean up the listener when the view is destroyed
        viewLifecycleOwner.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                binding.nestedScrollView.viewTreeObserver.removeOnScrollChangedListener(
                    scrollListener
                )
            }
        })
    }

    private fun setUpRecyclerView() {
        orderAdapter = OrderListAdapter(
            mutableListOf(),
            { product, type ->
                if (type == "view") {
                    AppPreferences.getInstance(requireContext()).saveOrderID(product?.id)
                    findNavController().navigate(R.id.nav_order_details)
                } else if (type == "return") {
                    findNavController().navigate(R.id.nav_sell_return)
                }
            },
            { product, status ->
                updateDeliveryStatus(product?.id.toString(), status)
            }
        )


        binding.recyclerList.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            adapter = orderAdapter
        }
    }

    private fun updateDeliveryStatus(id: String, status: String) {

        val request = ApiService.UpdateDeliveryStatusRequest(
            id = id,
            status = status
        )
        binding.progressBar.visibility = View.VISIBLE
        viewModel.updateDeliveryStatus(
            AppPreferences.getInstance(requireContext()).getToken().toString(), request
        )
    }

    private fun setViewModel() {
        val appPreferences = AppPreferences.getInstance(requireContext())
        viewModel = ViewModelProvider(
            this,
            HomeViewModelFactory(requireContext())
        ).get(HomeViewModel::class.java)
        viewLifecycleOwner.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                _binding = null
            }
        })
        viewModel.loadingState.observe(viewLifecycleOwner) { isLoading ->

            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.INVISIBLE

        }
        viewModel.showAlertEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE


        }
        viewModel.updatePaymentStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()

        }
        viewModel.productEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE

        }
        viewModel.deliveryBoyData.observe(viewLifecycleOwner) { data ->

            manageFilterDeliveryBoys(data)


        }
        viewModel.orderListData.observe(viewLifecycleOwner) { data ->

            try {
                val totalPages = data?.totalPages
                if (!totalPages.isNullOrEmpty()) {
                    viewModel.totalPageCountOrder = totalPages.toInt()
                } else {
                    // Handle the case where totalPages is empty or null
                }
            } catch (e: NumberFormatException) {
                // Handle the exception if totalPages is still an invalid format
            }
            if (viewModel.currentPageOrder == 1) {

                manageSales(data)
            } else {
                orderAdapter.addOrder(data?.orders!!)
            }

        }


        viewModel.getDeliveryBoys(appPreferences.getToken().toString())

    }

    private fun manageFilterDeliveryBoys(data: List<DeliveryBoyResponse>?) {
        binding.pageFilter.recyclerDeliveryStaff.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)

            val catAdapter = FilterDeliveryBoyAdapter(data) { data, position ->

                delivery_boy = data?.id.toString()
                binding.pageFilter.txtFilterDeliveryStaff.text = data?.name
                closeDeliveryBoy()
            }
            adapter = catAdapter
        }
    }

    private fun closeDeliveryBoy() {
        binding.pageFilter.imgDeliveryStaffArrow.rotation = 0f
        binding.pageFilter.cardFilterDeliveryStaff.visibility = View.GONE
    }

    private fun callOrderListApi() {
        binding.progressBar.visibility = View.VISIBLE
        viewModel.currentPageOrder = 1
        viewModel.clearOrderData()
        val request = ApiService.OrderListRequest(
            area = area,
            per_page = "3",
            page_no = "1",
            search = searchValue,
            date = date,
            order_type = order_type,
            collected_by_seller = collected_by_seller,
            zones = zones,
            payment_change = payment_change,
            payment_method = payment_method,
            payment_status = payment_status,
            collected_by = collected_by,
            delivery_boy = delivery_boy,
            delivery_status = delivery_status,
            userId = "",
            IsPrescription = "0"
        )
        viewModel.getOrderList(
            AppPreferences.getInstance(requireContext()).getToken().toString(),
            request
        )
    }

    private fun handleClick() {
        binding.cardFilter.setOnClickListener {
            binding.pageFilter.lytFilter.visibility = View.VISIBLE
        }
        binding.pageFilter.imgClose.setOnClickListener {
            binding.pageFilter.lytFilter.visibility = View.GONE
        }

        binding.imgBack.setOnClickListener {
            findNavController().popBackStack()
        }

    }

    private fun manageSales(data: OrderListResponse) {
        binding.recyclerList.visibility = View.VISIBLE
        orderAdapter.setOrder(data?.orders?.toMutableList() ?: mutableListOf())
    }


}