package com.voxel.wasfaadminapp.ui.splash

import androidx.appcompat.app.AppCompatActivity
import android.annotation.SuppressLint
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.MotionEvent
import android.view.View
import android.view.WindowInsets
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.voxel.wasfaadminapp.databinding.ActivitySplashBinding
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.helper.AppPreferences
import com.voxel.wasfaadminapp.ui.doctor.main.DoctorHomeActivity
import com.voxel.wasfaadminapp.ui.login.LoginActivity
import com.voxel.wasfaadminapp.ui.main.MainActivity


class SplashActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySplashBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val window = window
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.statusBarColor = ContextCompat.getColor(this, R.color.white)
        }

        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)


        handleTimer()
    }

    private fun handleTimer() {
        Handler(Looper.getMainLooper()).postDelayed({
            manageNavigation()
        }, 2000)
    }

    private fun manageNavigation() {

        val appPreferences = AppPreferences.getInstance(this)

        if (appPreferences.getToken() == null){

            val intent = Intent(this, LoginActivity::class.java)
            startActivity(intent)
            overridePendingTransition(0, 0)
            finish()
        }else {

            if(appPreferences.getLoginType() == "influencer"){
                val intent = Intent(this, DoctorHomeActivity::class.java)
                startActivity(intent)
                overridePendingTransition(0, 0)
                finish()
            }else{
                val intent = Intent(this, MainActivity::class.java)
                startActivity(intent)
                overridePendingTransition(0, 0)
                finish()
            }

        }

    }


}