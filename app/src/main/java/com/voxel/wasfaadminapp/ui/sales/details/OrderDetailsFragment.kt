package com.voxel.wasfaadminapp.ui.sales.details

import android.app.AlertDialog
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.ImageView
import android.widget.RadioGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.card.MaterialCardView
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.FragmentOrderDetailsBinding
import com.voxel.wasfaadminapp.helper.AppPreferences
import com.voxel.wasfaadminapp.network.ApiService
import com.voxel.wasfaadminapp.network.response.DeliveryBoys
import com.voxel.wasfaadminapp.network.response.InfluencerListResponse
import com.voxel.wasfaadminapp.network.response.ItemsList
import com.voxel.wasfaadminapp.network.response.OrderDetailsResponse
import com.voxel.wasfaadminapp.ui.home.model.Cat
import com.voxel.wasfaadminapp.ui.sales.adapter.DeliveryBoysAdapter
import com.voxel.wasfaadminapp.ui.sales.adapter.InfluencerAdapter
import com.voxel.wasfaadminapp.ui.sales.adapter.OrderDetailsAdapter
import com.voxel.wasfaadminapp.viewmodel.HomeViewModel
import com.voxel.wasfaadminapp.viewmodel.HomeViewModelFactory


class OrderDetailsFragment : Fragment() {
    private var _binding: FragmentOrderDetailsBinding? = null
    private val binding get() = _binding!!
    private lateinit var viewModel: HomeViewModel
    private var deliveryBoys: List<DeliveryBoys>? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentOrderDetailsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        handleClick()
        setViewModel()
        callOrderDetailsApi()
    }

    private fun callOrderDetailsApi() {
        val appPreferences = AppPreferences.getInstance(requireContext())
        val request = ApiService.CatDetailsRequest(
            id = appPreferences.getOrderID().toString()
        )
        viewModel.getOrderDetails(appPreferences.getToken().toString(), request)
    }

    private fun setViewModel() {
        val appPreferences = AppPreferences.getInstance(requireContext())
        viewModel = ViewModelProvider(
            this,
            HomeViewModelFactory(requireContext())
        ).get(HomeViewModel::class.java)
        viewLifecycleOwner.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                _binding = null
            }
        })
        viewModel.loadingState.observe(viewLifecycleOwner) { isLoading ->

            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.INVISIBLE

        }
        viewModel.showAlertEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE


        }
        viewModel.productEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE

        }
        viewModel.updatePaymentStatus.observe(viewLifecycleOwner) { message ->
            callOrderDetailsApi()

        }
        viewModel.orderDetailsData.observe(viewLifecycleOwner) { data ->
            binding.progressBar.visibility = View.GONE
            manageData(data)
        }
        viewModel.influencerList.observe(viewLifecycleOwner) { data ->
            binding.progressBar.visibility = View.GONE
            showAssignInfluencerDialog(data)
        }



        binding.progressBar.visibility = View.VISIBLE

    }
    private fun showDeliveryBoyDialog() {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_assign_influencer, null)

        val txtHead = dialogView.findViewById<TextView>(R.id.txt_heading)
        val txtNoData = dialogView.findViewById<TextView>(R.id.txt_no_data)
        val imgClose = dialogView.findViewById<ImageView>(R.id.img_close)
        val recyclerView = dialogView.findViewById<RecyclerView>(R.id.recycler_influencers)
        val btnChange = dialogView.findViewById<MaterialCardView>(R.id.card_change)

        txtHead.text = "Assign Delivery Staff"

        if (deliveryBoys.isNullOrEmpty()){
            txtNoData.visibility = View.VISIBLE
        }else{
            txtNoData.visibility = View.GONE
        }


        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setCancelable(true)
            .create()

        var selectedInfluencer: DeliveryBoys? = null

        fun setButtonEnabled(enabled: Boolean) {
            btnChange.isEnabled = enabled
            btnChange.alpha = if (enabled) 1f else 0.5f
        }
        imgClose.setOnClickListener {
            dialog.dismiss()
        }

        setButtonEnabled(false)
        val currentAssigned = binding.txtDeliveryStaff.text.toString().trim()

        val adapter = DeliveryBoysAdapter(
            deliveryBoys ?: emptyList(),
            onItemSelected = { influencer ->
                selectedInfluencer = influencer
                setButtonEnabled(true)
            },
            preSelectedName = currentAssigned
        )


        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        recyclerView.adapter = adapter

        btnChange.setOnClickListener {
            if (!btnChange.isEnabled) return@setOnClickListener

            selectedInfluencer?.let {
                dialog.dismiss()
                callAssignDeliveryBoyApi(it)
            }
        }

        dialog.show()

    }

    private fun callAssignDeliveryBoyApi(it: DeliveryBoys) {
        val request = ApiService.AssignDeliveryBoyRequest(
            id = AppPreferences.getInstance(requireContext()).getOrderID().toString(),
            deliveryBoy = it?.id.toString()
        )
        binding.progressBar.visibility = View.VISIBLE
        viewModel.assignDeliveryBoy(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
    }

    private fun showAssignInfluencerDialog(data: List<InfluencerListResponse>?) {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_assign_influencer, null)

        val imgClose = dialogView.findViewById<ImageView>(R.id.img_close)
        val recyclerView = dialogView.findViewById<RecyclerView>(R.id.recycler_influencers)
        val btnChange = dialogView.findViewById<MaterialCardView>(R.id.card_change)

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setCancelable(true)
            .create()

        var selectedInfluencer: InfluencerListResponse? = null

        fun setButtonEnabled(enabled: Boolean) {
            btnChange.isEnabled = enabled
            btnChange.alpha = if (enabled) 1f else 0.5f
        }
        imgClose.setOnClickListener {
            dialog.dismiss()
        }

        setButtonEnabled(false)
        val currentAssigned = binding.txtAssignInfluencer.text.toString().trim()

        val adapter = InfluencerAdapter(
            data ?: emptyList(),
            onItemSelected = { influencer ->
                selectedInfluencer = influencer
                setButtonEnabled(true)
            },
            preSelectedName = currentAssigned
        )


        recyclerView.layoutManager = LinearLayoutManager(requireContext())
        recyclerView.adapter = adapter

        btnChange.setOnClickListener {
            if (!btnChange.isEnabled) return@setOnClickListener

            selectedInfluencer?.let {
                dialog.dismiss()
                callAssignInfluencerApi(it)
            }
        }

        dialog.show()

    }

    private fun callAssignInfluencerApi(it: InfluencerListResponse) {
        val request = ApiService.AssignInfluencerStatusRequest(
            id = AppPreferences.getInstance(requireContext()).getOrderID().toString(),
            influencerId = it?.id.toString()
        )
        binding.progressBar.visibility = View.VISIBLE
        viewModel.assignInfluencer(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
    }

    private fun manageData(data: List<OrderDetailsResponse>?) {
        val orderData = data?.get(0)
        binding.txtDeliveryStatus.text = orderData?.deliveryStatus
        binding.txtPaymentStatus.text = orderData?.paymentStatus
        binding.txtLead.text = orderData?.lead
        binding.txtAssignInfluencer.text = orderData?.influencerName
        binding.txtDeliveryStaff.text = orderData?.deleiveryBoyName

        manageProduct(orderData?.itemsList)
        deliveryBoys = orderData?.deliveryBoys

        orderData?.deliveryBoys

        binding.txtOrderId.text = "Order " + orderData?.code
        binding.txtSubTotal.text = orderData?.grandTotal
        binding.txtCancelledTotal.text = "KD 0.000"
        binding.txtTotal.text = orderData?.grandTotal


        binding.txtAddress.text =
            listOf(orderData?.address?.governorate, orderData?.address?.areaName)
                .filterNotNull()
                .joinToString(", ")

        binding.txtAddress.text =
            listOf(orderData?.address?.street, orderData?.address?.building)
                .filterNotNull()
                .joinToString(", ")


        binding.txtOrderDate.text = orderData?.date

    }

    private fun handleClick() {
        binding.imgBack.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.cardAssignDeliveryStaff.setOnClickListener {
            showDeliveryBoyDialog()
        }
        binding.cardChangeDeliveryStatus.setOnClickListener {
            showDeliveryStatusDialog()
        }
        binding.cardChangePaymentStatus.setOnClickListener {
            showPaymentStatusDialog()
        }
        binding.cardChangeLead.setOnClickListener {
            showLeadDialog()
        }
        binding.cardAssignInfluencer.setOnClickListener {
            binding.progressBar.visibility = View.VISIBLE
            viewModel.getInfluencerList(AppPreferences.getInstance(requireContext()).getToken().toString())

        }
    }
    private fun showDeliveryStatusDialog() {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_change_delivery, null)

        val imgClose = dialogView.findViewById<ImageView>(R.id.img_close)
        val radioGroup = dialogView.findViewById<RadioGroup>(R.id.radio_group_status)
        val btnChange = dialogView.findViewById<MaterialCardView>(R.id.card_change)

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setCancelable(true)
            .create()

        fun setButtonEnabled(enabled: Boolean) {
            btnChange.isEnabled = enabled
            btnChange.alpha = if (enabled) 1f else 0.5f
        }

        setButtonEnabled(false)

        imgClose.setOnClickListener {
            dialog.dismiss()
        }

        // 🔷 Pre-select based on txtDeliveryStatus
        val currentStatus = binding.txtDeliveryStatus.text.toString().trim()

        val checkedId = when (currentStatus) {
            "Pending"      -> R.id.radio_pending
            "Confirmed"    -> R.id.radio_confirmed
            "Picked Up"    -> R.id.radio_picked_up
            "On The Way"   -> R.id.radio_on_the_way
            "Delivered"    -> R.id.radio_delivered
            "Cancel"       -> R.id.radio_cancel
            "Closed"       -> R.id.radio_closed
            else           -> -1
        }

        if (checkedId != -1) {
            radioGroup.check(checkedId)
            setButtonEnabled(true)
        }

        radioGroup.setOnCheckedChangeListener { _, id ->
            setButtonEnabled(id != -1)
        }

        btnChange.setOnClickListener {
            if (!btnChange.isEnabled) return@setOnClickListener

            val selectedStatus = when (radioGroup.checkedRadioButtonId) {
                R.id.radio_pending -> "Pending"
                R.id.radio_confirmed -> "Confirmed"
                R.id.radio_picked_up -> "Picked Up"
                R.id.radio_on_the_way -> "On The Way"
                R.id.radio_delivered -> "Delivered"
                R.id.radio_cancel -> "Cancel"
                R.id.radio_closed -> "Closed"
                else -> null
            }

            selectedStatus?.let {
                dialog.dismiss()
                callUpdateStatusApi(it)
            }
        }

        dialog.show()
    }
    private fun showPaymentStatusDialog() {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_payment_status, null)

        val imgClose = dialogView.findViewById<ImageView>(R.id.img_close)
        val radioGroup = dialogView.findViewById<RadioGroup>(R.id.radio_group_status)
        val btnChange = dialogView.findViewById<MaterialCardView>(R.id.card_change)

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setCancelable(true)
            .create()

        fun setButtonEnabled(enabled: Boolean) {
            btnChange.isEnabled = enabled
            btnChange.alpha = if (enabled) 1f else 0.5f
        }

        setButtonEnabled(false)

        imgClose.setOnClickListener {
            dialog.dismiss()
        }

        // 🔷 Pre-select based on txtDeliveryStatus
        val currentStatus = binding.txtPaymentStatus.text.toString().trim().lowercase().replace("-", "")


        val checkedId = when (currentStatus) {
            "unpaid"      -> R.id.radio_un_paid
            "processed"    -> R.id.radio_processed
            "paid"    -> R.id.radio_paid
            else           -> -1
        }

        if (checkedId != -1) {
            radioGroup.check(checkedId)
            setButtonEnabled(true)
        }

        radioGroup.setOnCheckedChangeListener { _, id ->
            setButtonEnabled(id != -1)
        }

        btnChange.setOnClickListener {
            if (!btnChange.isEnabled) return@setOnClickListener

            val selectedStatus = when (radioGroup.checkedRadioButtonId) {
                R.id.radio_un_paid -> "unpaid"
                R.id.radio_processed -> "processed"
                R.id.radio_paid -> "paid"
                else -> null
            }

            selectedStatus?.let {
                dialog.dismiss()
                callUpdateStatusApi(it)
            }
        }

        dialog.show()
    }
    private fun showLeadDialog() {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_lead, null)

        val imgClose = dialogView.findViewById<ImageView>(R.id.img_close)
        val radioGroup = dialogView.findViewById<RadioGroup>(R.id.radio_group_status)
        val btnChange = dialogView.findViewById<MaterialCardView>(R.id.card_change)

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setCancelable(true)
            .create()

        fun setButtonEnabled(enabled: Boolean) {
            btnChange.isEnabled = enabled
            btnChange.alpha = if (enabled) 1f else 0.5f
        }

        setButtonEnabled(false)

        imgClose.setOnClickListener {
            dialog.dismiss()
        }

        // 🔷 Pre-select based on txtDeliveryStatus
        val currentStatus = binding.txtLead.text.toString().trim()

        val checkedId = when (currentStatus) {
            "social_media"      -> R.id.radio_social_media
            "prescription"    -> R.id.radio_prescription
            "gift"    -> R.id.radio_gift
            else           -> -1
        }

        if (checkedId != -1) {
            radioGroup.check(checkedId)
            setButtonEnabled(true)
        }

        radioGroup.setOnCheckedChangeListener { _, id ->
            setButtonEnabled(id != -1)
        }

        btnChange.setOnClickListener {
            if (!btnChange.isEnabled) return@setOnClickListener

            val selectedStatus = when (radioGroup.checkedRadioButtonId) {
                R.id.radio_social_media -> "social_media"
                R.id.radio_prescription -> "prescription"
                R.id.radio_gift -> "gift"
                else -> null
            }

            selectedStatus?.let {
                dialog.dismiss()
                callUpdateLeadApi(it)
            }
        }

        dialog.show()
    }

    private fun callUpdateLeadApi(it: String) {
        val request = ApiService.UpdateLeadRequest(
            id = AppPreferences.getInstance(requireContext()).getOrderID().toString(),
            lead = it
        )
        binding.progressBar.visibility = View.VISIBLE
        viewModel.updateLead(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
    }

    private fun callUpdateStatusApi(deliveryStatus: String) {

        val request = ApiService.UpdateDeliveryStatusRequest(
            id = AppPreferences.getInstance(requireContext()).getOrderID().toString(),
            status = deliveryStatus
        )
        binding.progressBar.visibility = View.VISIBLE
        viewModel.updatePaymentStatus(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
    }

    private fun manageProduct(itemsList: List<ItemsList>?) {

        binding.recyclerProduct.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)

            val catAdapter = OrderDetailsAdapter(itemsList) { data, position ->
                findNavController().navigate(R.id.nav_order_details)
            }
            adapter = catAdapter
        }
    }

}