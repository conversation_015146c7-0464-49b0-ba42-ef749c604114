package com.voxel.wasfaadminapp.ui.doctor.pres.add

import android.app.DatePickerDialog
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.card.MaterialCardView
import com.voxel.wasfaadminapp.R
import com.voxel.wasfaadminapp.databinding.FragmentAddPresBinding
import com.voxel.wasfaadminapp.helper.AppPreferences
import com.voxel.wasfaadminapp.network.ApiService
import com.voxel.wasfaadminapp.network.response.CartItem
import com.voxel.wasfaadminapp.network.response.CustomerListResponse
import com.voxel.wasfaadminapp.network.response.PatientInfo
import com.voxel.wasfaadminapp.ui.doctor.main.DoctorHomeActivity
import com.voxel.wasfaadminapp.ui.doctor.pres.adapter.MedicationListAdapter
import com.voxel.wasfaadminapp.ui.home.model.Cat
import com.voxel.wasfaadminapp.viewmodel.HomeViewModel
import com.voxel.wasfaadminapp.viewmodel.HomeViewModelFactory
import java.util.Calendar


class AddPresFragment : Fragment() {
    private var _binding: FragmentAddPresBinding? = null
    private val binding get() = _binding!!
    var patientId = ""
    private lateinit var viewModel: HomeViewModel
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentAddPresBinding.inflate(inflater, container, false)
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        manageClick()

        handleEditTexts()
        setViewModel()
    }
    fun isTablet(): Boolean {
        val metrics = resources.displayMetrics
        val widthDp = metrics.widthPixels / metrics.density

        return widthDp >= 600 ||
                resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
    }
    private fun setViewModel() {
        if (!isTablet()){
            (activity as? DoctorHomeActivity)?.showBottomNav()
        }else{
            (activity as? DoctorHomeActivity)?.hideBottomNav()
        }
        val appPreferences = AppPreferences.getInstance(requireContext())
        viewModel = ViewModelProvider(
            this,
            HomeViewModelFactory(requireContext())
        ).get(HomeViewModel::class.java)

        viewLifecycleOwner.lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
            fun onDestroy() {
                _binding = null
            }
        })
        viewModel.loadingState.observe(viewLifecycleOwner) { isLoading ->

            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.INVISIBLE

        }
        viewModel.showAlertEvent.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE

        }
        viewModel.createdStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            if (message == "Patient saved successfully"){
                binding.rltBtn.visibility = View.GONE
            }else{
                binding.rltBtn.visibility = View.VISIBLE
            }
            Toast.makeText(requireContext(),message, Toast.LENGTH_LONG).show()
            //56567876

        }
        viewModel.deleteCartShopStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.VISIBLE
            println("me =--------------")
            viewModel.getCart(appPreferences.getToken().toString())

        }
        viewModel.patientIdStatus.observe(viewLifecycleOwner) { patient_id ->
            binding.progressBar.visibility = View.GONE
            patientId = patient_id

        }
        viewModel.addCartShopStatus.observe(viewLifecycleOwner) { message ->
            binding.progressBar.visibility = View.GONE
            Toast.makeText(requireContext(),message, Toast.LENGTH_LONG).show()
            viewModel.getCartCount(appPreferences.getToken().toString())

        }


        viewModel.cartList.observe(viewLifecycleOwner) { data ->
            println("--------------------------")
            binding.progressBar.visibility = View.GONE

            manageCart(data?.cartItems)
           managePatient(data?.patientInfo)

        }

        binding.progressBar.visibility = View.VISIBLE
        viewModel.getCart(appPreferences.getToken().toString())
    }
    private fun manageCart(cartItems: List<CartItem>?) {

        if (cartItems.isNullOrEmpty()) {
            binding.cardCreate.visibility = View.GONE
            binding.recyclerMed.visibility = View.GONE
        } else {
            binding.recyclerMed.visibility = View.VISIBLE
            binding.cardCreate.visibility = View.VISIBLE
            binding.txtNoData.visibility = View.GONE
            binding.recyclerMed.apply {
                layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
                val catAdapter = MedicationListAdapter(cartItems) { data, type ->

                    binding.progressBar.visibility = View.VISIBLE
                    val request = ApiService.CartRemoveRequest(
                        id = data?.id.toString()
                    )
                    viewModel.deleteCartShop(
                        AppPreferences.getInstance(requireContext()).getToken().toString(),
                        request
                    )
                }
                adapter = catAdapter
            }
        }

    }
    private fun managePatient(data: List<PatientInfo>?) {
        if (data.isNullOrEmpty()){
            binding.editCivilId.setText(null)
            binding.editPatientName.setText(null)
            binding.editPhone.setText(null)
            binding.editEmail.setText(null)
            binding.txtDate.setText(null)
        }else{

            patientId = data?.get(0)?.id.toString()
            binding.rltBtn.visibility = View.VISIBLE
            binding.cardCreatePatiant.visibility = View.GONE
            binding.cardUpdate.visibility = View.VISIBLE

            binding.editCivilId.setText(data.get(0)?.civil_id)
            binding.editPatientName.setText(data.get(0)?.name)
            binding.editEmail.setText(data.get(0)?.email)
            binding.txtDate.setText(data.get(0)?.dob)
            binding.editPhone.setText(data.get(0)?.phone)

        }
    }
    private fun manageCustomer(data: CustomerListResponse?) {


        if (data?.users.isNullOrEmpty()){
            binding.cardCustBox.strokeColor = Color.parseColor("#80A61C5C")
            binding.rltBtn.visibility = View.VISIBLE
            binding.cardCreatePatiant.visibility = View.VISIBLE
            binding.cardUpdate.visibility = View.GONE
        }else{
            binding.cardCustBox.strokeColor = Color.parseColor("#8000BCD7")
            patientId = data?.users?.get(0)?.id.toString()
            binding.rltBtn.visibility = View.VISIBLE
            binding.cardCreatePatiant.visibility = View.GONE
            binding.cardUpdate.visibility = View.VISIBLE

            binding.editCivilId.setText(data?.users?.get(0)?.civilId)
            binding.editPatientName.setText(data?.users?.get(0)?.name)
            binding.editEmail.setText(data?.users?.get(0)?.email)
            binding.txtDate.setText(data?.users?.get(0)?.dob)

        }
    }
    private fun handleEditTexts() {
        binding.editPhone.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtPhoneEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
                val phoneNumber = s.toString()
                if (phoneNumber.length == 8) {
                    checkCustomerExistCall(phoneNumber)
                }
            }
        })
        binding.editCivilId.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtCivilEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.editPatientName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtNameEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        binding.editEmail.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                binding.txtEmailEmpty.visibility = View.GONE
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
    }

    private fun checkCustomerExistCall(phoneNumber: String) {

        binding.progressBar.visibility = View.VISIBLE
        val appPreferences = AppPreferences.getInstance(requireContext())
        val request = ApiService.CheckPatientRequest(
            keyword = "+965$phoneNumber"
        )
        viewModel.checkCustomerExist(appPreferences.getToken().toString(),request)
        viewModel.customerListData.observe(viewLifecycleOwner) { data ->
            binding.progressBar.visibility = View.GONE

            manageCustomer(data)
        }


    }
    private fun showDatePicker() {
        val calendar = Calendar.getInstance()
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        val datePickerDialog = DatePickerDialog(requireContext(), { _, selectedYear, selectedMonth, selectedDay ->
            val formattedDate = String.format("%02d-%02d-%04d", selectedDay, selectedMonth + 1, selectedYear)
            binding.txtDate.text = formattedDate
            binding.txtDobEmpty.visibility = View.GONE
        }, year, month, day)

        datePickerDialog.show()
    }
    private fun manageClick() {

        binding.imgMenu.setOnClickListener {
            (activity as? DoctorHomeActivity)?.toggleBottomNav()
        }
        binding.txtCancel.setOnClickListener {
            parentFragmentManager.popBackStack()
        }
        binding.cardCalendar.setOnClickListener {
            showDatePicker()
        }
        binding.cardBack.setOnClickListener {
            parentFragmentManager.popBackStack()
        }
        binding.cardCreate.setOnClickListener {
            parentFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, PresReviewFragment())
                .addToBackStack(null)
                .commit()
        }
        binding.cardAddNewMed.setOnClickListener {
            if (patientId == ""){
                showAlertCustom("Please Add Patient")
            }else{
                AppPreferences.getInstance(requireContext()).savePatientId(patientId)
                parentFragmentManager.beginTransaction()
                    .replace(R.id.fragment_container, AddMedFragment())
                    .addToBackStack(null)
                    .commit()
            }
        }
        binding.cardCreatePatiant.setOnClickListener {
            validateFields("create")
        }

        binding.cardUpdate.setOnClickListener {
            validateFields("update")
        }
    }
    private fun validateFields(type: String) {
        if (binding.editPhone.text.isEmpty()){
            binding.txtPhoneEmpty.visibility = View.VISIBLE
        }else if (binding.editPatientName.text.isEmpty()){
            binding.txtNameEmpty.visibility = View.VISIBLE
        }else{

            if (type == "create"){
                callCreatePatientApi()
            }else{
                callUpdatePatientApi()
            }

        }
    }
    private fun callUpdatePatientApi() {

        binding.progressBar.visibility = View.VISIBLE
        val request = ApiService.CreatePatientRequest(
            email = binding.editEmail.text.toString(),
            phone ="+965"+ binding.editPhone.text.toString(),
            name = binding.editPatientName.text.toString(),
            dob = binding.txtDate.text.toString(),
            altMobileNo = "",
            civilId = binding.editCivilId.text.toString(),
            id = patientId
        )
        viewModel.createPatient(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
    }
    private fun callCreatePatientApi() {

        binding.progressBar.visibility = View.VISIBLE
        val request = ApiService.CreatePatientRequest(
            email = binding.editEmail.text.toString(),
            phone = "+965"+binding.editPhone.text.toString(),
            name = binding.editPatientName.text.toString(),
            dob = binding.txtDate.text.toString(),
            altMobileNo ="",
            civilId = binding.editCivilId.text.toString(),
            id = ""
        )
        viewModel.createPatient(AppPreferences.getInstance(requireContext()).getToken().toString(),request)
    }
    fun showAlertCustom(message: String) {
        val builder = AlertDialog.Builder(requireContext())


        // set the custom layout
        val customLayout: View = layoutInflater.inflate(R.layout.validation_alert, null)
        builder.setView(customLayout)
        val text_validation = customLayout.findViewById<TextView>(R.id.text_validation)
        text_validation.text = message
        val continueShoppingButton = customLayout.findViewById<MaterialCardView>(R.id.view_cart)
        lateinit var dialog: AlertDialog
        continueShoppingButton.setOnClickListener {
            if (message == "Internal Server Error"){
                findNavController().popBackStack()
                dialog?.dismiss()
            }else{
                dialog?.dismiss()
            }

        }

        dialog = builder.create()
        dialog.show()
    }

}