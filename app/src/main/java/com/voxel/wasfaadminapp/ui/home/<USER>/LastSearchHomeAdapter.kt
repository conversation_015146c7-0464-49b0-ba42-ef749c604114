package com.voxel.wasfaadminapp.ui.home.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.voxel.wasfaadminapp.databinding.ItemBestSellingProductBinding
import com.voxel.wasfaadminapp.databinding.ItemLastSearchHomeBinding
import com.voxel.wasfaadminapp.databinding.ItemRecentOrdersHomeBinding
import com.voxel.wasfaadminapp.ui.home.model.Cat

class LastSearchHomeAdapter(
    private val CatList: List<Cat>,
    private val listener: (Cat, Int) -> Unit
) :
    RecyclerView.Adapter<LastSearchHomeAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = ItemLastSearchHomeBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindItem(CatList!![position])

    }

    override fun getItemCount(): Int {
        return CatList!!.size
    }

    class ViewHolder(var itemBinding: ItemLastSearchHomeBinding) :
        RecyclerView.ViewHolder(itemBinding.root) {
        fun bindItem(cat: Cat) {

        }
    }
}