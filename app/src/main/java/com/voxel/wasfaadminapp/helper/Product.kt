package com.voxel.wasfaadminapp.helper

data class Product(
    var skuId: String = "",
    var skuName: String = "",
    var productName: String = "",
    var arabicProductName: String = "",
    var shortDesc: String = "",
    var arabicShortDesc: String = "",
    var dosageForm: String = "",
    var categoryId: String = "",
    var categoryName: String = "",
    var brandId: String = "",
    var brandName: String = "",
    var sellerName: String = "",
    var sellerId: String = "",
    var pickUpId: List<String> = listOf(),
    var pickUpName: List<String> = listOf(),
    var unitId: String = "",
    var unitName: String = "",
    var weight: String = "",
    var purchaseFormId: List<String> = listOf(),
    var purchaseFormName: List<String> = listOf(),
    var purchasePrice: String = "",
    var minPurchaseQty: String = "1",
    var maxPurchaseQty: String = "1",
    var tags: List<String> = listOf(),
    var arabicTags: List<String> = listOf(),
    var relatedProductsName: List<String> = listOf(),
    var relatedProductsId: List<String> = listOf(),
    var upSellingProductsName: List<String> = listOf(),
    var upSellingProductsId: List<String> = listOf(),
    var crossSellingProductsName: List<String> = listOf(),
    var crossSellingProductsId: List<String> = listOf(),
    var barcode: String = "",
    var refundable: String = "0",
    var isCancel: String = "0",
    var isPharma: String = "0",
    var expiryDate: String = "",
    var productStockType: String = "",
    var unitPrice: String = "",
    var discountDateRange: String = "",
    var discount: String = "",
    var discountType: String = "Flat",
    var sellerDiscount: String = "",
    var apixDiscount: String = "",
    var quantity: String = "",
    var sellerSku: String = "",
    var externalLink: String = "",
    var externalLinkButton: String = "",
    var photos: List<PhotoMetaData> = listOf(),
    var description: String = "",
    var arabicDescription: String = "",
    var thumbUrl: String = "",
    var thumbId: String = "",
    var thumbSize: String = "",
    var videoType: String = "Youtube",
    var videoLink: String = "",

    var metaTitla: String = "",
    var arabicMetaTitla: String = "",
    var metaDesc: String = "",
    var arabicMetaDesc: String = "",
    var metaImageId: String = "",
    var metaImageUrl: String = "",

    var shippingConfig: String = "",
    var lowStockQuantity: String = "",
    var stockVisibilityState: String = "0",
    var codStatus: String = "0",
    var featuredStatus: String = "0",
    var todaysDealStatus: String = "0",
    var flashDiscount: String = "",
    var flashDiscountType: String = ""

)

data class PhotoMetaData(
    val id: String,
    val name: String,
    val sizeInBytes: String,
    val fileUrl: String
)
