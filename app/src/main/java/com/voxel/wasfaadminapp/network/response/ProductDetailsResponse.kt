package com.voxel.wasfaadminapp.network.response
data class ProductDetailsResponse(
    val id: String,
    val name: String,
    val arabic_name: String,
    val scientific_name: String,
    val seller: String,
    val thumbnailImage: String,
    val hasDiscount: String,
    val discount: String,
    val strikedPrice: String,
    val unitPrice: String,
    val currentStock: String,
    val rating: String,
    val description: String,
    val shortDescription: String,
    val arabic_short_description: String,
    val brand: String,
    val photos: List<PhotosList>,
    val apix_sku: String,
    val usage_form: String,
    val category_id: String,
    val brand_id: String,
    val origin_id: String,
    val seller_id: String,
    val unit: String,
    val weight: String,
    val purchase_from: List<String>,
    val purchase_price: String,
    val min_qty: String,
    val max_qty: String,
    val tags: String,
    val arabic_tags: String,
    val relatedProducts: List<Products>,
    val upSellProducts: List<Products>,
    val crossSellProducts: List<Products>,
    val barcode: String,
    val refundable: String,
    val cancelable: String,
    val is_pharmaceutical: String,
    val on_request: String,
    val expiry_date: String,
    val product_level: String,
    val discount_start_date: String,
    val discount_end_date: String,
    val discount_type: String,
    val seller_discount: String,
    val apix_discount: String,
    val max_discount: String,
    val sku: String,
    val current_stock: String,
    val external_link: String,
    val external_link_btn: String,
    val arabic_description: String,
    val video_provider: String,
    val video_link: String,
    val pdf: String,
    val meta_title: String,
    val arabic_meta_title: String,
    val meta_description: String,
    val arabic_meta_description: String,
    val meta_img: String,
    val shipping_type: String,
    val flat_shipping_cost: String,
    val low_stock_quantity: String,
    val stock_visibility_state: String,
    val cash_on_delivery: String,
    val featured: String,
    val todays_deal: String,
    val flash_deal_id: String,
    val flash_discount: String,
    val flash_discount_type: String,

    val apix_sku_name: String,
    val category_name: String,
    val brand_name: String,
    val unit_name: String,
    val pick_up_points: List<PickUps>,
    val purchase_from_id: String,
    val product_stock_type: String,
    val thumbnailImageSize: String,
    val thumbnailImageId: String,
    val thumbnailImageName: String


)
data class ProductSaved(
    val id: String,
    val name: String
)
data class PickUpPoints(
    val id: String,
    val name: String
)
data class PhotosList(
    val path: String,
    val id: String,
    val name: String,
    val size: String
)

