package com.voxel.wasfaadminapp.network.response

data class HomeDataResponse (
    val topSellingProducts: List<TopSellProd>,
    val prescriptions: List<PresHome>,
    val totalPrescriptions: String,
    val mostlyPrescibed: String,
    val amount: String,
    val countOfSale: String,
    val pendingCommission: String,
    val completedPayment: String,
    val refundOrders: String,
    val refundAmount: String,
    val currentMonthAmount: String
)
data class PatientInfoHome(
    val id: String,
    val name: String,
    val phone: String,
    val email: String,
    val alt_phone: String?,
    val civil_id: String,
    val dob: String,
    val address: List<AddressItem>,
    val profilePic: String
)
data class PresHome(
    val id: String,
    val name: String,
    val medicationsPrescribed: String,
    val patientInfo: List<PatientInfoHome>,
)
data class TopSellProd(
    val id: String,
    val name: String,
    val sellerLogo: String,
    val thumbnail_image: String,
    val discount: String,
    val basePrice: String,
    val purchasePrice: String,
    val rating: String,
    val numberOfsales: String,
    val sellerDiscount: String,
    val sellerName: String,
    val sellerSku: String,
    val apixSku: String,
    val currentStock: String,
    val publishedOnWebsite: String,
    val Restricted: String,
    val publishedOnPos: String,
    val isPharma: String,
    val isClean: String,
    val cog: String,
    val apixMargin: String,
    val influencerrMargin: String
)