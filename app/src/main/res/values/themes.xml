<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.WasfaAdminApp" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
    </style>

    <style name="Theme.WasfaAdminApp" parent="Base.Theme.WasfaAdminApp" />

    <style name="Theme.WasfaAdminApp.Fullscreen" parent="Theme.WasfaAdminApp">
        <item name="android:actionBarStyle">@style/Widget.Theme.WasfaAdminApp.ActionBar.Fullscreen
        </item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@null</item>
    </style>
    <style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="background">@color/white</item>
    </style>

    <style name="BottomSheetDialogTheme" parent="Theme.Design.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style>
    <style name="ThemeOverlay.WasfaAdminApp.FullscreenContainer" parent="">
        <item name="fullscreenBackgroundColor">@color/white</item>
        <item name="fullscreenTextColor">@color/white</item>
    </style>
</resources>