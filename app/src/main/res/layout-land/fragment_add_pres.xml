<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="40dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_back"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:visibility="gone"
                    app:cardBackgroundColor="#F7F7F7"
                    app:cardCornerRadius="10dp">

                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center"
                        android:src="@drawable/brown_back" />
                </com.google.android.material.card.MaterialCardView>
                <LinearLayout
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <ImageView
                        android:visibility="visible"
                        android:id="@+id/img_menu"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/menu"/>
                <TextView
                    android:layout_marginStart="16dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center_vertical"
                    android:text="New Prescription"
                    android:textColor="@color/black"
                    android:textSize="22sp" />
                </LinearLayout>
            </LinearLayout>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="50dp"
                android:layout_marginBottom="100dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="20dp">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout

                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"

                            android:layout_weight="1"
                            android:orientation="vertical">
                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:id="@+id/card_cust_box"
                                android:layout_height="wrap_content"
                                android:elevation="2dp"
                                app:strokeWidth="1dp"
                                android:layout_marginTop="10dp"
                                android:layout_marginBottom="1dp"
                                android:layout_marginStart="1dp"
                                android:layout_marginEnd="1dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="10dp"
                                app:cardElevation="2dp">

                            <LinearLayout
                                android:layout_margin="15dp"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical">
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Patient Info"
                                android:textColor="@color/black"
                                android:textSize="20sp" />
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="30dp"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Phone Number"
                                android:textColor="@color/black"
                                android:textSize="14sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="53dp"
                                android:layout_marginTop="10dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="15dp"
                                app:strokeColor="#2B000000"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <ImageView
                                        android:layout_width="28dp"
                                        android:layout_height="28dp"
                                        android:src="@drawable/country_icon" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="10dp"
                                        android:fontFamily="@font/roboto_medium"
                                        android:text="+965"
                                        android:textColor="#C49C9C9C"
                                        android:textSize="12dp" />

                                    <View
                                        android:layout_width="1dp"
                                        android:layout_height="31dp"
                                        android:layout_marginStart="10dp"
                                        android:background="#36000000" />

                                    <EditText
                                        android:id="@+id/edit_phone"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_marginStart="20dp"
                                        android:layout_marginEnd="10dp"
                                        android:background="@android:color/transparent"
                                        android:fontFamily="@font/roboto_regular"
                                        android:hint="phone number"
                                        android:inputType="phone"
                                        android:textColor="@color/black"
                                        android:textSize="12dp" />
                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>
                            <TextView
                                android:visibility="gone"
                                android:id="@+id/txt_phone_empty"
                                android:layout_marginTop="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="20dp"
                                android:layout_marginEnd="10dp"
                                android:layout_gravity="center_vertical"
                                android:gravity="center_vertical"
                                android:background="@android:color/transparent"
                                android:hint="Please Enter Contact Number"
                                android:singleLine="true"
                                android:textColorHint="#FF0000"
                                android:textSize="12sp" />
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Patient Name"
                                android:textColor="@color/black"
                                android:textSize="14sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="53dp"
                                android:layout_marginTop="10dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="15dp"
                                app:strokeColor="#2B000000"
                                app:strokeWidth="1dp">

                                <EditText
                                    android:id="@+id/edit_patient_name"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="20dp"
                                    android:layout_marginEnd="10dp"
                                    android:background="@android:color/transparent"
                                    android:fontFamily="@font/roboto_regular"
                                    android:hint="patient name"
                                    android:textColor="@color/black"
                                    android:textSize="12dp" />
                            </com.google.android.material.card.MaterialCardView>
                            <TextView
                                android:visibility="gone"
                                android:id="@+id/txt_name_empty"
                                android:layout_marginTop="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="20dp"
                                android:layout_marginEnd="10dp"
                                android:layout_gravity="center_vertical"
                                android:gravity="center_vertical"
                                android:background="@android:color/transparent"
                                android:hint="Please Enter Full Name"
                                android:singleLine="true"
                                android:textColorHint="#FF0000"
                                android:textSize="12sp" />


                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Civil ID"
                                android:textColor="@color/black"
                                android:textSize="14sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="53dp"
                                android:layout_marginTop="10dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="15dp"
                                app:strokeColor="#2B000000"
                                app:strokeWidth="1dp">

                                <EditText
                                    android:id="@+id/edit_civil_id"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="20dp"
                                    android:layout_marginEnd="10dp"
                                    android:background="@android:color/transparent"
                                    android:fontFamily="@font/roboto_regular"
                                    android:hint="civil id"
                                    android:textColor="@color/black"
                                    android:textSize="12dp" />
                            </com.google.android.material.card.MaterialCardView>
                            <TextView
                                android:visibility="gone"
                                android:id="@+id/txt_civil_empty"
                                android:layout_marginTop="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="20dp"
                                android:layout_marginEnd="10dp"
                                android:layout_gravity="center_vertical"
                                android:gravity="center_vertical"
                                android:background="@android:color/transparent"
                                android:hint="Please Enter Civil Id"
                                android:singleLine="true"
                                android:textColorHint="#FF0000"
                                android:textSize="12sp" />
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Email"
                                android:textColor="@color/black"
                                android:textSize="14sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="53dp"
                                android:layout_marginTop="10dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="15dp"
                                app:strokeColor="#2B000000"
                                app:strokeWidth="1dp">

                                <EditText
                                    android:id="@+id/edit_email"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="20dp"
                                    android:layout_marginEnd="10dp"
                                    android:background="@android:color/transparent"
                                    android:fontFamily="@font/roboto_regular"
                                    android:hint="email"
                                    android:inputType="textEmailAddress"
                                    android:textColor="@color/black"
                                    android:textSize="12dp" />
                            </com.google.android.material.card.MaterialCardView>
                            <TextView
                                android:visibility="gone"
                                android:id="@+id/txt_email_empty"
                                android:layout_marginTop="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="20dp"
                                android:layout_marginEnd="10dp"
                                android:layout_gravity="center_vertical"
                                android:gravity="center_vertical"
                                android:background="@android:color/transparent"
                                android:hint="Please Enter Email"
                                android:singleLine="true"
                                android:textColorHint="#FF0000"
                                android:textSize="12sp" />
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Date"
                                android:textColor="@color/black"
                                android:textSize="14sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="53dp"
                                android:id="@+id/card_calendar"
                                android:layout_marginTop="10dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="15dp"
                                app:strokeColor="#2B000000"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/txt_date"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="20dp"
                                        android:layout_marginEnd="10dp"
                                        android:layout_weight="1"
                                        android:background="@android:color/transparent"
                                        android:fontFamily="@font/roboto_regular"
                                        android:gravity="center_vertical"
                                        android:hint="choose date"
                                        android:textColor="@color/black"
                                        android:textSize="12dp" />

                                    <ImageView
                                        android:layout_width="20dp"
                                        android:layout_height="20dp"
                                        android:layout_marginEnd="20dp"
                                        android:src="@drawable/calendar_new" />
                                </LinearLayout>

                            </com.google.android.material.card.MaterialCardView>
                            <TextView
                                android:visibility="gone"
                                android:id="@+id/txt_dob_empty"
                                android:layout_marginTop="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="20dp"
                                android:layout_marginEnd="10dp"
                                android:layout_gravity="center_vertical"
                                android:gravity="center_vertical"
                                android:background="@android:color/transparent"
                                android:hint="Please Choose DOB"
                                android:singleLine="true"
                                android:textColorHint="#FF0000"
                                android:textSize="12sp" />

                            <RelativeLayout
                                android:id="@+id/rlt_btn"
                                android:layout_marginTop="30dp"
                                android:visibility="gone"
                                android:layout_marginBottom="10dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">
                                <com.google.android.material.card.MaterialCardView
                                    android:layout_width="118dp"
                                    android:layout_centerInParent="true"
                                    android:id="@+id/card_create_patiant"
                                    android:layout_height="37dp"
                                    android:layout_marginEnd="6dp"
                                    app:cardBackgroundColor="@color/main_color"
                                    app:cardCornerRadius="10dp">

                                    <LinearLayout
                                        android:layout_gravity="center_vertical"
                                        android:gravity="center_vertical"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginEnd="10dp"
                                        android:orientation="horizontal">
                                        <TextView
                                            android:text="Create Patient"
                                            android:textSize="15sp"
                                            android:textColor="@color/white"
                                            android:fontFamily="@font/roboto_regular"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"/>
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>
                                <com.google.android.material.card.MaterialCardView
                                    android:layout_width="118dp"
                                    android:layout_centerInParent="true"
                                    android:id="@+id/card_update"
                                    android:layout_height="37dp"
                                    android:layout_marginEnd="6dp"
                                    app:cardBackgroundColor="@color/main_color"
                                    app:cardCornerRadius="10dp">

                                    <LinearLayout
                                        android:layout_gravity="center"
                                        android:gravity="center"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginEnd="10dp"
                                        android:orientation="horizontal">
                                        <TextView
                                            android:text="Update"
                                            android:textSize="15sp"
                                            android:textColor="@color/white"
                                            android:fontFamily="@font/roboto_regular"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"/>
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>
                            </RelativeLayout>
                            </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>
                        </LinearLayout>

                        <View
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:background="#17000000" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">
                                <TextView
                                    android:singleLine="true"
                                    android:layout_weight="1"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/roboto_medium"
                                    android:text="Medications"
                                    android:textColor="@color/black"
                                    android:textSize="20sp" />
                                <com.google.android.material.card.MaterialCardView
                                    android:id="@+id/card_add_new_med"
                                    android:layout_width="153dp"
                                    android:layout_height="31dp"
                                    app:cardBackgroundColor="@color/doctor_main_color"
                                    app:cardCornerRadius="12dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center"
                                        android:gravity="center"
                                        android:orientation="horizontal">


                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="10dp"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Add Medication"
                                            android:textColor="@color/white"
                                            android:textSize="14sp" />
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>

                            </LinearLayout>

                            <TextView
                                android:visibility="gone"
                                android:id="@+id/txt_no_data"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="25dp"
                                android:fontFamily="@font/roboto_medium"
                                android:text="No Data"
                                android:layout_gravity="center"

                                android:textColor="@color/black"
                                android:textSize="14sp" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recycler_med"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="50dp"
                                tools:itemCount="2"
                                tools:listitem="@layout/item_medication" />
                        </LinearLayout>
                    </LinearLayout
                        >


                    <LinearLayout
                        android:layout_marginTop="80dp"
                        android:layout_marginBottom="50dp"

                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <TextView
                            android:id="@+id/txt_cancel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:fontFamily="@font/roboto_medium"
                            android:gravity="center"
                            android:layout_marginStart="30dp"
                            android:layout_marginEnd="25dp"
                            android:text="Cancel"
                            android:textColor="@color/doctor_main_color"
                            android:textSize="18sp" />
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_create"
                            android:layout_width="221dp"
                            android:layout_height="46dp"

                            app:cardBackgroundColor="@color/doctor_main_color"
                            app:cardCornerRadius="12dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:fontFamily="@font/roboto_medium"
                                android:gravity="center"
                                android:text="Create Prescription"
                                android:textColor="@color/white"
                                android:textSize="16sp" />
                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
