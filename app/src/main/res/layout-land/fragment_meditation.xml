<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FCF8FA"
    android:orientation="vertical">
    <androidx.core.widget.NestedScrollView
        android:id="@+id/nested_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:orientation="vertical">
        <LinearLayout
            android:layout_marginTop="40dp"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <ImageView
                android:visibility="visible"
                android:id="@+id/img_menu"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/menu"/>
            <TextView
                android:id="@+id/txt_title"
                android:layout_marginStart="16dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:fontFamily="@font/roboto_bold"
                android:gravity="center_vertical"
                android:text="@string/medications"
                android:textColor="@color/black"
                android:textSize="22sp" />
        </LinearLayout>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="20dp"
                android:layout_marginBottom="100dp"
                android:layout_marginTop="50dp"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="20dp">
                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="30dp"
                        android:orientation="horizontal">
                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="wrap_content"
                            android:layout_height="50dp"
                            android:layout_marginStart="2dp"
                            android:layout_weight="1"
                            android:layout_marginTop="1dp"
                            android:layout_marginBottom="1dp"
                            android:layout_marginEnd="20dp"
                            app:cardBackgroundColor="#F8F8F8"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="2dp"
                            app:strokeColor="@color/white">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_vertical"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <ImageView
                                    android:layout_width="35dp"
                                    android:layout_height="35dp"
                                    android:layout_marginStart="10dp"
                                    android:src="@drawable/search_icon" />

                                <EditText
                                    android:background="@android:color/transparent"
                                    android:id="@+id/edit_search"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center_vertical"
                                    android:layout_weight="1"
                                    android:fontFamily="@font/roboto_regular"
                                    android:gravity="center_vertical"
                                    android:hint="Search ... "
                                    android:textColorHint="#ABB7C2"
                                    android:textSize="16sp" />

                                <ImageView
                                    android:visibility="gone"
                                    android:layout_width="36dp"
                                    android:layout_height="36dp"
                                    android:layout_marginEnd="10dp"
                                    android:src="@drawable/icon_filter" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>
                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="80dp"
                            android:id="@+id/card_filter"
                            app:cardCornerRadius="12dp"
                            app:strokeWidth="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:strokeColor="#B0B0B0"
                            android:layout_height="40dp">

                            <LinearLayout
                                android:layout_gravity="center"
                                android:gravity="center"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="horizontal">
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:fontFamily="@font/roboto_medium"
                                    android:gravity="center_vertical"
                                    android:text="Filter"
                                    android:textColor="#454545"
                                    android:textSize="14sp" />
                                <ImageView
                                    android:layout_marginStart="5dp"
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/filter_new"/>
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>
                    </LinearLayout>

                    <TextView
                        android:visibility="gone"
                        android:id="@+id/txt_no_data"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:text="No Data Found"
                        android:layout_gravity="center"
                        android:layout_marginTop="50dp"
                        android:gravity="center"
                        android:layout_centerInParent="true"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                    <androidx.recyclerview.widget.RecyclerView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="50dp"
                        tools:itemCount="3"
                        android:layout_marginBottom="20dp"
                        tools:listitem="@layout/item_med"
                        android:id="@+id/recycler_med"/>
                    <RelativeLayout
                        android:id="@+id/progressBar_small"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="#50F9FEFF"
                        android:layout_gravity="center"
                        android:visibility="gone">

                        <ProgressBar
                            style="?android:attr/progressBarStyle"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_centerInParent="true"
                            android:layout_gravity="center"
                            android:indeterminateDrawable="@drawable/rotating_icon"
                            android:visibility="visible"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </RelativeLayout>
                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>



        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
    <include android:id="@+id/page_filter"
        layout="@layout/fragment_filter_pos_shop"/>
</androidx.constraintlayout.widget.ConstraintLayout>
