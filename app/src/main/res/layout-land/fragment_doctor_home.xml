<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FAFAFE"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none"
        android:fillViewport="true">
        <LinearLayout
            android:elevation="0dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="100dp"
            android:layout_marginStart="20dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_marginTop="40dp"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <ImageView
                    android:visibility="visible"
                    android:id="@+id/img_menu"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/menu"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="16dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Prescription Insights"
                    android:textColor="@color/black"
                    android:textSize="22sp" />
            </LinearLayout>


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_slider"
                android:layout_marginTop="30dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:clipChildren="false" />

            <LinearLayout
                android:id="@+id/dotContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="visible"
                app:layout_constraintTop_toBottomOf="@id/viewPager" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="20dp"
                android:fontFamily="@font/roboto_bold"
                android:gravity="center"
                android:text="Visual Data"
                android:textColor="@color/black"
                android:textSize="22sp" />
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:id="@+id/card_filter_by_date"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="1dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="10dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="25dp"
                app:cardElevation="2dp"
                app:strokeColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:paddingEnd="2dp"
                        android:singleLine="true"
                        android:layout_weight="1"
                        android:background="@android:color/transparent"
                        android:id="@+id/txt_filter_by_date"
                        android:layout_width="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Filter by date"
                        android:textColorHint="#ABB7C2"
                        android:textSize="15sp" />
                    <ImageView
                        android:visibility="gone"
                        android:id="@+id/img_filter_reset"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:layout_marginEnd="10dp"
                        android:src="@drawable/filter_reset"
                        app:tint="#000"/>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/roboto_regular"
                android:gravity="center"
                android:text="Sales Analytics"
                android:textColor="#737D93"
                android:textSize="14sp" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/txt_sales_analytics"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:text="KD 0.000"
                    android:textColor="#212325"
                    android:textSize="24sp" />
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="72dp"
                    android:visibility="gone"
                    app:cardCornerRadius="12dp"
                    app:cardBackgroundColor="#ECF5EE"
                    android:layout_marginStart="25dp"
                    android:layout_height="32dp">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"

                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:text="+7.6%"
                        android:textColor="#458F5A"
                        android:textSize="14sp" />
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>

            <com.github.mikephil.charting.charts.LineChart
                android:id="@+id/chart_no_of_sales"
                android:layout_width="match_parent"
                android:layout_height="340dp"
                android:layout_marginStart="5dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="20dp"
                android:layout_marginBottom="30dp" />


            <TextView
                android:id="@+id/txt_top_sell"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="20dp"
                android:fontFamily="@font/roboto_bold"
                android:gravity="center"
                android:text="Top Selling Products"
                android:textColor="@color/black"
                android:textSize="22sp" />
            <androidx.recyclerview.widget.RecyclerView
                android:layout_width="match_parent"
                tools:itemCount="1"
                tools:listitem="@layout/item_top_selling_pd"
                android:layout_height="wrap_content"
                android:id="@+id/recycler_selling_pd"
                android:layout_marginTop="15dp"/>

            <TextView
                android:id="@+id/txt_pres"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="30dp"
                android:fontFamily="@font/roboto_bold"
                android:gravity="center"
                android:text="Recent Prescriptions "
                android:textColor="@color/black"
                android:textSize="22sp" />
            <androidx.recyclerview.widget.RecyclerView
                android:layout_width="match_parent"
                tools:itemCount="1"

                tools:listitem="@layout/item_pres"
                android:layout_height="wrap_content"
                android:id="@+id/recycler_recent_pres"
                android:layout_marginTop="15dp"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>