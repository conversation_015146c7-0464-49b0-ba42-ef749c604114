<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/nav_home">
    <fragment
        android:id="@+id/nav_home"
        android:name="com.voxel.wasfaadminapp.ui.home.HomeFragment"
        android:label="Home"
        tools:layout="@layout/fragment_home"/>
    <fragment
        android:id="@+id/nav_pos"
        android:name="com.voxel.wasfaadminapp.ui.pos.POSFragment"
        android:label="POS"
        tools:layout="@layout/fragment_p_o_s"/>
    <fragment
        android:id="@+id/nav_products"
        android:name="com.voxel.wasfaadminapp.ui.products.ProductFragment"
        android:label="POS"
        tools:layout="@layout/fragment_product"/>
    <fragment
        android:id="@+id/nav_sale"
        android:name="com.voxel.wasfaadminapp.ui.sales.SalesFragment"
        android:label="Sales"
        tools:layout="@layout/fragment_sales"/>
    <fragment
        android:id="@+id/nav_account"
        android:name="com.voxel.wasfaadminapp.ui.account.AccountFragment"
        android:label="Account"
        tools:layout="@layout/fragment_account"/>
    <fragment
        android:id="@+id/nav_sale_details"
        android:name="com.voxel.wasfaadminapp.ui.sales.details.SalesDetailsFragment"
        android:label="SalesDetails"
        tools:layout="@layout/fragment_sales_details"/>
    <fragment
        android:id="@+id/nav_filter"
        android:name="com.voxel.wasfaadminapp.ui.filter.FilterFragment"
        android:label="filter"
        tools:layout="@layout/fragment_filter"/>
    <fragment
        android:id="@+id/nav_add_product"
        android:name="com.voxel.wasfaadminapp.ui.products.add.AddProductFragment"
        android:label="addProduct"
        tools:layout="@layout/fragment_addproduct"/>
    <fragment
        android:id="@+id/nav_add_product_2"
        android:name="com.voxel.wasfaadminapp.ui.products.add.AddProduct2Fragment"
        android:label="addProduct2"
        tools:layout="@layout/fragment_addproduct_2"/>
    <fragment
        android:id="@+id/nav_add_product_3"
        android:name="com.voxel.wasfaadminapp.ui.products.add.AddProduct3Fragment"
        android:label="addProduct3"
        tools:layout="@layout/fragment_addproduct_3"/>
    <fragment
        android:id="@+id/nav_add_product_4"
        android:name="com.voxel.wasfaadminapp.ui.products.add.AddProduct4Fragment"
        android:label="addProduct4"
        tools:layout="@layout/fragment_addproduct_4"/>
    <fragment
        android:id="@+id/nav_add_product_5"
        android:name="com.voxel.wasfaadminapp.ui.products.add.AddProduct5Fragment"
        android:label="addProduct5"
        tools:layout="@layout/fragment_addproduct_5"/>
    <fragment
        android:id="@+id/nav_all_product"
        android:name="com.voxel.wasfaadminapp.ui.products.view.AllProductFragment"
        android:label="allProduct"
        tools:layout="@layout/fragment_all_product"/>
    <fragment
        android:id="@+id/nav_tab_pos_shop_product"
        android:name="com.voxel.wasfaadminapp.ui.pos.tab.shop.POSShopTabFragment"
        android:label="tabposshopfragment"
        tools:layout="@layout/fragment_p_o_s_shop_tab"/>
    <fragment
        android:id="@+id/nav_tab_pos_rx_2_product"
        android:name="com.voxel.wasfaadminapp.ui.pos.tab.rx.POSRX2TabFragment"
        android:label="tabposrx2fragment"
        tools:layout="@layout/fragment_p_o_s_r_x_2_tab"/>
    <fragment
        android:id="@+id/nav_tab_pos_rx_product"
        android:name="com.voxel.wasfaadminapp.ui.pos.tab.rx.POSRXTabFragment"
        android:label="tabposrxfragment"
        tools:layout="@layout/fragment_p_o_s_r_x_tab"/>
    <fragment
        android:id="@+id/nav_pos_rx_product"
        android:name="com.voxel.wasfaadminapp.ui.pos.rx.POSRXFragment"
        android:label="posrxfragment"
        tools:layout="@layout/fragment_p_o_s_r_x"/>

    <fragment
        android:id="@+id/nav_cart"
        android:name="com.voxel.wasfaadminapp.ui.cart.POSRXCartFragment"
        android:label="cart"
        tools:layout="@layout/fragment_pos_rx_cart"/>

    <fragment
        android:id="@+id/nav_cart_details"
        android:name="com.voxel.wasfaadminapp.ui.cart.CartDetailFragment"
        android:label="cartDetails"
        tools:layout="@layout/fragment_cart_detail"/>
    <fragment
        android:id="@+id/nav_pos_shop"
        android:name="com.voxel.wasfaadminapp.ui.pos.shop.POSShopFragment"
        android:label="posShop"
        tools:layout="@layout/fragment_p_o_s_shop"/>
    <fragment
        android:id="@+id/nav_pos_shop_details"
        android:name="com.voxel.wasfaadminapp.ui.pos.shop.POSShopDetailsFragment"
        android:label="posConfig"
        tools:layout="@layout/fragment_p_o_s_shop_details"/>
    <fragment
        android:id="@+id/nav_pos_rx_details"
        android:name="com.voxel.wasfaadminapp.ui.pos.rx.POSRXDetailsFragment"
        android:label="posRXDetails"
        tools:layout="@layout/fragment_p_o_s_rx_details"/>
    <fragment
        android:id="@+id/nav_order_details"
        android:name="com.voxel.wasfaadminapp.ui.sales.details.OrderDetailsFragment"
        android:label="orderDetails"
        tools:layout="@layout/fragment_order_details"/>
    <fragment
        android:id="@+id/nav_seller"
        android:name="com.voxel.wasfaadminapp.ui.seller.SellerFragment"
        android:label="seller"
        tools:layout="@layout/fragment_seller"/>
    <fragment
        android:id="@+id/nav_influencer_apix"
        android:name="com.voxel.wasfaadminapp.ui.influencer.InfluencerApixFragment"
        android:label="influencerApix"
        tools:layout="@layout/fragment_seller_apix"/>
    <fragment
        android:id="@+id/nav_seller_apix"
        android:name="com.voxel.wasfaadminapp.ui.seller.SellerApixFragment"
        android:label="sellerApix"
        tools:layout="@layout/fragment_seller_apix"/>
    <fragment
        android:id="@+id/nav_influencer"
        android:name="com.voxel.wasfaadminapp.ui.influencer.InfluencerFragment"
        android:label="influencer"
        tools:layout="@layout/fragment_influencer"/>
    <fragment
        android:id="@+id/nav_pos_shop_cart"
        android:name="com.voxel.wasfaadminapp.ui.cart.shop.POSShopCartFragment"
        android:label="posShopCart"
        tools:layout="@layout/fragment_pos_shop_cart"/>
    <fragment
        android:id="@+id/nav_pos_cart_summery"
        android:name="com.voxel.wasfaadminapp.ui.cart.summary.CartSummaryFragment"
        android:label="cartSummery"
        tools:layout="@layout/fragment_cart_summary"/>
    <fragment
        android:id="@+id/nav_sell_return"
        android:name="com.voxel.wasfaadminapp.ui.sales.details.SellReturnFragment"
        android:label="sellReturn"
        tools:layout="@layout/fragment_sell_return"/>
    <fragment
        android:id="@+id/nav_pos_rx_cart_summery"
        android:name="com.voxel.wasfaadminapp.ui.cart.summary.CartRXSummaryFragment"
        android:label="rxSummery"
        tools:layout="@layout/fragment_cart_r_x_summary"/>









    <fragment
        android:id="@+id/nav_customer"
        android:name="com.voxel.wasfaadminapp.ui.customers.CustomerFragment"
        android:label="customer"
        tools:layout="@layout/fragment_customer"/>

    <fragment
        android:id="@+id/nav_customer_edit"
        android:name="com.voxel.wasfaadminapp.ui.customers.edit.EditCustomerFragment"
        android:label="editCustomer"
        tools:layout="@layout/fragment_edit_customer"/>

    <fragment
        android:id="@+id/nav_segment"
        android:name="com.voxel.wasfaadminapp.ui.customers.segment.SegmentFragment"
        android:label="segment"
        tools:layout="@layout/fragment_segment"/>

    <fragment
        android:id="@+id/nav_type"
        android:name="com.voxel.wasfaadminapp.ui.customers.type.TypeFragment"
        android:label="type"
        tools:layout="@layout/fragment_type"/>

    <fragment
        android:id="@+id/nav_cust_cart"
        android:name="com.voxel.wasfaadminapp.ui.cart.shop.CustomerCartFragment"
        android:label="custCart"
        tools:layout="@layout/fragment_customer_cart"/>

</navigation>