<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FAFAFE"
    android:orientation="vertical">
    <androidx.core.widget.NestedScrollView
        android:id="@+id/nested_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:orientation="vertical">
        <LinearLayout
            android:layout_marginTop="40dp"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <ImageView
                android:visibility="visible"
                android:id="@+id/img_menu"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/menu"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/roboto_bold"
                android:gravity="center_vertical"
                android:layout_marginStart="16dp"
                android:text="@string/reports"
                android:textColor="@color/black"
                android:textSize="16dp" />

        </LinearLayout>
            <TextView
                android:visibility="gone"
                android:id="@+id/txt_no_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_bold"
                android:text="No Data Found"
                android:layout_gravity="center"
                android:layout_marginTop="50dp"
                android:gravity="center"
                android:layout_centerInParent="true"
                android:textColor="@color/black"
                android:textSize="14sp" />
            <androidx.recyclerview.widget.RecyclerView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="50dp"
                tools:itemCount="3"
                tools:listitem="@layout/item_report"
                android:id="@+id/recycler_pres"/>
            <RelativeLayout
                android:id="@+id/progressBar_small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="#50F9FEFF"
                android:layout_gravity="center"
                android:visibility="gone">

                <ProgressBar
                    style="?android:attr/progressBarStyle"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    android:indeterminateDrawable="@drawable/rotating_icon"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </RelativeLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
