<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"

    android:background="#FAFAFE"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/img_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentStart="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                app:cardBackgroundColor="#F7F7F7"
                app:cardCornerRadius="10dp"
                app:strokeWidth="0dp">

                <ImageView
                    android:layout_width="6dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center"

                    android:src="@drawable/arrow_brown_new" />
            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:id="@+id/txt_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/roboto_bold"
                android:text="POS Shop"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <RelativeLayout
                android:layout_width="33dp"
                android:layout_height="27dp"
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:layout_marginEnd="12dp">

                <ImageView
                    android:id="@+id/img_cart"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/rx_icon_new" />

                <RelativeLayout
                    android:id="@+id/rlt_cart_count"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/cart_count_bg" />

                    <TextView
                        android:id="@+id/txt_cart_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center"
                        android:maxLength="2"
                        android:text="2"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </RelativeLayout>
            </RelativeLayout>
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|top"
                android:layout_marginTop="30dp"

                android:gravity="center_vertical|top"
                android:orientation="horizontal">


                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_order_box"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="1dp"
                    android:layout_marginEnd="12dp"
                    android:layout_marginBottom="1dp"
                    android:layout_weight="1"
                    android:elevation="2dp"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="2dp"
                    app:strokeWidth="0.5dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="ALL ORDERS"
                            android:textColor="@color/black"
                            android:textSize="18sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#1F000000" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <RelativeLayout
                                android:id="@+id/progressBarOrder"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_centerInParent="true"
                                android:background="#50F9FEFF"
                                android:visibility="gone">

                                <ProgressBar
                                    style="?android:attr/progressBarStyle"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerInParent="true"
                                    android:layout_gravity="center"
                                    android:indeterminateDrawable="@drawable/rotating_icon"
                                    android:visibility="visible"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />
                            </RelativeLayout>

                            <TextView
                                android:id="@+id/txt_no_data_order"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:layout_gravity="center"
                                android:layout_marginTop="25dp"
                                android:fontFamily="@font/roboto_medium"
                                android:gravity="center"
                                android:text="No Data"
                                android:textColor="@color/black"

                                android:textSize="14sp"
                                android:visibility="gone" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recycler_all_orders"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_margin="15dp"
                                tools:itemCount="3"

                                tools:listitem="@layout/tab_pos_rx" />
                        </RelativeLayout>


                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_view_all_order"
                            android:layout_width="218dp"
                            android:layout_height="39dp"
                            android:layout_centerInParent="true"
                            android:layout_gravity="center"
                            android:layout_marginBottom="20dp"

                            app:cardBackgroundColor="@color/main_color"

                            app:cardCornerRadius="10dp"
                            app:strokeWidth="0dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:gravity="center"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/roboto_medium"
                                    android:gravity="center"
                                    android:text="View All Orders"
                                    android:textColor="@color/white"
                                    android:textSize="18sp" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>

            <LinearLayout

                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical|top"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="30dp"

                android:gravity="center_vertical|top"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="10dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="All PRODUCTS"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="71dp"
                            android:layout_marginStart="12dp"
                            android:layout_marginTop="18dp"
                            android:layout_marginEnd="12dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="10dp"
                            app:cardElevation="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_vertical"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <com.google.android.material.card.MaterialCardView
                                    android:id="@+id/card_influencer"
                                    android:layout_width="match_parent"
                                    android:layout_height="40dp"
                                    android:layout_marginStart="13dp"
                                    android:layout_marginEnd="5dp"
                                    android:layout_weight="1"
                                    app:cardBackgroundColor="@color/white"
                                    app:cardCornerRadius="5dp"
                                    app:strokeColor="#7D7C7C"
                                    app:strokeWidth="1dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:id="@+id/txt_influencer"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_marginStart="10dp"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:gravity="center_vertical"
                                            android:hint="Search influencer"
                                            android:singleLine="true"
                                            android:textColorHint="#B4ABAB"
                                            android:textSize="12sp" />

                                        <ImageView
                                            android:layout_width="24dp"
                                            android:layout_height="24dp"
                                            android:layout_marginEnd="6dp"
                                            android:src="@drawable/arrow_down"
                                            app:tint="#222222" />
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>

                                <com.google.android.material.card.MaterialCardView
                                    android:id="@+id/card_filter"
                                    android:layout_width="118dp"
                                    android:layout_height="40dp"
                                    android:layout_marginEnd="6dp"
                                    app:cardBackgroundColor="@color/main_color"
                                    app:cardCornerRadius="10dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <ImageView
                                            android:layout_width="15dp"
                                            android:layout_height="15dp"
                                            android:layout_marginEnd="5dp"
                                            android:src="@drawable/filter_home" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="More Filter"
                                            android:textColor="@color/white"
                                            android:textSize="15sp" />
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>

                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginStart="12dp"
                            android:layout_marginTop="15dp"
                            android:layout_marginEnd="12dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <EditText
                                    android:id="@+id/edit_search"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="15dp"
                                    android:layout_weight="1"
                                    android:background="@android:color/transparent"
                                    android:fontFamily="@font/roboto_regular"
                                    android:gravity="center_vertical"
                                    android:hint="Search Products"
                                    android:singleLine="true"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="12sp" />

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:layout_marginEnd="17dp"
                                    android:src="@drawable/search_green" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="12dp"
                            android:layout_marginTop="24dp"
                            android:layout_marginEnd="12dp"
                            android:layout_marginBottom="30dp">

                            <include
                                android:id="@+id/page_shimmer"
                                layout="@layout/shimmer_pos_shop_product" />

                            <TextView
                                android:id="@+id/txt_no_data"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:layout_marginTop="100dp"
                                android:fontFamily="@font/roboto_bold"
                                android:gravity="center"
                                android:text="No Data Found"
                                android:textColor="@color/black"
                                android:textSize="14sp"
                                android:visibility="gone" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recycler_products"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:nestedScrollingEnabled="false"
                                tools:itemCount="1"

                                tools:listitem="@layout/item_pos_shop_product" />
                        </RelativeLayout>

                        <RelativeLayout
                            android:id="@+id/progressBar_small"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="#50F9FEFF"
                            android:visibility="gone">

                            <ProgressBar
                                style="?android:attr/progressBarStyle"
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_centerInParent="true"
                                android:layout_gravity="center"
                                android:indeterminateDrawable="@drawable/rotating_icon"
                                android:visibility="visible"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />
                        </RelativeLayout>
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="10dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="CART"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_marginTop="18dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="Customer "
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="18dp"
                        android:layout_marginBottom="1dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/lyt_choose_customer"
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:layout_margin="2dp"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#DBE0E6"
                            app:strokeWidth="1dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/txt_customer"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="15dp"
                                    android:layout_weight="1"
                                    android:fontFamily="@font/roboto_regular"
                                    android:gravity="center_vertical"
                                    android:hint="--- Walk In Customer ---"
                                    android:singleLine="true"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="14sp" />

                                <ImageView
                                    android:id="@+id/img_delivery_staff_arrow"
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:layout_marginEnd="6dp"
                                    android:src="@drawable/arrow_down"
                                    app:tint="#222222" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/add_cust"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginEnd="10dp"
                            android:layout_marginStart="10dp"
                            app:cardBackgroundColor="@color/main_color"
                            app:cardCornerRadius="5dp"
                            app:strokeWidth="0dp">

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_gravity="center"
                                android:src="@drawable/add_cust_icon"

                                />
                        </com.google.android.material.card.MaterialCardView>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="30dp"
                        android:layout_marginBottom="20dp"
                        android:background="#DBE0E6" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_bold"
                        android:text="Items"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <androidx.core.widget.NestedScrollView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scrollbars="none"
                        android:fillViewport="true">
                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:elevation="2dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="10dp"
                            app:cardElevation="2dp"
                            app:strokeWidth="0dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">

                                    <RelativeLayout
                                        android:id="@+id/progressBarCart"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:background="#50F9FEFF"
                                        android:visibility="gone">

                                        <ProgressBar
                                            style="?android:attr/progressBarStyle"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_centerInParent="true"
                                            android:layout_gravity="center"
                                            android:indeterminateDrawable="@drawable/rotating_icon"
                                            android:visibility="visible"
                                            app:layout_constraintBottom_toBottomOf="parent"
                                            app:layout_constraintEnd_toEndOf="parent"
                                            app:layout_constraintStart_toStartOf="parent"
                                            app:layout_constraintTop_toTopOf="parent" />
                                    </RelativeLayout>

                                    <TextView
                                        android:id="@+id/txt_no_data_cart"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:layout_marginTop="25dp"
                                        android:fontFamily="@font/roboto_medium"
                                        android:text="No Data"
                                        android:textColor="@color/black"
                                        android:textSize="14sp"
                                        android:visibility="gone" />

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recycler_cart"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentTop="true"
                                        tools:itemCount="1"

                                        tools:listitem="@layout/rx_cart_item" />
                                </RelativeLayout>
                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1" />

                                <com.google.android.material.card.MaterialCardView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    app:cardBackgroundColor="#9EF5F5F5"
                                    android:elevation="1dp"
                                    app:cardCornerRadius="8dp"
                                    app:strokeWidth="0dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="vertical"
                                        android:padding="10dp"
                                        android:visibility="visible">


                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="20dp"
                                            android:layout_marginTop="21dp"
                                            android:layout_marginEnd="20dp"
                                            android:orientation="horizontal">

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:fontFamily="@font/roboto_medium"
                                                android:text="Sub Total"
                                                android:textColor="#A0A0A0"
                                                android:textSize="14sp" />

                                            <TextView
                                                android:id="@+id/txt_sub_total"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:fontFamily="@font/roboto_medium"
                                                android:text="KWD  0.000"
                                                android:textColor="#A0A0A0"
                                                android:textSize="14sp" />
                                        </LinearLayout>

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="20dp"
                                            android:layout_marginTop="10dp"
                                            android:layout_marginEnd="20dp"
                                            android:orientation="horizontal">

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:fontFamily="@font/roboto_medium"
                                                android:text="Item Count"
                                                android:textColor="#A0A0A0"
                                                android:textSize="14sp" />

                                            <TextView
                                                android:id="@+id/txt_item_count"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:fontFamily="@font/roboto_medium"
                                                android:textColor="#A0A0A0"
                                                android:textSize="14sp" />

                                        </LinearLayout>

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="20dp"
                                            android:layout_marginTop="10dp"
                                            android:layout_marginEnd="20dp"
                                            android:orientation="horizontal">

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:fontFamily="@font/roboto_medium"
                                                android:text="Tax"
                                                android:textColor="#A0A0A0"
                                                android:textSize="14sp" />

                                            <TextView
                                                android:id="@+id/txt_tax"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:fontFamily="@font/roboto_medium"
                                                android:text="KWD  0.000"
                                                android:textColor="#A0A0A0"
                                                android:textSize="14sp" />

                                        </LinearLayout>

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="20dp"
                                            android:layout_marginTop="10dp"
                                            android:layout_marginEnd="20dp"
                                            android:orientation="horizontal">

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:fontFamily="@font/roboto_medium"
                                                android:text="Shipping"
                                                android:textColor="#A0A0A0"
                                                android:textSize="14sp" />

                                            <TextView
                                                android:id="@+id/txt_shipping"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:fontFamily="@font/roboto_medium"
                                                android:text="KWD  0.000"
                                                android:textColor="#A0A0A0"
                                                android:textSize="14sp" />

                                        </LinearLayout>

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="20dp"
                                            android:layout_marginTop="10dp"
                                            android:layout_marginEnd="20dp"
                                            android:orientation="horizontal">

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:fontFamily="@font/roboto_medium"
                                                android:text="Discount"
                                                android:textColor="#A0A0A0"
                                                android:textSize="14sp" />

                                            <TextView
                                                android:id="@+id/txt_discount"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:fontFamily="@font/roboto_medium"
                                                android:text="KWD  0.000"
                                                android:textColor="#A0A0A0"
                                                android:textSize="14sp" />

                                        </LinearLayout>


                                        <View
                                            android:layout_width="match_parent"
                                            android:layout_height="0.8dp"
                                            android:layout_marginStart="20dp"
                                            android:layout_marginTop="20dp"
                                            android:layout_marginEnd="20dp"
                                            android:background="#80000000" />

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="20dp"
                                            android:layout_marginTop="20dp"
                                            android:layout_marginEnd="20dp"
                                            android:layout_marginBottom="20dp"
                                            android:orientation="horizontal">

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:fontFamily="@font/roboto_bold"
                                                android:text="Total"
                                                android:textColor="@color/black"
                                                android:textSize="19sp" />

                                            <TextView
                                                android:id="@+id/txt_total"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:fontFamily="@font/roboto_bold"
                                                android:text="KWD  0.000"
                                                android:textColor="@color/black"
                                                android:textSize="19sp" />

                                        </LinearLayout>
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>

                                <com.google.android.material.card.MaterialCardView
                                    android:id="@+id/card_proceed"
                                    android:layout_width="match_parent"
                                    android:layout_height="54dp"
                                    android:layout_gravity="center"
                                    android:layout_marginTop="40dp"
                                    android:layout_marginBottom="22dp"
                                    app:cardBackgroundColor="@color/main_color"
                                    app:cardCornerRadius="12dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:fontFamily="@font/roboto_medium"
                                        android:text="Proceed to payment"
                                        android:textColor="@color/white"
                                        android:textSize="14sp" />
                                </com.google.android.material.card.MaterialCardView>
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                    </androidx.core.widget.NestedScrollView>
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>

    <include
        android:id="@+id/page_filter"
        layout="@layout/fragment_filter_pos_shop" />
    <include layout="@layout/sheet_add_address_customer"
        android:id="@+id/page_address_new"/>
    <include layout="@layout/fragment_shipping_address_cart"
        android:id="@+id/page_list"/>
</androidx.coordinatorlayout.widget.CoordinatorLayout>