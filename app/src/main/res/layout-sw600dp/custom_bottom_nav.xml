<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/customBottomNav"
    android:layout_width="290dp"
    android:layout_height="match_parent"
    android:layout_alignParentStart="true"
    android:background="@color/white"
    android:elevation="8dp"
    android:gravity="center"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center|top"
        android:background="@color/white"
        android:gravity="center|top"
        android:orientation="vertical">

        <ImageView
            android:layout_marginTop="10dp"
            android:layout_width="100dp"
            android:layout_height="90dp"
            android:src="@drawable/wasfa_logo" />

        <LinearLayout
            android:id="@+id/lyt_home"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="20dp"
            android:layout_marginTop="50dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/home_selection_bg"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_home_selected"
                android:layout_width="42dp"
                android:layout_height="match_parent"
                android:layout_marginStart="-16dp"
                android:rotation="270"
                android:src="@drawable/nav_selection" />

            <ImageView
                android:id="@+id/nav_home"
                android:layout_width="24dp"
                android:layout_marginStart="-10dp"
                android:layout_height="24dp"
                android:contentDescription="Home"
                android:src="@drawable/nav_doc_home_selected" />

            <TextView
                android:id="@+id/txt_nav_home"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:fontFamily="@font/roboto_medium"
                android:text="@string/home"
                android:textColor="@color/doctor_main_color"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/lyt_med"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_med_selected"
                android:layout_width="42dp"
                android:layout_height="match_parent"
                android:layout_marginStart="-16dp"
                android:rotation="270"
                android:src="@drawable/nav_selection"
                android:visibility="invisible" />

            <ImageView
                android:id="@+id/nav_med"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="-10dp"
                android:contentDescription="Home"
                android:src="@drawable/nav_med" />

            <TextView
                android:id="@+id/txt_nav_med"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_regular"
                android:text="@string/medicine"
                android:textColor="#9E9E9E"
                android:textSize="16sp" />
            <ImageView
                app:tint="#9E9E9E"
                android:id="@+id/img_pos_arrow"
                android:layout_width="18dp"
                android:rotation="270"
                android:layout_height="20dp"
                android:layout_marginEnd="5dp"
                android:src="@drawable/arrow_down_menu" />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/lyt_med_hide"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:id="@+id/lyt_all_prod_med"
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="20dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="14dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_centerInParent="true"
                        android:id="@+id/prod_bullet"
                        android:layout_width="18dp"
                        android:layout_height="27dp"
                        android:visibility="visible"

                        android:src="@drawable/bullet"
                        app:tint="#9E9E9E" />

                    <ImageView
                        android:layout_centerInParent="true"
                        android:id="@+id/prod_bullet_selected"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:src="@drawable/bullet_selected"
                        android:visibility="gone"
                        app:tint="#9E9E9E" />
                </RelativeLayout>


                <TextView
                    android:id="@+id/txt_all_products"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_regular"
                    android:text="All Products"
                    android:textColor="#9E9E9E"
                    android:textSize="12sp" />


            </LinearLayout>

            <LinearLayout
                android:id="@+id/lyt_fav_med"
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:layout_gravity="center_vertical"
                android:layout_marginBottom="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="14dp"
                    android:layout_width="20dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_centerInParent="true"
                        android:id="@+id/fav_bullet"
                        android:layout_width="18dp"
                        android:layout_height="27dp"

                        android:src="@drawable/bullet"
                        app:tint="#9E9E9E" />

                    <ImageView
                        android:layout_centerInParent="true"
                        android:id="@+id/fav_bullet_selected"
                        android:layout_width="10dp"
                        android:layout_height="10dp"
                        android:src="@drawable/bullet_selected"
                        android:visibility="gone"
                        app:tint="#9E9E9E" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/txt_fav"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Favorites"
                    android:textColor="#9E9E9E"
                    android:textSize="12sp" />


            </LinearLayout>
        </LinearLayout>


        <LinearLayout
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            android:id="@+id/lyt_prescriptions"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:visibility="invisible"
                android:id="@+id/img_pres_selected"
                android:layout_width="42dp"
                android:layout_height="match_parent"
                android:layout_marginStart="-16dp"
                android:rotation="270"
                android:src="@drawable/nav_selection" />

            <ImageView
                android:id="@+id/nav_pres"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="-10dp"
                android:contentDescription="Home"
                android:src="@drawable/nav_pres" />

            <TextView
                android:id="@+id/txt_nav_pres"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:fontFamily="@font/roboto_regular"
                android:text="@string/prescriptions"
                android:textColor="#9E9E9E"
                android:textSize="16sp" />
        </LinearLayout>


        <LinearLayout
            android:id="@+id/lyt_report"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_report_selected"
                android:layout_width="42dp"
                android:layout_height="match_parent"
                android:layout_marginStart="-16dp"
                android:rotation="270"
                android:src="@drawable/nav_selection"
                android:visibility="invisible" />

            <ImageView
                android:id="@+id/nav_report"
                android:layout_width="24dp"
                app:tint="#9E9E9E"
                android:layout_height="24dp"
                android:layout_marginStart="-10dp"
                android:contentDescription="Home"
                android:src="@drawable/report" />

            <TextView
                android:id="@+id/txt_nav_report"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:fontFamily="@font/roboto_regular"
                android:text="@string/reports"
                android:textColor="#9E9E9E"
                android:textSize="16sp" />
        </LinearLayout>
        <LinearLayout
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            android:id="@+id/lyt_settings"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:visibility="invisible"
                android:id="@+id/img_settings_selected"
                android:layout_width="42dp"
                android:layout_height="match_parent"
                android:layout_marginStart="-16dp"
                android:rotation="270"
                android:src="@drawable/nav_selection" />

            <ImageView
                android:id="@+id/nav_settings"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="-10dp"
                android:contentDescription="Home"
                android:src="@drawable/nav_settings" />

            <TextView
                android:id="@+id/txt_nav_settings"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:fontFamily="@font/roboto_regular"
                android:text="@string/settings"
                android:textColor="#9E9E9E"
                android:textSize="16sp" />
        </LinearLayout>
        <LinearLayout
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            android:id="@+id/lyt_logout"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:visibility="invisible"
                android:id="@+id/img_logout_selected"
                android:layout_width="42dp"
                android:layout_height="match_parent"
                android:layout_marginStart="-16dp"
                android:rotation="270"
                android:src="@drawable/nav_selection" />

            <ImageView
                android:id="@+id/nav_logout"
                android:layout_width="24dp"
                android:layout_height="24dp"
                app:tint="#9E9E9E"
                android:layout_marginStart="-10dp"
                android:contentDescription="Home"
                android:src="@drawable/log_out_icon" />

            <TextView
                android:id="@+id/txt_nav_logout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:fontFamily="@font/roboto_regular"
                android:text="@string/log_out"
                android:textColor="#9E9E9E"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

    <com.google.android.material.card.MaterialCardView
        android:layout_width="68dp"
        android:layout_height="68dp"
        android:layout_alignParentTop="true"
        android:layout_centerInParent="true"
        android:layout_gravity="center"
        android:layout_marginTop="-20dp"
        android:visibility="gone"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="68dp"
        app:cardElevation="0dp"
        app:strokeColor="@color/white">

        <ImageView
            android:id="@+id/nav_add"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center"
            android:src="@drawable/nav_add" />
    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
