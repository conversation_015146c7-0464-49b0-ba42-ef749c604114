<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">
    <androidx.core.widget.NestedScrollView
        android:id="@+id/nested_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="16dp"
        android:layout_marginTop="26dp"
        android:layout_marginEnd="16dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="40dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_back"
                android:visibility="visible"
                android:layout_centerInParent="true"
                android:layout_alignParentStart="true"
                android:layout_width="32dp"
                android:layout_height="32dp"
                app:cardBackgroundColor="#F7F7F7"
                app:cardCornerRadius="10dp">

                <ImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center"
                    android:src="@drawable/brown_back" />
            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:layout_marginStart="16dp"
               android:layout_weight="1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:fontFamily="@font/roboto_bold"
                android:gravity="center_vertical"
                android:text="Add Medications"
                android:textColor="@color/black"
                android:textSize="22sp" />
        </LinearLayout>
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginStart="1dp"
            android:layout_marginTop="30dp"
            android:layout_marginEnd="1dp"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="25dp"
            app:cardElevation="2dp"
            app:strokeColor="@color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/search_icon" />

                <EditText
                    android:id="@+id/edit_search"
                    android:background="@android:color/transparent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_regular"
                    android:gravity="center_vertical"
                    android:hint="Search ... "
                    android:textColorHint="#ABB7C2"
                    android:textSize="15sp" />

                <ImageView
                    android:id="@+id/card_filter"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginEnd="10dp"
                    android:src="@drawable/icon_filter" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:id="@+id/card_my_list"
                app:cardBackgroundColor="@color/main_color"
                app:strokeWidth="1dp"
                app:strokeColor="@color/main_color"
                android:layout_weight="1"
                android:layout_marginEnd="5dp"
                app:cardCornerRadius="5dp">
                <TextView
                    android:id="@+id/txt_my_list"
                    android:layout_centerInParent="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="My List"
                    android:textColor="@color/white"
                    android:textSize="16dp" />
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:id="@+id/card_all_products"
                app:cardBackgroundColor="@color/white"
                app:strokeWidth="1dp"
                app:strokeColor="@color/main_color"
                android:layout_weight="1"
                android:layout_marginStart="5dp"
                app:cardCornerRadius="5dp">
                <TextView
                    android:id="@+id/txt_all_products"
                    android:layout_centerInParent="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="All Products"
                    android:textColor="@color/main_color"
                    android:textSize="16dp" />
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
        <TextView
            android:visibility="gone"
            android:id="@+id/txt_no_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_bold"
            android:text="No Data Found"
            android:layout_gravity="center"
            android:layout_marginTop="50dp"
            android:gravity="center"
            android:layout_centerInParent="true"
            android:textColor="@color/black"
            android:textSize="14sp" />
        <androidx.recyclerview.widget.RecyclerView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/recycler_med"
            tools:itemCount="2"
            tools:listitem="@layout/item_medication_list"
            android:layout_marginTop="40dp"/>
        <RelativeLayout
            android:id="@+id/progressBar_small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="#50F9FEFF"
            android:layout_gravity="center"
            android:visibility="gone">

            <ProgressBar
                style="?android:attr/progressBarStyle"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:indeterminateDrawable="@drawable/rotating_icon"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </RelativeLayout>
    </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
    <include android:id="@+id/page_filter"
        layout="@layout/fragment_filter_pos_shop"/>
</androidx.constraintlayout.widget.ConstraintLayout>