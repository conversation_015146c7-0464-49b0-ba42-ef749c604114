<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="40dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_back"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_alignParentStart="true"
                    android:layout_centerInParent="true"
                    app:cardBackgroundColor="#F7F7F7"
                    app:cardCornerRadius="10dp">

                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center"
                        android:src="@drawable/brown_back" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="New Prescription"
                    android:textColor="@color/black"
                    android:textSize="22sp" />
            </RelativeLayout>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="50dp"
                android:layout_marginBottom="100dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="20dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">


                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Patient Information"
                                android:textColor="@color/black"
                                android:textSize="20sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="160dp"
                                android:layout_marginTop="15dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="9dp"
                                app:strokeColor="#54DFDFDF"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="center_vertical"
                                    android:orientation="vertical"
                                    android:padding="10dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Name"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:id="@+id/txt_patient_name"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="DOB"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:id="@+id/txt_dob"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Civil Id"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:id="@+id/txt_civil_id"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Phone"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:id="@+id/txt_phone"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>


                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:id="@+id/txt_address_details"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="30dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Address Information"
                                android:textColor="@color/black"
                                android:textSize="20sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/card_address"
                                android:layout_width="match_parent"
                                android:layout_height="243dp"
                                android:layout_marginTop="15dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="9dp"
                                app:strokeColor="#54DFDFDF"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="center_vertical"
                                    android:orientation="vertical"
                                    android:padding="10dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Governorate"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:id="@+id/txt_governorate"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Area"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:id="@+id/txt_area"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Block Number"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:id="@+id/txt_block"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Street"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:id="@+id/txt_street"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Building"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:id="@+id/txt_building"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Floor"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:id="@+id/txt_floor"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>


                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>
                        </LinearLayout>

                        <View
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="#17000000" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Prescription Details"
                                android:textColor="@color/black"
                                android:textSize="20sp" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recycler_med"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                tools:itemCount="2"
                                tools:listitem="@layout/item_medication_preview" />
                        </LinearLayout>


                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="80dp"
                        android:layout_marginBottom="50dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="20dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:gravity="center_vertical"
                            android:text="Cancel"
                            android:textColor="@color/doctor_main_color"
                            android:textSize="18sp"
                            android:visibility="gone" />

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_save"
                            android:layout_width="199dp"
                            android:layout_height="46dp"
                            android:layout_marginEnd="20dp"
                            android:visibility="visible"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="12dp"
                            app:strokeColor="@color/doctor_main_color"
                            app:strokeWidth="1dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:fontFamily="@font/roboto_medium"
                                android:gravity="center"
                                android:text="Save"
                                android:textColor="@color/doctor_main_color"
                                android:textSize="16sp" />

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_send"
                            android:layout_width="199dp"
                            android:layout_height="46dp"
                            android:layout_marginEnd="10dp"
                            app:cardBackgroundColor="@color/doctor_main_color"
                            app:cardCornerRadius="12dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:gravity="center"
                                android:orientation="horizontal">

                                <ImageView
                                    android:layout_width="19dp"
                                    android:layout_height="19dp"
                                    android:src="@drawable/print_icon" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:layout_marginStart="10dp"
                                    android:fontFamily="@font/roboto_medium"
                                    android:gravity="center"
                                    android:text="Save &amp; Print"
                                    android:textColor="@color/white"
                                    android:textSize="16sp" />
                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>


                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>