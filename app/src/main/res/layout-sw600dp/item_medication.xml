<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="105dp"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginBottom="10dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="5dp">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="91dp"
            android:layout_height="89dp"
            android:layout_margin="1dp"
            app:cardBackgroundColor="#F7F7F7"
            app:cardCornerRadius="6dp">

            <ImageView
                android:id="@+id/img_product"
                android:layout_width="37dp"
                android:layout_height="56dp"
                android:layout_gravity="center"
                android:src="@drawable/dummy_image_new" />
        </com.google.android.material.card.MaterialCardView>

        <LinearLayout
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:fontFamily="@font/roboto_bold"
                android:singleLine="true"
                android:id="@+id/txt_product_name"
                android:textColor="@color/black"
                android:textSize="21sp" />
            <TextView
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:fontFamily="@font/roboto_bold"
                android:singleLine="true"
                android:id="@+id/txt_store_name"
                android:textColor="@color/black"
                android:textSize="21sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_marginEnd="20dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Dose"
                        android:textColor="#B9B9B9"
                        android:textSize="15sp" />
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_medium"
                            android:id="@+id/txt_dose"
                            android:textColor="@color/black"
                            android:textSize="15sp" />
                    </LinearLayout>
                </LinearLayout>



                <LinearLayout
                    android:layout_marginEnd="20dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Course"
                        android:textColor="#B9B9B9"
                        android:textSize="15sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:id="@+id/txt_course"
                        android:textColor="@color/black"
                        android:textSize="15sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_marginEnd="20dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Price"
                        android:textColor="#B9B9B9"
                        android:textSize="15sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:id="@+id/txt_prize"
                        android:textColor="@color/black"
                        android:textSize="15sp" />
                </LinearLayout>


            </LinearLayout>


        </LinearLayout>
        <LinearLayout
            android:layout_marginEnd="15dp"
            android:id="@+id/img_remove"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_width="30dp"
            android:layout_height="30dp">
            <ImageView
                android:layout_gravity="center"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/delete_new"/>
        </LinearLayout>
    </LinearLayout>


</com.google.android.material.card.MaterialCardView>