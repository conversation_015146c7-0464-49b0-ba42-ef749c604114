<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">
        <LinearLayout
            android:layout_marginBottom="100dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="40dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_marginTop="40dp"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <ImageView
                    android:visibility="visible"
                    android:id="@+id/img_menu"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/menu"/>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="16dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center_vertical"
                    android:text="@string/settings"
                    android:textColor="@color/black"
                    android:textSize="22sp" />
            </LinearLayout>



            <LinearLayout
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical|top"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/rlt_image"
                    android:layout_width="165dp"
                    android:layout_height="170dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="50dp"
                    android:gravity="center_vertical">
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="160dp"
                        android:layout_height="160dp"
                        app:cardBackgroundColor="#edeeee"
                        app:strokeWidth="0dp"
                        app:cardCornerRadius="160dp">
                    <ImageView
                        android:id="@+id/img_profile"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/dummy_dp_new" />
                    </com.google.android.material.card.MaterialCardView>
                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="24dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentBottom="true"
                        android:layout_marginTop="-50dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="7dp"
                        android:src="@drawable/cam" />
                </RelativeLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Name"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="521dp"
                    android:layout_height="44dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:strokeColor="#24544C4C"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_name"
                        android:textSize="14dp"
                        android:textColor="#544C4C"
                        android:fontFamily="@font/roboto_regular"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="10dp"
                        android:singleLine="true"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:hint="name"
                        android:background="@android:color/transparent"/>
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Email"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="521dp"
                    android:layout_height="44dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:strokeColor="#24544C4C"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_email"
                        android:textSize="14dp"
                        android:textColor="#544C4C"
                        android:fontFamily="@font/roboto_regular"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="10dp"
                        android:singleLine="true"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:hint="email"
                        android:background="@android:color/transparent"/>
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Phone Number"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:strokeColor="#24544C4C"
                    app:strokeWidth="1dp">

                    <EditText

                        android:id="@+id/edit_phome"
                        android:textSize="14dp"
                        android:textColor="#544C4C"
                        android:fontFamily="@font/roboto_regular"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="10dp"
                        android:singleLine="true"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:hint="phone number"
                        android:inputType="phone"
                        android:background="@android:color/transparent"/>
                </com.google.android.material.card.MaterialCardView>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Alternative Number"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:strokeColor="#24544C4C"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_alt_phome"
                        android:textSize="14dp"
                        android:textColor="#544C4C"
                        android:fontFamily="@font/roboto_regular"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="10dp"
                        android:singleLine="true"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:hint="alternative number"
                        android:inputType="phone"
                        android:background="@android:color/transparent"/>
                </com.google.android.material.card.MaterialCardView>


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Date of Birth"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="521dp"
                    android:id="@+id/card_dob"
                    android:layout_height="44dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:strokeColor="#24544C4C"
                    app:strokeWidth="1dp">

                    <TextView
                        android:id="@+id/txt_dob"
                        android:textSize="14dp"
                        android:textColor="#544C4C"
                        android:fontFamily="@font/roboto_regular"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="10dp"
                        android:singleLine="true"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:hint="choose date"
                        android:inputType="textPassword"
                        android:background="@android:color/transparent"/>
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Civil ID"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="521dp"
                    android:layout_height="44dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:strokeColor="#24544C4C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">
                        <EditText
                            android:id="@+id/edit_civil_id"
                            android:textSize="14dp"
                            android:textColor="#544C4C"
                            android:fontFamily="@font/roboto_regular"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="10dp"
                            android:singleLine="true"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:text="civil id"
                            android:inputType="number"
                            android:background="@android:color/transparent"/>
                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="80dp"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical">
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="190dp"
                        app:cardCornerRadius="12dp"
                        android:id="@+id/card_save"
                        app:cardBackgroundColor="@color/doctor_main_color"
                        android:layout_height="46dp">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_medium"
                            android:gravity="center"
                            android:text="Save "
                            android:layout_gravity="center"
                            android:textColor="@color/white"
                            android:textSize="18dp" />
                    </com.google.android.material.card.MaterialCardView>
                    <TextView
                        android:id="@+id/card_reset"
                        android:layout_width="100dp"
                        android:layout_height="46dp"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:layout_marginStart="30dp"
                        android:text="Reset"
                        android:layout_gravity="center"
                        android:textColor="@color/doctor_main_color"
                        android:textSize="18dp" />
                </LinearLayout>
                <LinearLayout
                    android:visibility="gone"
                    android:id="@+id/lyt_log_out"
                    android:layout_width="150dp"
                    android:layout_height="50dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="50dp"
                    android:layout_marginBottom="100dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/log_out_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="14dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="@string/log_out"
                        android:textColor="#FF0000"
                        android:textSize="14dp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
