<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="40dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_back"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:visibility="gone"
                    app:cardBackgroundColor="#F7F7F7"
                    app:cardCornerRadius="10dp">

                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center"
                        android:src="@drawable/brown_back" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center_vertical"
                    android:text=""
                    android:textColor="@color/black"
                    android:textSize="34sp" />
            </LinearLayout>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="50dp"
                android:layout_marginBottom="100dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="20dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <LinearLayout
                            android:layout_weight="1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">


                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Patient Information"
                                android:textColor="@color/black"
                                android:textSize="24sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="160dp"
                                android:layout_marginTop="15dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="9dp"
                                app:strokeColor="#54DFDFDF"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="center_vertical"
                                    android:orientation="vertical"
                                    android:padding="10dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Name"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:id="@+id/txt_patient_name"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="DOB"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:id="@+id/txt_dob"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Civil Id"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:id="@+id/txt_civil_id"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Phone"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:id="@+id/txt_phone"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>


                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:id="@+id/txt_address_details"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="30dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Address Information"
                                android:textColor="@color/black"
                                android:textSize="24sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="243dp"
                                android:layout_marginTop="15dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="9dp"
                                android:id="@+id/card_address"
                                app:strokeColor="#54DFDFDF"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="center_vertical"
                                    android:orientation="vertical"
                                    android:padding="10dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Governorate"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:id="@+id/txt_governorate"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Area"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:id="@+id/txt_area"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Block Number"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:id="@+id/txt_block"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Street"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:id="@+id/txt_street"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Building"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:id="@+id/txt_building"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginTop="15dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:text="Floor"
                                            android:textColor="#80000000"
                                            android:textSize="14sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:id="@+id/txt_floor"
                                            android:textColor="@color/black"
                                            android:textSize="14sp" />
                                    </LinearLayout>


                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>
                        </LinearLayout>
                        <View
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:background="#17000000"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="20dp"/>
                        <LinearLayout
                            android:layout_weight="1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Prescription Details"
                                android:textColor="@color/black"
                                android:textSize="24sp" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recycler_med"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                tools:itemCount="2"
                                tools:listitem="@layout/item_medication_preview" />
                        </LinearLayout>


                    </LinearLayout>


               
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>