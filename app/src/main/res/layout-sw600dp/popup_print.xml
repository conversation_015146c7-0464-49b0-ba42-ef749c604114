<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"

    >

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="17dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="18dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="100dp"
                    android:layout_marginBottom="30dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"

                        android:fontFamily="@font/roboto_bold"
                        android:text="Print Prescription"
                        android:textColor="@color/black"
                        android:textSize="24sp" />

                    <ImageView
                        android:id="@+id/img_close"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/close_filter" />
                </LinearLayout>


                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    app:cardBackgroundColor="@color/white"
                    app:strokeColor="#14000000"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:padding="26dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginTop="10dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="115dp"
                                android:layout_height="67dp"
                                android:src="@drawable/wasfa_logo" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="end"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"

                                    android:fontFamily="@font/roboto_bold"
                                    android:text="RX PRESCRIPTION"
                                    android:textColor="#0A2540"
                                    android:textSize="24sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:fontFamily="@font/roboto_bold"
                                    android:text="WASFA"
                                    android:textColor="#0A2540"
                                    android:textSize="16sp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:fontFamily="@font/roboto_regular"
                                    android:text="RX Id: : 100021-8657"
                                    android:textColor="#0A2540"
                                    android:textSize="14sp" />
                            </LinearLayout>
                        </LinearLayout>

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="50dp"
                            app:cardBackgroundColor="@color/white"
                            app:strokeColor="#DFE4EA"
                            app:strokeWidth="1dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_vertical"
                                android:gravity="center_vertical"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_margin="10dp"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="5dp"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_bold"
                                        android:text="Sl No"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_bold"
                                        android:text="Product Name"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_bold"
                                        android:text="Qty"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_bold"
                                        android:text="Frequency"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_bold"
                                        android:text="Dose Time"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_bold"
                                        android:text="Additional Notes"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                </LinearLayout>

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:background="#DFE4EA" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginTop="20dp"
                                    android:layout_marginEnd="10dp"
                                    android:layout_marginBottom="20dp"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="5dp"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="1"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="Cefim 400 Mg 6 Capsule"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="1"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="Weekly (1)  "
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="After Meal"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="Not here"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                </LinearLayout>

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:background="#DFE4EA" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginTop="20dp"
                                    android:layout_marginEnd="10dp"
                                    android:layout_marginBottom="20dp"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="5dp"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="2"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="Cefim 400 Mg 6 Capsule"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="1"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="Weekly (1)  "
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="After Meal"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:text="Not here"
                                        android:textColor="#0A2540"
                                        android:textSize="12sp" />

                                </LinearLayout>
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="379dp"
                            android:layout_height="107dp"
                            android:layout_gravity="end"
                            android:layout_marginTop="50dp"
                            android:layout_marginBottom="30dp"
                            app:cardBackgroundColor="#F9F9FA"
                            app:strokeColor="#DFE4EA"
                            app:strokeWidth="1dp">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="horizontal"
                                android:padding="20dp">

                                <TextView
                                    android:layout_width="166dp"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentStart="true"

                                    android:fontFamily="@font/roboto_regular"
                                    android:text="Scan here to recieve the Prescription"
                                    android:textColor="@color/black"
                                    android:textSize="15sp" />

                                <ImageView
                                    android:layout_width="68dp"
                                    android:layout_height="68dp"
                                    android:layout_alignParentEnd="true"
                                    android:src="@drawable/bar_code" />
                            </RelativeLayout>
                        </com.google.android.material.card.MaterialCardView>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>


                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:id="@+id/card_print"
                    android:layout_marginTop="30dp"
                    app:cardBackgroundColor="@color/doctor_main_color"
                    app:cardCornerRadius="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"

                        android:fontFamily="@font/roboto_bold"
                        android:text="Print"
                        android:textColor="@color/white"
                        android:textSize="18sp" />
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>


        </com.google.android.material.card.MaterialCardView>
    </androidx.core.widget.NestedScrollView>

</RelativeLayout>
