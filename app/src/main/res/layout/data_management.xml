<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="135dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_seller_one"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Seller"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="NOS"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Seller Missing"
                        android:textColor="@color/white"
                        android:textSize="11sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Product Count"
                        android:textColor="@color/white"
                        android:textSize="11sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="2149"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="135dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_seller_two"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Seller"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="NOS"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Seller Commission"
                        android:textColor="@color/white"
                        android:textSize="11sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="with NIL"
                        android:textColor="@color/white"
                        android:textSize="11sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21637"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="11dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="135dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_influencer_one"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Influencer "
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="NOS"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Influencer Commission"
                        android:textColor="@color/white"
                        android:textSize="11sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="TableCount"
                        android:textColor="@color/white"
                        android:textSize="11sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="17341"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="135dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_influencer_two"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Influencer "
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="NOS"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Influencer Commission"
                        android:textColor="@color/white"
                        android:textSize="11sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Table Count"
                        android:textColor="@color/white"
                        android:textSize="11sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="62964"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>
</LinearLayout>