<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"

    >
<androidx.core.widget.NestedScrollView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true"
    android:scrollbars="none">
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="17dp"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="16dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/img_product"
                    android:layout_width="68dp"
                    android:layout_height="68dp"
                    android:src="@drawable/dummy_image_new" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/txt_product_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/txt_store_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Store Name"
                        android:textColor="#B9B9B9"
                        android:textSize="14sp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/txt_prize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="@color/doctor_main_color"
                    android:textSize="15sp" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:fontFamily="@font/roboto_bold"
                android:text="Dosage"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_dose"
                android:layout_height="41dp"
                android:layout_marginTop="15dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_selected_dose"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:text="Select"
                        android:textColor="#B4ABAB" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="5dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#8C7D7D" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="41dp"
                android:id="@+id/card_dose_day"
                android:layout_marginTop="15dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_dose_selected_day"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:text="Select"
                        android:textColor="#B4ABAB" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="5dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#8C7D7D" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>


            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_dose_time"
                android:layout_height="40dp"
                android:layout_marginTop="15dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_dose_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:text="Select"
                        android:textColor="#B4ABAB" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="5dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#8C7D7D" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/roboto_medium"
                android:text="Course"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_course_duration"
                android:layout_height="40dp"
                android:layout_marginTop="15dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_course_duration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:text="Select"
                        android:textColor="#B4ABAB" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="5dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#8C7D7D" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:id="@+id/card_course_day"
                android:layout_marginTop="15dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_course_day"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:text="Select"
                        android:textColor="#B4ABAB" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="5dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#8C7D7D" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:fontFamily="@font/roboto_medium"
                android:text="Additional Notes"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="15dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/edit_notes"
                        android:background="@android:color/transparent"
                        android:textSize="14dp"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="8dp"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:hint="Additional Notes"
                        android:textColorHint="#B4ABAB" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_add"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:layout_marginTop="40dp"
                android:layout_marginBottom="1dp"
                app:cardBackgroundColor="@color/doctor_main_color"
                app:cardCornerRadius="12dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:text="Add"
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_cancel"
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:layout_marginTop="15dp"
                android:layout_marginBottom="60dp"
                app:cardBackgroundColor="#F7EBF0"
                app:cardCornerRadius="12dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:text="Cancel"
                    android:textColor="@color/doctor_main_color"
                    android:textSize="16sp" />
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.core.widget.NestedScrollView>
</RelativeLayout>
