<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/lyt_menu"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:clickable="true"
    android:focusable="true"
    android:visibility="visible">

    <androidx.core.widget.NestedScrollView
        android:layout_width="270dp"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none"
        tools:ignore="MissingConstraints">

        <LinearLayout
            android:layout_width="270dp"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:orientation="vertical">

            <LinearLayout

                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="24dp"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_centerInParent="true"
                        android:layout_alignParentStart="true"
                        android:layout_gravity="center_vertical"
                        android:layout_width="100dp"
                        android:layout_height="50dp"
                        android:src="@drawable/wasfa_logo" />

                    <LinearLayout
                        android:layout_alignParentEnd="true"
                        android:layout_centerInParent="true"
                        android:id="@+id/img_back"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="end|center"
                        android:gravity="end"
                        android:layout_marginEnd="15dp">

                        <ImageView
                            android:layout_gravity="center"
                            app:tint="@color/black"
                            android:id="@+id/img_menu_back"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/arrow_white_right" />
                    </LinearLayout>


                </RelativeLayout>

                <!--need to start here-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/lyt_pos"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="50dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="18dp"
                            android:layout_height="27dp"
                            android:layout_marginEnd="14dp"
                            android:src="@drawable/menu" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="POS System"
                            android:textColor="@color/black"
                            android:textSize="13dp" />

                        <ImageView
                            app:tint="@color/black"
                            android:id="@+id/img_pos_arrow"
                            android:layout_width="18dp"
                            android:rotation="270"
                            android:layout_height="20dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/arrow_down_menu" />
                    </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/lyt_pos_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <LinearLayout
                            android:id="@+id/lyt_pos_rx"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_marginStart="10dp"
                                android:layout_width="18dp"
                                android:layout_height="27dp"
                                android:layout_marginEnd="14dp"
                                android:src="@drawable/bullet" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_regular"
                                android:text="POS RX"
                                android:textColor="@color/black"
                                android:textSize="12sp" />


                        </LinearLayout>
                        <LinearLayout
                            android:layout_marginBottom="10dp"
                            android:id="@+id/lyt_pos_shop"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_marginStart="10dp"
                                android:layout_width="18dp"
                                android:layout_height="27dp"
                                android:layout_marginEnd="14dp"
                                android:src="@drawable/bullet" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_regular"
                                android:text="POS Shop"
                                android:textColor="@color/black"
                                android:textSize="12sp" />


                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#40008F96" />

                    <LinearLayout
                        android:id="@+id/lyt_products"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="17dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="15dp"
                            android:layout_marginEnd="11dp"
                            android:src="@drawable/menu_product_icon" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Products"
                            android:textColor="@color/black"
                            android:textSize="13dp" />

                        <ImageView
                            app:tint="@color/black"
                            android:id="@+id/img_product_arrow"
                            android:layout_width="18dp"
                            android:rotation="270"
                            android:layout_height="20dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/arrow_down_menu" />
                    </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/lyt_product_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <LinearLayout
                            android:id="@+id/lyt_add_new_product"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_marginStart="10dp"
                                android:layout_width="18dp"
                                android:layout_height="27dp"
                                android:layout_marginEnd="14dp"
                                android:src="@drawable/bullet" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Add New Product"
                                android:textColor="@color/black"
                                android:textSize="12sp" />


                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/lyt_all_product"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_marginStart="10dp"
                                android:layout_width="18dp"
                                android:layout_height="27dp"
                                android:layout_marginEnd="14dp"
                                android:src="@drawable/bullet" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_regular"
                                android:text="All Products"
                                android:textColor="@color/black"
                                android:textSize="12sp" />


                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/lyt_category"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_marginStart="10dp"
                                android:layout_width="18dp"
                                android:layout_height="27dp"
                                android:layout_marginEnd="14dp"
                                android:src="@drawable/bullet" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Category"
                                android:textColor="@color/black"
                                android:textSize="12sp" />


                        </LinearLayout>
                        <LinearLayout
                            android:layout_marginBottom="10dp"
                            android:id="@+id/lyt_brand"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_marginStart="10dp"
                                android:layout_width="18dp"
                                android:layout_height="27dp"
                                android:layout_marginEnd="14dp"
                                android:src="@drawable/bullet" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Brand"
                                android:textColor="@color/black"
                                android:textSize="12sp" />


                        </LinearLayout>
                    </LinearLayout>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#40008F96" />

                    <LinearLayout
                        android:id="@+id/lyt_customers"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="17dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_marginEnd="11dp"
                            android:src="@drawable/menu_customers_icon" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Customers"
                            android:textColor="@color/black"
                            android:textSize="13dp" />

                        <ImageView
                            app:tint="@color/black"
                            android:id="@+id/img_customer_arrow"
                            android:layout_width="18dp"
                            android:rotation="270"
                            android:layout_height="20dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/arrow_down_menu" />
                    </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/lyt_customer_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <LinearLayout
                            android:id="@+id/lyt_customer_list"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_marginStart="10dp"
                                android:layout_width="18dp"
                                android:layout_height="27dp"
                                android:layout_marginEnd="14dp"
                                android:src="@drawable/bullet" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Customer list"
                                android:textColor="@color/black"
                                android:textSize="12sp" />


                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/lyt_segment"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_marginStart="10dp"
                                android:layout_width="18dp"
                                android:layout_height="27dp"
                                android:layout_marginEnd="14dp"
                                android:src="@drawable/bullet" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Segmant"
                                android:textColor="@color/black"
                                android:textSize="12sp" />


                        </LinearLayout>
                        <LinearLayout
                            android:layout_marginBottom="10dp"
                            android:id="@+id/lyt_type"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_marginStart="10dp"
                                android:layout_width="18dp"
                                android:layout_height="27dp"
                                android:layout_marginEnd="14dp"
                                android:src="@drawable/bullet" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Type"
                                android:textColor="@color/black"
                                android:textSize="12sp" />


                        </LinearLayout>
                    </LinearLayout>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#40008F96" />


                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"/>
                <LinearLayout
                    android:id="@+id/lyt_log_out"
                    android:layout_width="150dp"
                    android:layout_height="50dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="50dp"
                    android:layout_marginBottom="50dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        app:tint="#FF0000"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/log_out_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="14dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="@string/log_out"
                        android:textColor="#FF0000"
                        android:textSize="14dp" />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>