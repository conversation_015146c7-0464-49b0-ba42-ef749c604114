<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="168dp"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    app:strokeWidth="0dp"
    app:cardElevation="2dp"
    android:layout_marginBottom="10dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="94dp"
                app:strokeWidth="0dp"
                android:layout_height="148dp"
                app:cardBackgroundColor="#F7F7F7"
                app:cardCornerRadius="6dp">

                <ImageView
                    android:layout_width="67dp"
                    android:layout_height="97dp"
                    android:layout_gravity="center"
                    android:id="@+id/img_product" />
            </com.google.android.material.card.MaterialCardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="20dp"
                        app:cardCornerRadius="50dp"
                        app:cardBackgroundColor="@color/white"
                        android:layout_height="20dp">
                        <ImageView
                            android:id="@+id/img_seller"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            />
                    </com.google.android.material.card.MaterialCardView>


                    <TextView
                        android:id="@+id/txt_store_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Store Name"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:fontFamily="@font/roboto_bold"
                    android:maxLines="2"
                    android:minHeight="35dp"
                    android:id="@+id/txt_product_name"
                    android:textColor="@color/black"
                    android:textSize="15sp" />
                <Switch
                    android:visibility="gone"
                    android:id="@+id/switch_fav"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerInParent="true"
                    android:checked="false"
                    android:thumbTint="@android:color/white"
                    android:track="@drawable/custom_switch_thumb" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Stock"
                        android:textColor="#80000000"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/txt_stock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="5dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Price"
                        android:textColor="#80000000"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/txt_prize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>


    </LinearLayout>

</com.google.android.material.card.MaterialCardView>