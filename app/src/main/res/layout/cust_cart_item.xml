<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:id="@+id/card_cust"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    app:strokeWidth="0dp"
    app:cardBackgroundColor="@android:color/transparent">

    <TextView
        android:id="@+id/txt_customer"
        android:gravity="center_vertical"
        android:layout_width="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_height="match_parent"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:fontFamily="@font/roboto_regular"
        android:text="Seller Discount"
        android:textColor="@color/black"
        android:textSize="13sp" />
</com.google.android.material.card.MaterialCardView>