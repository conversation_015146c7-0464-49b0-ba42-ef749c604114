<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="20dp"
    android:background="@color/white"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="250dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/txt_heading"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/roboto_bold"
            android:text="Assign Influencer"
            android:textColor="#424546"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/img_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="27dp"
            android:src="@drawable/close_filter" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="15dp"
        android:background="#7D7C7C" />
    <TextView
        android:visibility="gone"
        android:id="@+id/txt_no_data"
        android:layout_gravity="center"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:fontFamily="@font/roboto_regular"
        android:text="No Data Found"
        android:textColor="#424546"
        android:textSize="16sp" />
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_influencers"
        android:layout_width="match_parent"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:overScrollMode="never"
        android:scrollbars="vertical" />

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:id="@+id/card_change"
        android:layout_height="38dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="10dp"
        app:cardBackgroundColor="@color/doctor_main_color"
        app:cardCornerRadius="12dp">

        <TextView
            android:visibility="visible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:textColor="@color/white"
            android:text="Change"
            android:textSize="16sp"
            android:fontFamily="@font/roboto_medium"/>
    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
