<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="118dp"
    android:elevation="2dp"
    android:layout_marginBottom="10dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="20dp"
        android:paddingEnd="20dp">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="97dp"
            android:layout_height="97dp"
            app:cardBackgroundColor="#F5F6FA"
            app:strokeWidth="0dp">

            <ImageView
                android:src="@drawable/dummy_image"
                android:layout_gravity="center"
                android:id="@+id/img_order"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="10dp" />
        </com.google.android.material.card.MaterialCardView>
        <LinearLayout
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_marginStart="20dp"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical">
            <TextView
                android:singleLine="true"
                android:id="@+id/txt_order_name"
                android:fontFamily="@font/roboto_medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="hdeiufhwierf"
                android:textSize="16sp"
                android:textColor="#1D1E20"/>
            <TextView
                android:singleLine="true"
                android:id="@+id/txt_order_prize"
                android:fontFamily="@font/roboto_regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="KD 0.000"
                android:textSize="14sp"
                android:textColor="#A61C5C"/>
            <TextView
                android:singleLine="true"
                android:id="@+id/txt_order_quantity"
                android:fontFamily="@font/roboto_regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="KD 0.000"
                android:textSize="14sp"
                android:textColor="#222222"/>
        </LinearLayout>
        <LinearLayout
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_marginStart="10dp"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical">
            <TextView
                android:singleLine="true"
                android:id="@+id/txt_order_delivery"
                android:fontFamily="@font/roboto_medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Delivered on Jun 25"
                android:textSize="16sp"
                android:textColor="#1D1E20"/>
            <TextView
                android:singleLine="true"
                android:id="@+id/txt_order_id"
                android:fontFamily="@font/roboto_medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ORDER ID:1255"
                android:textSize="16sp"
                android:textColor="#1D1E20"/>
            <TextView
                android:singleLine="true"
                android:id="@+id/txt_order_return_policy"
                android:fontFamily="@font/roboto_regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Return policy valid till Aug 04"
                android:textSize="14sp"
                android:textColor="#E2B4C9"/>
        </LinearLayout>
        <ImageView
            android:layout_gravity="top"
            android:layout_marginTop="20dp"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/view_icon"/>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>