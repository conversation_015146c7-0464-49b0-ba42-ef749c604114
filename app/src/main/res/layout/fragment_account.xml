<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="20dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="20dp"
        android:orientation="vertical">
        <ImageView
            android:visibility="gone"
            android:id="@+id/img_menu"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/menu"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/roboto_bold"
            android:gravity="center"
            android:text="Profile"
            android:textColor="@color/black"
            android:textSize="16dp" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/rlt_image"
                    android:layout_width="165dp"
                    android:layout_height="160dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="50dp"
                    android:gravity="center">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="160dp"
                        android:layout_height="160dp"
                        app:cardBackgroundColor="#edeeee"
                        app:strokeWidth="0dp"
                        app:cardCornerRadius="160dp">

                        <ImageView
                            android:id="@+id/img_profile"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent" />
                    </com.google.android.material.card.MaterialCardView>


                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="24dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentBottom="true"
                        android:layout_marginTop="70dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="7dp"
                        android:src="@drawable/cam" />
                </RelativeLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Name"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:strokeColor="#24544C4C"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_name"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="10dp"
                        android:background="@android:color/transparent"
                        android:fontFamily="@font/roboto_regular"
                        android:hint="name"
                        android:singleLine="true"
                        android:textColorHint="#544C4C"
                        android:textSize="14dp" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Email"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:strokeColor="#24544C4C"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_email"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="10dp"
                        android:background="@android:color/transparent"
                        android:fontFamily="@font/roboto_regular"
                        android:hint="email"
                        android:singleLine="true"
                        android:textColorHint="#544C4C"
                        android:textSize="14dp" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Phone Number"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:strokeColor="#24544C4C"
                    app:strokeWidth="1dp">

                    <EditText

                        android:id="@+id/edit_phome"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="10dp"
                        android:background="@android:color/transparent"
                        android:fontFamily="@font/roboto_regular"
                        android:hint="phone number"
                        android:inputType="phone"
                        android:singleLine="true"
                        android:textColorHint="#544C4C"
                        android:textSize="14dp" />
                </com.google.android.material.card.MaterialCardView>


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="New Password"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:strokeColor="#24544C4C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/edit_new_password"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="10dp"
                            android:background="@android:color/transparent"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:inputType="textPassword"
                            android:singleLine="true"
                            android:hint="new password"
                            android:textColorHint="#544C4C"
                            android:textSize="14dp" />


                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Confirm Password"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:strokeColor="#24544C4C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/edit_confirm_password"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="20dp"
                            android:layout_marginEnd="10dp"
                            android:background="@android:color/transparent"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:inputType="textPassword"
                            android:singleLine="true"
                            android:hint="confirm password"
                            android:textColorHint="#544C4C"
                            android:textSize="14dp" />


                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="80dp"

                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_save"
                        android:layout_width="190dp"
                        android:layout_height="46dp"
                        app:cardBackgroundColor="@color/doctor_main_color"
                        app:cardCornerRadius="12dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:fontFamily="@font/roboto_medium"
                            android:gravity="center"
                            android:text="Save "
                            android:textColor="@color/white"
                            android:textSize="18dp" />
                    </com.google.android.material.card.MaterialCardView>

                    <TextView
                        android:id="@+id/card_reset"
                        android:layout_width="100dp"
                        android:layout_height="46dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="30dp"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:text="Reset"
                        android:textColor="@color/doctor_main_color"
                        android:textSize="18dp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lyt_log_out"
                    android:layout_width="150dp"
                    android:layout_height="50dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="50dp"
                    android:layout_marginBottom="100dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        app:tint="#FF0000"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/log_out_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="14dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="@string/log_out"
                        android:textColor="#FF0000"
                        android:textSize="14dp" />
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
