<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="156dp"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginBottom="10dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="5dp"
            android:layout_marginTop="5dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="62dp"
                android:layout_height="62dp"
                app:cardBackgroundColor="#f2f2f2"
                app:cardCornerRadius="64dp">

                <ImageView
                    android:id="@+id/img_product"
                    android:layout_width="55dp"
                    android:layout_height="55dp"
                    android:layout_gravity="center"
                    android:src="@drawable/dummy_med" />
            </com.google.android.material.card.MaterialCardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:maxLines="2"
                    android:id="@+id/txt_product_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Panadol"
                    android:textColor="@color/black"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/txt_store_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:fontFamily="@font/roboto_regular"
                    android:textColor="#B9B9B9"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/img_remove"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_gravity="center"
                android:gravity="center">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:src="@drawable/delete_new" />
            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|top"
            android:layout_marginStart="5dp"
            android:layout_marginTop="25dp"
            android:gravity="center_vertical|top"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/med_bg_icon" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Dose"
                    android:textColor="#99A61C5C"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/txt_dose"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="@color/doctor_main_color"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Course"
                    android:textColor="#99A61C5C"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/txt_course"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="@color/doctor_main_color"
                    android:textSize="12sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/txt_prize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="10dp"
                android:fontFamily="@font/roboto_bold"
                android:textColor="@color/doctor_main_color"
                android:textSize="15sp" />
        </LinearLayout>
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>