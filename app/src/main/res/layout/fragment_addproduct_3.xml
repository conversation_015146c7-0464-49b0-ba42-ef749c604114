<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentTop="true"

                android:fillViewport="true"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="21dp"
                            android:layout_marginTop="30dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Gallery Images"
                            android:textColor="@color/black"
                            android:textSize="16sp" />




                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="71dp"
                            android:layout_marginStart="13dp"
                            android:layout_marginTop="25dp"
                            android:layout_marginEnd="13dp"
                            android:background="@drawable/dashed_border"
                            app:cardElevation="0dp"
                            app:cardCornerRadius="6dp"
                            app:cardBackgroundColor="@android:color/transparent">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:gravity="center"
                                android:orientation="vertical">


                                <TextView
                                    android:id="@+id/txt_browse"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="9dp"
                                    android:fontFamily="@font/roboto_regular"
                                    android:textColor="#D9D9D9"
                                    android:textSize="13sp" />


                            </LinearLayout>
                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="13dp"
                            android:layout_marginEnd="13dp"
                            android:id="@+id/recycler_images"
                            android:layout_marginTop="40dp"/>

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="21dp"
                            android:layout_marginTop="30dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Thumbnail Images"
                            android:textColor="@color/black"
                            android:textSize="16sp" />




                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="71dp"
                            android:layout_marginStart="13dp"
                            android:layout_marginTop="25dp"
                            android:layout_marginEnd="13dp"
                            android:background="@drawable/dashed_border"
                            app:cardElevation="0dp"
                            app:cardCornerRadius="6dp"
                            app:cardBackgroundColor="@android:color/transparent">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:gravity="center"
                                android:orientation="vertical">


                                <TextView
                                    android:id="@+id/txt_browse_thumb"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="9dp"
                                    android:fontFamily="@font/roboto_regular"
                                    android:textColor="#D9D9D9"
                                    android:textSize="13sp" />


                            </LinearLayout>
                        </LinearLayout>

                    <LinearLayout
                        android:id="@+id/lyt_thumb"
                        android:visibility="gone"
                        android:layout_marginTop="40dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginEnd="13dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="54dp"
                            android:layout_height="45dp"
                            app:cardBackgroundColor="#33D9D9D9"
                            app:cardCornerRadius="10dp"
                            app:strokeWidth="0dp">

                            <ImageView
                                android:id="@+id/img_list"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:padding="10dp" />
                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/txt_image_name"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_regular"
                                android:textColor="@color/black"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/txt_image_size"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:fontFamily="@font/roboto_regular"
                                android:textColor="#B9B9B9"
                                android:textSize="10sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/btn_dot"
                            android:layout_marginEnd="10dp"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_gravity="center"
                            android:gravity="center">
                            <ImageView
                                android:layout_gravity="center"
                                android:layout_width="15dp"
                                android:layout_height="3dp"
                                android:src="@drawable/three_dot_image_list"/>
                        </LinearLayout>
                    </LinearLayout>

                    </LinearLayout>
                </LinearLayout>

            </androidx.core.widget.NestedScrollView>


        </RelativeLayout>

    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>