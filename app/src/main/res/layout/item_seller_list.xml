<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="94dp"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginBottom="8dp"
    app:cardBackgroundColor="#F8F5F5"
    app:cardCornerRadius="5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:gravity="center_vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:orientation="vertical">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_bold"
                android:singleLine="true"
                android:text="Royal Pharmacy"
                android:textColor="@color/black"
                android:textSize="13sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_regular"
                android:singleLine="true"
                android:layout_marginTop="5dp"
                android:text="+96555999467"
                android:textColor="@color/black"
                android:textSize="13sp" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_regular"
                android:singleLine="true"
                android:layout_marginTop="5dp"
                android:text="<EMAIL>"
                android:textColor="@color/black"
                android:textSize="13sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_gravity="center|end"
            android:gravity="center|end"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="15dp"
            android:orientation="horizontal">

      <com.google.android.material.card.MaterialCardView
          android:layout_width="35dp"
          app:cardCornerRadius="50dp"
          android:layout_gravity="center"
          app:cardBackgroundColor="#d4d5d8"
          android:layout_height="35dp">
          <ImageView
              android:layout_gravity="center"
              android:layout_width="20dp"
              android:layout_height="20dp"
              android:src="@drawable/apix_margin_icon"/>
      </com.google.android.material.card.MaterialCardView>


        </LinearLayout>
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>