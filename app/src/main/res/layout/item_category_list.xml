<com.chauthai.swipereveallayout.SwipeRevealLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginBottom="10dp"
    android:orientation="horizontal"
    app:dragEdge="right"
    app:mode="normal">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="100dp"
        android:gravity="center">

        <LinearLayout
            android:id="@+id/layout_swipe_actions"
            android:layout_width="100dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/btn_delete"
                android:layout_width="35dp"
                android:layout_height="35dp"
                app:cardBackgroundColor="#5DB245"
                app:cardCornerRadius="35dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:src="@drawable/icon_delete_cart"
                    app:tint="@color/white" />
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/btn_edit"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_marginTop="10dp"
                app:cardBackgroundColor="#5DB245"
                app:cardCornerRadius="35dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:src="@drawable/edit_icon_single"
                    app:tint="@color/white" />
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </FrameLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="100dp"
        android:visibility="visible">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:elevation="5dp"
            android:orientation="vertical"

            app:cardBackgroundColor="#F8F5F5"
            app:cardCornerRadius="10dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="10dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="90dp"
                    android:layout_height="75dp"
                    app:strokeWidth="0dp"
                    app:cardBackgroundColor="#F5F6FA"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="1dp">

                    <ImageView
                        android:id="@+id/img_icon"
                        android:layout_width="77dp"
                        android:layout_height="74dp"
                        android:layout_gravity="center"
                        />
                </com.google.android.material.card.MaterialCardView>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/txt_cat_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:fontFamily="@font/roboto_medium"
                        android:singleLine="true"
                        android:textColor="#1D1E20"
                        android:textSize="14sp" />

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="wrap_content"
                        android:id="@+id/card_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="1dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="38dp"
                        app:strokeColor="#424546"
                        app:strokeWidth="1dp">

                        <TextView
                            android:id="@+id/txt_parent"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="10dp"
                            android:layout_marginBottom="2dp"
                            android:fontFamily="@font/roboto_medium"
                            android:singleLine="true"
                            android:textColor="#1D1E20"
                            android:textSize="12sp" />
                    </com.google.android.material.card.MaterialCardView>

                    <TextView
                        android:id="@+id/txt_order_level"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:layout_marginTop="5dp"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"

                        android:textColor="#1D1E20"
                        android:textSize="11sp" />
                </LinearLayout>

                <Switch
                    android:id="@+id/switch_featured"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerInParent="true"
                    android:layout_marginEnd="7dp"
                    android:checked="false"
                    android:thumbTint="@android:color/white"
                    android:track="@drawable/custom_switch_thumb" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </FrameLayout>
</com.chauthai.swipereveallayout.SwipeRevealLayout>
