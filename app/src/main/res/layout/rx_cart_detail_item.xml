<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="281dp"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginBottom="12dp"
    app:cardBackgroundColor="#FEFEFE"
    app:cardCornerRadius="10dp"
    app:cardElevation="5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="100dp"
                android:layout_height="85dp"
                app:cardBackgroundColor="#F5F6FA"
                app:cardCornerRadius="10dp"
                app:cardElevation="1dp">

                <ImageView
                    android:layout_width="77dp"
                    android:layout_height="74dp"
                    android:layout_gravity="center"
                    android:src="@drawable/dummy_image" />
            </com.google.android.material.card.MaterialCardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_medium"
                    android:singleLine="true"
                    android:text="Livostin Nasal Spray 10Ml"
                    android:textColor="#1D1E20"
                    android:textSize="13sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="11dp"
                    android:fontFamily="@font/roboto_regular"
                    android:text="KD 0.000"
                    android:textColor="#8F959E"
                    android:textSize="13sp" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="14dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_decrease_count"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginStart="1dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="1dp"
                        app:cardBackgroundColor="@color/white"
                        app:strokeColor="#DEDEDE"
                        app:strokeWidth="1dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:src="@drawable/arrow_down"

                            app:tint="#8F959E" />
                    </com.google.android.material.card.MaterialCardView>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:singleLine="true"
                        android:text="1"
                        android:textColor="#1D1E20"
                        android:textSize="13sp" />

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_increase_count"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginEnd="1dp"
                        android:layout_marginBottom="1dp"
                        app:cardBackgroundColor="@color/white"
                        app:strokeColor="#DEDEDE"
                        app:strokeWidth="1dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:src="@drawable/arrow_down"

                            app:tint="#8F959E" />
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>
            </LinearLayout>

            <ImageView
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginStart="5dp"
                android:src="@drawable/icon_delete_cart" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="3dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"

                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Dose"
                    android:textColor="@color/black"
                    android:textSize="12sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="5dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="5dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:singleLine="true"
                            android:text="Select"
                            android:textColor="#B4ABAB" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#8C7D7D" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Course Duration"
                    android:textColor="@color/black"
                    android:textSize="12sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="5dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="5dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:singleLine="true"
                            android:text="Select"
                            android:textColor="#B4ABAB" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#8C7D7D" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="3dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"

                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Dose"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    android:visibility="invisible" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="5dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="5dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:singleLine="true"
                            android:text="Select"
                            android:textColor="#B4ABAB" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#8C7D7D" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Course Duration"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    android:visibility="invisible" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="5dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="5dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:singleLine="true"
                            android:text="Select"
                            android:textColor="#B4ABAB" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#8C7D7D" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:layout_weight="2"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"

                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Dose Time"
                    android:textColor="@color/black"
                    android:textSize="12sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="5dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="5dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:singleLine="true"
                            android:text="Select"
                            android:textColor="#B4ABAB" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#8C7D7D" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Additional Notes"
                    android:textColor="@color/black"
                    android:textSize="12sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="5dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="5dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:fontFamily="@font/roboto_regular"
                            android:singleLine="true"
                            android:text="Additional Notes"
                            android:textColor="#B4ABAB" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>