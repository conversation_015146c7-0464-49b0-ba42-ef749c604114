<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">



        <RelativeLayout
            android:layout_marginTop="30dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentTop="true"

                android:fillViewport="true"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="23dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="1dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="5dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="21dp"
                                android:layout_marginTop="11dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Shipping Configuration"
                                android:textColor="#424546"
                                android:textSize="16sp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginTop="10dp"
                                android:background="#40000000" />


                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="17dp"
                                android:layout_marginTop="19dp"
                                android:layout_marginEnd="17dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="22dp"

                                    android:fontFamily="@font/roboto_regular"
                                    android:text="Free Shipping"
                                    android:textColor="@color/black"
                                    android:textSize="12sp" />

                                <Switch
                                    android:id="@+id/switch_free"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerInParent="true"
                                    android:checked="true"
                                    android:thumbTint="@android:color/white"
                                    android:track="@drawable/custom_switch_thumb" />
                            </RelativeLayout>

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="17dp"
                                android:layout_marginTop="19dp"
                                android:layout_marginEnd="17dp"
                                android:layout_marginBottom="30dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="22dp"
                                    android:fontFamily="@font/roboto_regular"
                                    android:text="Flat Rate"
                                    android:textColor="@color/black"
                                    android:textSize="12sp" />

                                <Switch
                                    android:id="@+id/switch_flat"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerInParent="true"
                                    android:checked="true"
                                    android:thumbTint="@android:color/white"
                                    android:track="@drawable/custom_switch_thumb" />
                            </RelativeLayout>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="1dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="5dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="21dp"
                                android:layout_marginTop="11dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Low Stock Quantity Warning"
                                android:textColor="#424546"
                                android:textSize="16sp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginTop="10dp"
                                android:background="#40000000" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="22dp"
                                android:layout_marginStart="18dp"
                                android:layout_marginTop="22dp"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Quantity"
                                android:textColor="@color/black"
                                android:textSize="12sp" />
                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                app:cardCornerRadius="5dp"
                                android:layout_height="40dp"
                                android:layout_marginStart="15dp"
                                android:layout_marginTop="7dp"
                                android:layout_marginEnd="11dp"
                                android:layout_marginBottom="27dp"
                                app:cardBackgroundColor="@color/white"
                                app:strokeColor="#7D7C7C"
                                app:strokeWidth="1dp">

                                <EditText
                                    android:id="@+id/edit_quantity"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:background="@android:color/transparent"
                                    android:hint="1"
                                    android:singleLine="true"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="12sp" />
                            </com.google.android.material.card.MaterialCardView>

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="19dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="1dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="5dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="21dp"
                                android:layout_marginTop="11dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Stock Visibility State"
                                android:textColor="#424546"
                                android:textSize="16sp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginTop="10dp"
                                android:background="#40000000" />


                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="17dp"
                                android:layout_marginTop="19dp"
                                android:layout_marginEnd="17dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="22dp"

                                    android:fontFamily="@font/roboto_regular"
                                    android:text="Show Stock Quantity"
                                    android:textColor="@color/black"
                                    android:textSize="12sp" />

                                <Switch
                                    android:id="@+id/switch_show_with_quantity"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerInParent="true"
                                    android:checked="true"
                                    android:thumbTint="@android:color/white"
                                    android:track="@drawable/custom_switch_thumb" />
                            </RelativeLayout>
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="17dp"
                                android:layout_marginTop="19dp"
                                android:layout_marginEnd="17dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="22dp"

                                    android:fontFamily="@font/roboto_regular"
                                    android:text="Show Stock With Text Only"
                                    android:textColor="@color/black"
                                    android:textSize="12sp" />

                                <Switch
                                    android:id="@+id/switch_show_with_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerInParent="true"
                                    android:checked="true"
                                    android:thumbTint="@android:color/white"
                                    android:track="@drawable/custom_switch_thumb" />
                            </RelativeLayout>
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="17dp"
                                android:layout_marginTop="19dp"
                                android:layout_marginEnd="17dp"
                                android:layout_marginBottom="23dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="22dp"
                                    android:fontFamily="@font/roboto_regular"
                                    android:text="Hide Stock"
                                    android:textColor="@color/black"
                                    android:textSize="12sp" />

                                <Switch
                                    android:id="@+id/switch_hide_stock"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerInParent="true"
                                    android:checked="true"
                                    android:thumbTint="@android:color/white"
                                    android:track="@drawable/custom_switch_thumb" />
                            </RelativeLayout>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="1dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="5dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="21dp"
                                android:layout_marginTop="11dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Cash On Delivery"
                                android:textColor="#424546"
                                android:textSize="16sp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginTop="10dp"
                                android:background="#40000000" />


                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="17dp"
                                android:layout_marginTop="19dp"
                                android:layout_marginEnd="17dp"
                                android:layout_marginBottom="24dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="22dp"

                                    android:fontFamily="@font/roboto_regular"
                                    android:text="Status"
                                    android:textColor="@color/black"
                                    android:textSize="12sp" />

                                <Switch
                                    android:id="@+id/switch_cod"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerInParent="true"
                                    android:checked="true"
                                    android:thumbTint="@android:color/white"
                                    android:track="@drawable/custom_switch_thumb" />
                            </RelativeLayout>

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="14dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="1dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="5dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="21dp"
                                android:layout_marginTop="11dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Featured"
                                android:textColor="#424546"
                                android:textSize="16sp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginTop="10dp"
                                android:background="#40000000" />


                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="17dp"
                                android:layout_marginTop="19dp"
                                android:layout_marginEnd="17dp"
                                android:layout_marginBottom="24dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="22dp"

                                    android:fontFamily="@font/roboto_regular"
                                    android:text="Status"
                                    android:textColor="@color/black"
                                    android:textSize="12sp" />

                                <Switch
                                    android:id="@+id/switch_featured"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerInParent="true"
                                    android:checked="true"
                                    android:thumbTint="@android:color/white"
                                    android:track="@drawable/custom_switch_thumb" />
                            </RelativeLayout>

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="14dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="1dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="5dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="21dp"
                                android:layout_marginTop="11dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Todays Deal"
                                android:textColor="#424546"
                                android:textSize="16sp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginTop="10dp"
                                android:background="#40000000" />


                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="17dp"
                                android:layout_marginTop="19dp"
                                android:layout_marginEnd="17dp"
                                android:layout_marginBottom="24dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="22dp"

                                    android:fontFamily="@font/roboto_regular"
                                    android:text="Status"
                                    android:textColor="@color/black"
                                    android:textSize="12sp" />

                                <Switch
                                    android:id="@+id/switch_today_deal"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerInParent="true"
                                    android:checked="true"
                                    android:thumbTint="@android:color/white"
                                    android:track="@drawable/custom_switch_thumb" />
                            </RelativeLayout>

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="29dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="150dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="8dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="21dp"
                                android:layout_marginTop="11dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Flash Deal"
                                android:textColor="#424546"
                                android:textSize="16sp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginTop="10dp"
                                android:background="#40000000" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="22dp"
                                android:layout_marginStart="18dp"
                                android:layout_marginTop="17dp"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Add To Flash"
                                android:textColor="@color/black"
                                android:textSize="12sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_marginStart="15dp"
                                android:layout_marginTop="7dp"
                                android:layout_marginEnd="11dp"
                                android:layout_marginBottom="1dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="5dp"
                                app:strokeColor="#7D7C7C"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_marginStart="10dp"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:gravity="center_vertical"
                                        android:text="Choose Flash Title"
                                        android:textColor="#B4ABAB"
                                        android:textSize="12sp" />

                                    <ImageView
                                        android:layout_width="24dp"
                                        android:layout_height="24dp"
                                        android:layout_marginEnd="6dp"
                                        android:src="@drawable/arrow_down"
                                        app:tint="#222222" />
                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="22dp"
                                android:layout_marginStart="18dp"
                                android:layout_marginTop="17dp"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Discount"
                                android:textColor="@color/black"
                                android:textSize="12sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_marginStart="15dp"
                                android:layout_marginTop="7dp"
                                android:layout_marginEnd="11dp"
                                android:layout_marginBottom="1dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="5dp"
                                app:strokeColor="#7D7C7C"
                                app:strokeWidth="1dp">

                                <EditText
                                    android:id="@+id/edit_discount"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:background="@android:color/transparent"
                                    android:hint="Discount"
                                    android:singleLine="true"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="12sp" />
                            </com.google.android.material.card.MaterialCardView>
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="22dp"
                                android:layout_marginStart="18dp"
                                android:layout_marginTop="17dp"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Discount Type"
                                android:textColor="@color/black"
                                android:textSize="12sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:id="@+id/card_discount_type"
                                android:layout_height="40dp"
                                android:layout_marginStart="15dp"
                                android:layout_marginTop="7dp"
                                android:layout_marginEnd="11dp"
                                android:layout_marginBottom="28dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="5dp"
                                app:strokeColor="#7D7C7C"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/txt_discount_type"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_marginStart="10dp"
                                        android:layout_weight="1"
                                        android:fontFamily="@font/roboto_regular"
                                        android:gravity="center_vertical"
                                        android:hint="Choose Discount Type"
                                        android:textColorHint="#B4ABAB"
                                        android:textSize="12sp" />

                                    <ImageView
                                        android:layout_width="24dp"
                                        android:layout_height="24dp"
                                        android:layout_marginEnd="6dp"
                                        android:src="@drawable/arrow_down"
                                        app:tint="#222222" />
                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="83dp"
                android:layout_alignParentBottom="true"
                android:background="@color/white"
                android:elevation="10dp"
                android:visibility="visible">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_save_un_publish"
                    android:layout_weight="1"
                    android:layout_marginEnd="4.5dp"
                    android:layout_width="match_parent"
                    android:layout_height="54dp"
                    android:layout_alignParentStart="true"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="11dp"
                    android:elevation="1dp"
                    app:cardBackgroundColor="#F1892C"
                    app:cardCornerRadius="51dp"
                    app:cardElevation="1dp"
                   >

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:text="Save &amp; Unpublish"
                        android:textColor="@color/white"
                        android:textSize="18sp" />
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_save_publish"
                    android:layout_width="match_parent"
                    android:layout_weight="1"
                    android:layout_marginStart="4.5dp"
                    android:layout_height="54dp"
                    android:layout_marginEnd="17dp"
                    android:elevation="1dp"
                    app:cardBackgroundColor="#A61C5C"
                    app:cardCornerRadius="51dp"
                    app:cardElevation="1dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:text="Save &amp; Publish"
                        android:textColor="@color/white"
                        android:textSize="18sp" />
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>
        </RelativeLayout>

    </LinearLayout>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>