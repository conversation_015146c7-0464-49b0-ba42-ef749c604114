<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="212dp"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    app:strokeWidth="0dp"
    app:cardElevation="2dp"
    android:layout_marginBottom="10dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="5dp"
            android:layout_marginTop="5dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_cust"
                android:layout_width="62dp"
                android:layout_height="62dp"
                android:src="@drawable/icon_doctor" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txt_cust_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Charollette Baker"
                    android:textColor="@color/black"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/txt_med_condition"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Sinus Infection"
                    android:textColor="#B9B9B9"
                    android:textSize="14sp" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|top"
            android:layout_marginStart="5dp"
            android:layout_marginTop="20dp"
            android:layout_weight="1"
            android:gravity="center_vertical|top"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/med_bg_icon" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Medications Prescribed"
                    android:textColor="#99A61C5C"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/txt_med_pres"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_bold"
                    android:text="3"
                    android:textColor="@color/doctor_main_color"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="38dp"
            android:id="@+id/card_view"
            app:cardBackgroundColor="@color/doctor_main_color"
            app:cardCornerRadius="12dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="View Details"
                android:textColor="@color/white"
                android:layout_gravity="center"
                android:gravity="center"
                android:textSize="16sp"
                android:fontFamily="@font/roboto_regular"/>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>