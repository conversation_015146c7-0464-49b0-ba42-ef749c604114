<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_total_sales"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="NOS"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Sales"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_total_patient"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="NOS"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Patients/Client"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21637"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="11dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_total_sales_amount"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total "
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="NOS"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Sales Amount"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="17341"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_total_vendors"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total "
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="NOS"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Vendors"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="27"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="11dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_stock_count"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Stock Count"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="31785Nos"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total Products"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="31785Nos"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>


                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_influencer_outstanding"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Influencer"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="NOS"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Outstanding"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="-1"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_total_orders"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="NOS"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Orders"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_order_amount"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Order"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="KD"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Amount"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21637"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_total_influencer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="NOS"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Influencers"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_paid_to_influencer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Paid"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="KD"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="to Influencer"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21637"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_seller_outstanding"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Seller"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="KD"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Outstanding"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_paid_to_seller"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Paid"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="KD"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="to Seller"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21637"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_gross_margin"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Gross"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="KD"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Margin"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_net_margin"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Net"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="KD"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Margin"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21637"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_pending_delivery"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Pending"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Nos"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="delivery"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_sales_return_request"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Sales"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="KD"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="return requests"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21637"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_approved_request"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Approved"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Nos"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Requests"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_total_delivery_cost"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="KD"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Delivery cost"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21637"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_delivery_count"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Nos"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Delivery count"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_total_cancelled_orders"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Nos"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Cancelled Orders"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21637"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginEnd="4.5dp"
            android:layout_weight="1"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_closed_order"
                android:layout_width="match_parent"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Nos"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Closed Orders"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="132dp"
            android:layout_marginStart="4.5dp"
            android:layout_weight="1"
            android:visibility="invisible"
            app:cardCornerRadius="8dp">

            <RelativeLayout
                android:id="@+id/lyt_total_cancelled_order"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    android:src="@drawable/mask_home" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="9dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Total"
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/roboto_medium"
                            android:text="Nos"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Cancelled Orders"
                        android:textColor="@color/white"
                        android:textSize="11sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:fontFamily="@font/roboto_bold"
                        android:lineSpacingExtra="8sp"
                        android:text="21637"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                </LinearLayout>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>
</LinearLayout>