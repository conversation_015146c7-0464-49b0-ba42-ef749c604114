<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="200dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="@color/white">

    <!-- Top: Search & Add Row -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <!-- Search Input -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="0dp"
            android:layout_height="43dp"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            app:cardBackgroundColor="#F8F8F8"
            app:cardCornerRadius="16dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_marginStart="10dp"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/search_icon_new" />

                <EditText
                    android:id="@+id/edit_search"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="5dp"
                    android:background="@android:color/transparent"
                    android:hint="Search"
                    android:fontFamily="@font/roboto_regular"
                    android:textColorHint="#AEAEAE"
                    android:textSize="16dp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Add Button -->
    </LinearLayout>

    <!-- Scrollable SKU list -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvSkuList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="10dp"
        tools:itemCount="5"
        tools:listitem="@layout/item_apix_sku" />
    <RelativeLayout
        android:id="@+id/progressBar_small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="#50F9FEFF"
        android:layout_gravity="center"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</LinearLayout>
