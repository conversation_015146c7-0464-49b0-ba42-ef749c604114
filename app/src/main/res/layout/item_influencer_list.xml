<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginBottom="8dp"
    app:cardBackgroundColor="#F8F5F5"
    app:cardCornerRadius="5dp">

    <LinearLayout
        android:layout_marginBottom="5dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txt_dest"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:fontFamily="@font/roboto_medium"
                android:textColor="@color/nav_selected"
                android:textSize="11sp" />

            <TextView
                android:id="@+id/txt_doctor_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:fontFamily="@font/roboto_bold"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/txt_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_regular"
                android:singleLine="true"
                android:layout_marginTop="5dp"
                android:textColor="@color/black"
                android:textSize="13sp" />
            <TextView
                android:id="@+id/txt_clinic"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_medium"
                android:singleLine="true"
                android:layout_marginTop="5dp"
                android:textColor="@color/main_color"
                android:textSize="11sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_gravity="center|end"
            android:gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="15dp"
            android:orientation="vertical">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="60dp"
                android:id="@+id/card_status"
                android:layout_height="20dp"
                android:layout_margin="1dp"
                app:strokeWidth="0dp"
                app:cardBackgroundColor="#5DB245"
                app:cardCornerRadius="19dp">

                <TextView
                    android:id="@+id/txt_status"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:textColor="@color/white"
                    android:textSize="11sp"
                    android:text="Active"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"/>
            </com.google.android.material.card.MaterialCardView>
            <LinearLayout
                android:id="@+id/lyt_apix_margin"
                android:layout_marginTop="3dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="25dp"
                    app:cardCornerRadius="50dp"
                    android:layout_gravity="center"
                    app:cardBackgroundColor="#d4d5d8"
                    android:layout_height="25dp">
                    <ImageView
                        android:layout_gravity="center"
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:src="@drawable/apix_margin_icon"/>
                </com.google.android.material.card.MaterialCardView>


            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>