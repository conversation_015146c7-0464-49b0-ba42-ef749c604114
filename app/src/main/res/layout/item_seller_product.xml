<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginEnd="13dp"
    android:layout_marginBottom="14dp"
    android:layout_weight="1"
    android:elevation="1dp"
    app:strokeWidth="0dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="18dp"
    app:cardElevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="100dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="92dp"
                    android:layout_height="81dp"
                    android:layout_margin="10dp"
                    app:cardBackgroundColor="#F7F7F7"
                    app:strokeWidth="0dp">

                    <ImageView
                        android:id="@+id/img_product"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_stock"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginEnd="11dp"
                    android:visibility="gone"
                    app:cardBackgroundColor="#EC1448"
                    app:cardCornerRadius="19dp">

                    <TextView
                        android:id="@+id/txt_stock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="5dp"

                        android:layout_marginEnd="5dp"
                        android:fontFamily="@font/roboto_bold"
                        android:gravity="center"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </com.google.android.material.card.MaterialCardView>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginStart="6dp"
                    android:layout_marginBottom="21dp"
                    android:src="@drawable/view_icon"
                    android:visibility="gone" />
            </RelativeLayout>

            <LinearLayout
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_marginStart="5dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:src="@drawable/dummy_store" />

                    <TextView
                        android:id="@+id/txt_store_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Store Name"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                </LinearLayout>
                <TextView
                    android:id="@+id/txt_product_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="3dp"
                    android:layout_marginEnd="5dp"
                    android:fontFamily="@font/roboto_bold"
                    android:maxLines="2"
                    android:text="Nexium Tablet 40  Mg14'S"
                    android:textColor="@color/black"
                    android:textSize="15sp" />


            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_regular"
                android:text="Seller Discount"
                android:textColor="#B9B9B9"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/txt_seller_discount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_medium"
                android:text="0"
                android:textColor="@color/black"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_regular"
                android:text="Apix Margin"
                android:textColor="#B9B9B9"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/txt_apix_margin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_medium"
                android:text="0.000"
                android:textColor="@color/black"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_medium"
                android:text="Base Price"
                android:textColor="#B9B9B9"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/txt_base_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_medium"
                android:text="0.000"
                android:textColor="@color/black"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_medium"
                android:text="Influencer Margin"
                android:textColor="#B9B9B9"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/txt_influencer_margin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_medium"
                android:text="0.000"
                android:textColor="@color/black"
                android:textSize="12sp" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_medium"
                android:text="COG"
                android:textColor="#B9B9B9"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/txt_cog"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_medium"
                android:text="0.000"
                android:textColor="@color/black"
                android:textSize="12sp" />
        </LinearLayout>
        <LinearLayout
            android:layout_marginBottom="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:layout_marginTop="0dp"
            android:layout_marginEnd="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_medium"
                android:text="Commission Value"
                android:textColor="#B9B9B9"
                android:textSize="12sp" />
            <com.google.android.material.card.MaterialCardView
                android:layout_width="wrap_content"
                android:minWidth="40dp"
                app:cardCornerRadius="5dp"
                app:cardBackgroundColor="@color/white"
                android:layout_height="30dp">
                <EditText
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="5dp"
                    android:id="@+id/edt_com_value"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/roboto_medium"
                    android:text="00"
                    android:layout_gravity="center"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    android:inputType="numberDecimal"
                    android:background="@null"
                    android:padding="0dp"
                    android:maxLines="1"
                    android:gravity="center" />
            </com.google.android.material.card.MaterialCardView>



        </LinearLayout>

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>