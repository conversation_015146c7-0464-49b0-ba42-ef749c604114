<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FAFAFE"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/img_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentStart="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                app:cardBackgroundColor="#F7F7F7"
                app:cardCornerRadius="10dp"
                app:strokeWidth="0dp">

                <ImageView
                    android:layout_width="6dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center"

                    android:src="@drawable/arrow_brown_new" />
            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:id="@+id/txt_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/roboto_bold"
                android:text="POS Shop"
                android:textColor="@color/black"
                android:textSize="16sp" />
            <RelativeLayout
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:layout_width="33dp"
                android:layout_height="27dp"
                android:layout_marginEnd="12dp">

                <ImageView
                    android:id="@+id/img_cart"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/header_cart" />

                <RelativeLayout
                    android:visibility="gone"
                    android:id="@+id/rlt_cart_count"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/cart_count_bg" />

                    <TextView
                        android:id="@+id/txt_cart_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center"
                        android:maxLength="2"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </RelativeLayout>
            </RelativeLayout>
        </RelativeLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nested_scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="30dp"
                    android:layout_marginEnd="12dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="25dp"
                    app:cardElevation="2dp"
                    app:strokeColor="@color/white">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:layout_marginStart="10dp"
                            android:src="@drawable/search_icon" />

                        <EditText
                            android:id="@+id/edit_search"
                            android:background="@android:color/transparent"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Search ... "
                            android:textColorHint="#ABB7C2"
                            android:textSize="15sp" />

                        <ImageView
                            android:id="@+id/card_filter"
                            android:layout_width="36dp"
                            android:layout_height="36dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/icon_filter" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_marginTop="24dp"
                    android:layout_marginEnd="12dp"
                    android:layout_marginBottom="30dp"
                    android:layout_marginStart="12dp"
                    android:layout_height="match_parent">
                    <include android:id="@+id/page_shimmer"
                        layout="@layout/shimmer_pos_shop_product"/>
                    <TextView
                        android:visibility="gone"
                        android:id="@+id/txt_no_data"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:text="No Data Found"
                        android:layout_marginTop="100dp"
                        android:gravity="center"
                        android:layout_centerInParent="true"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_product"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        tools:itemCount="1"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/item_pos_shop_product" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/progressBar_small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="#50F9FEFF"
                    android:layout_gravity="center"
                    android:visibility="gone">

                    <ProgressBar
                        style="?android:attr/progressBarStyle"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:indeterminateDrawable="@drawable/rotating_icon"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </RelativeLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
    <include android:id="@+id/page_filter"
        layout="@layout/fragment_filter_pos_shop"/>
</androidx.coordinatorlayout.widget.CoordinatorLayout>