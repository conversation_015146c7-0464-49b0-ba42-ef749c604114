<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    android:background="#a3a3a3"
    android:id="@+id/lyt_address"
    >
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_gravity="center"

        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        app:cardCornerRadius="10dp">
            <LinearLayout
                android:layout_marginBottom="10dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="26dp"
                    android:layout_marginStart="18dp"
                    android:layout_marginEnd="18dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_bold"
                        android:text="Add New Type"
                        android:textColor="#424546"
                        android:textSize="16sp" />

                    <ImageView
                        android:id="@+id/img_close_date"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginStart="27dp"
                        android:src="@drawable/close_filter" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="15dp"
                    android:background="#40008F96" />

                <TextView
                    android:layout_marginStart="25dp"
                    android:layout_marginEnd="25dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="11dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Type"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:layout_marginStart="25dp"
                    android:layout_marginEnd="25dp"
                    android:layout_marginTop="10dp"
                    app:cardCornerRadius="10dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_first_name"
                        android:background="@android:color/transparent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Type"
                        android:textColor="#424546"
                        android:textColorHint="#C49C9C9C"
                        android:textSize="12sp" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_marginStart="25dp"
                    android:layout_marginEnd="25dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Arabic Name"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:layout_marginStart="25dp"
                    android:layout_marginEnd="25dp"
                    android:layout_marginTop="10dp"
                    app:cardCornerRadius="10dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:background="@android:color/transparent"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Arabic Name"
                        android:textColor="#424546"
                        android:textColorHint="#C49C9C9C"
                        android:textSize="14dp" />
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="47dp"
                    app:cardCornerRadius="8dp"
                    android:id="@+id/card_add_address"
                    android:layout_marginStart="25dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="25dp"
                    android:layout_marginBottom="15dp"
                    app:cardBackgroundColor="@color/main_color">

                    <TextView
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Save"
                        android:textColor="@color/white"
                        android:textSize="18sp" />
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>

    </com.google.android.material.card.MaterialCardView>
</FrameLayout>