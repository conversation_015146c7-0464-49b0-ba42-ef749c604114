<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.login.LoginActivity">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:textSize="20sp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="40dp"
                android:fontFamily="@font/roboto_black"
                android:gravity="center"
                android:text="Sign In"
                android:textColor="#111111" />

            <ImageView
                android:layout_width="100dp"
                android:layout_height="60dp"
                android:layout_gravity="center"
                android:layout_marginTop="70dp"
                android:src="@drawable/wasfa_logo" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="25dp"
                android:layout_marginTop="31dp"
                android:layout_marginEnd="25dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Email Address"
                    android:textSize="13dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="52dp"
                    android:layout_margin="1dp"
                    app:cardBackgroundColor="#FEFEFE"
                    app:cardCornerRadius="24dp"
                    app:strokeColor="#E3E7EC"
                    app:strokeWidth="1dp">

                    <EditText
                        android:singleLine="true"
                        android:id="@+id/edit_email"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="16dp"
                        android:background="@android:color/transparent"
                        android:hint="Enter your email address"
                        android:textColorHint="#C1C1C1"
                        android:textSize="13dp" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Password"
                    android:textSize="13dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="52dp"
                    android:layout_margin="1dp"
                    app:cardCornerRadius="24dp"
                    app:cardBackgroundColor="#FEFEFE"
                    app:strokeColor="#E3E7EC"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            android:singleLine="true"
                            android:id="@+id/edit_pass"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:background="@android:color/transparent"
                            android:hint="Enter your password"
                            android:textColorHint="#C1C1C1"
                            android:textSize="13dp" />

                        <ImageView
                            android:id="@+id/img_hide_pass"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/hide_icon"
                            app:tint="#C49C9C9C" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">
                    <LinearLayout
                        android:layout_centerInParent="true"
                        android:layout_alignParentStart="true"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:id="@+id/lyt_remember"
                        android:layout_width="wrap_content"
                        android:layout_height="30dp"
                        android:orientation="horizontal">
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_reminder"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        app:cardBackgroundColor="#FEFEFE"
                        app:cardCornerRadius="50dp"
                        app:strokeColor="#E3E7EC"
                        app:strokeWidth="1dp">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_reminder_selected"
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:layout_gravity="center"
                            android:visibility="gone"
                            app:cardBackgroundColor="@color/main_color"
                            app:cardCornerRadius="50dp" />

                    </com.google.android.material.card.MaterialCardView>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"

                        android:fontFamily="@font/roboto_medium"
                        android:text="Remember Me"
                        android:textColor="#424546"
                        android:textSize="14dp" />
                    </LinearLayout>
                    <TextView
                        android:id="@+id/txt_forgot_pass"
                        android:gravity="center_vertical"
                        android:layout_centerInParent="true"
                        android:layout_alignParentEnd="true"
                        android:layout_width="wrap_content"
                        android:layout_height="30dp"
                        android:layout_marginStart="8dp"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Forgot Password"
                        android:textColor="#E53935"
                        android:textSize="14dp" />
                </RelativeLayout>

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginTop="70dp"
                    android:id="@+id/btn_sign_in"
                    app:cardBackgroundColor="@color/main_color"
                    app:cardCornerRadius="24dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Sign In"
                        android:textSize="18dp"
                        android:fontFamily="@font/roboto_regular"
                        android:layout_gravity="center"
                        android:textColor="#FEFEFE" />
                </androidx.cardview.widget.CardView>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>