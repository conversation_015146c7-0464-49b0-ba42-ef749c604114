<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:background="@color/white"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_back"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/pos_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="25dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_bold"
                android:text="Sell Return"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <RelativeLayout
                android:layout_width="33dp"
                android:layout_height="27dp"
                android:layout_marginEnd="12dp">

                <ImageView
                    android:id="@+id/img_cart"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/header_cart" />

                <RelativeLayout
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/cart_count_bg" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center"
                        android:maxLength="2"
                        android:text="2"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </RelativeLayout>
            </RelativeLayout>

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="20dp"
                android:src="@drawable/home_notif" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:layout_marginBottom="10dp"
            android:scrollbars="none">

            <LinearLayout
                android:layout_marginBottom="50dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="16dp"
                android:layout_marginTop="20dp"

                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_product"
                    android:layout_marginTop="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:itemCount="1"
                    tools:listitem="@layout/item_order_details" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="22dp"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Return Total:"
                    android:textColor="@color/black"
                    android:textSize="12sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="7dp"
                    android:layout_marginBottom="5dp"
                    android:layout_marginEnd="10dp"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        app:cardCornerRadius="5dp"
                        android:layout_height="40dp"
                        android:layout_marginStart="1dp"
                        android:layout_marginEnd="6dp"
                        android:layout_marginBottom="1dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/white"
                        android:elevation="1dp"
                        app:cardElevation="1dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="10dp"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_regular"
                                android:gravity="center_vertical"
                                android:textColor="#B4ABAB"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="58dp"
                        android:layout_height="41dp"
                        app:cardBackgroundColor="#1FABE2"
                        app:cardCornerRadius="9dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Save"
                                android:textColor="@color/white"
                                android:textSize="13sp" />
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Order #12345"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Sub Total"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_medium"
                        android:text="KD 0.000"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Cancelled Total"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_medium"
                        android:text="KD 0.000"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_bold"
                        android:text="Total"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_bold"
                        android:text="KD 0.000"
                        android:textColor="@color/black"
                        android:textSize="16sp" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="#40000000" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:text="Shipping Address"
                        android:textColor="@color/black"
                        android:textSize="15sp" />



                        <TextView
                            android:layout_marginTop="10dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Hawalli, Bayan, ttg"
                            android:textColor="@color/black"
                            android:textSize="14sp" />
                    <TextView
                        android:layout_marginTop="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:text="ggg, Lane, ttg"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">
    <TextView
        android:layout_marginTop="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_medium"
        android:text="Note : "
        android:textColor="@color/black"
        android:textSize="14sp" />
    <TextView
        android:layout_marginTop="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_regular"
        android:text="Lorem Ipsum"
        android:textColor="@color/black"
        android:textSize="14sp" />
</LinearLayout>
                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="#40000000" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:text="Order Date"
                        android:textColor="@color/black"
                        android:textSize="15sp" />



                    <TextView
                        android:layout_marginTop="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:text="27-11-2024 05:09 AM"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>