<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/shimmer"
    android:visibility="gone"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="260dp"
        android:layout_weight="1"
        android:layout_marginEnd="5dp"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="15dp"
        android:layout_marginStart="2dp"
        android:orientation="vertical"
        app:cardCornerRadius="11dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="5dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView

                    android:layout_width="match_parent"
                    android:layout_height="103dp"
                    android:scaleType="centerCrop"
                    android:background="@color/shimmer_color" />

            </RelativeLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="15dp"
                android:layout_marginTop="10dp"
                android:background="@color/shimmer_color"
               />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="11dp"
                android:background="@color/shimmer_color" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginTop="11dp"
                android:background="@color/shimmer_color" />
            <com.google.android.material.card.MaterialCardView
                android:layout_width="123dp"
                android:layout_height="36dp"
                android:layout_gravity="center"
                android:layout_marginStart="1dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/shimmer_color"
                app:cardCornerRadius="18dp"/>
        </LinearLayout>


    </com.google.android.material.card.MaterialCardView>
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="260dp"
        android:layout_weight="1"
        android:layout_marginEnd="5dp"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="15dp"
        android:layout_marginStart="2dp"
        android:orientation="vertical"
        app:cardCornerRadius="11dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="5dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView

                    android:layout_width="match_parent"
                    android:layout_height="103dp"
                    android:scaleType="centerCrop"
                    android:background="@color/shimmer_color" />

            </RelativeLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="15dp"
                android:layout_marginTop="10dp"
                android:background="@color/shimmer_color"
                />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="11dp"
                android:background="@color/shimmer_color" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginTop="11dp"
                android:background="@color/shimmer_color" />
            <com.google.android.material.card.MaterialCardView
                android:layout_width="123dp"
                android:layout_height="36dp"
                android:layout_gravity="center"
                android:layout_marginStart="1dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/shimmer_color"
                app:cardCornerRadius="18dp"/>
        </LinearLayout>


    </com.google.android.material.card.MaterialCardView>

</LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="260dp"
                android:layout_weight="1"
                android:layout_marginEnd="5dp"
                android:layout_marginTop="2dp"
                android:layout_marginBottom="15dp"
                android:layout_marginStart="2dp"
                android:orientation="vertical"
                app:cardCornerRadius="11dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="5dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView

                            android:layout_width="match_parent"
                            android:layout_height="103dp"
                            android:scaleType="centerCrop"
                            android:background="@color/shimmer_color" />

                    </RelativeLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="15dp"
                        android:layout_marginTop="10dp"
                        android:background="@color/shimmer_color"
                        />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="11dp"
                        android:background="@color/shimmer_color" />
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:layout_marginTop="11dp"
                        android:background="@color/shimmer_color" />
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="123dp"
                        android:layout_height="36dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="1dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="1dp"
                        android:layout_marginBottom="16dp"
                        app:cardBackgroundColor="@color/shimmer_color"
                        app:cardCornerRadius="18dp"/>
                </LinearLayout>


            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="260dp"
                android:layout_weight="1"
                android:layout_marginEnd="5dp"
                android:layout_marginTop="2dp"
                android:layout_marginBottom="15dp"
                android:layout_marginStart="2dp"
                android:orientation="vertical"
                app:cardCornerRadius="11dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="5dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView

                            android:layout_width="match_parent"
                            android:layout_height="103dp"
                            android:scaleType="centerCrop"
                            android:background="@color/shimmer_color" />

                    </RelativeLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="15dp"
                        android:layout_marginTop="10dp"
                        android:background="@color/shimmer_color"
                        />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="11dp"
                        android:background="@color/shimmer_color" />
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:layout_marginTop="11dp"
                        android:background="@color/shimmer_color" />
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="123dp"
                        android:layout_height="36dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="1dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="1dp"
                        android:layout_marginBottom="16dp"
                        app:cardBackgroundColor="@color/shimmer_color"
                        app:cardCornerRadius="18dp"/>
                </LinearLayout>


            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="260dp"
                android:layout_weight="1"
                android:layout_marginEnd="5dp"
                android:layout_marginTop="2dp"
                android:layout_marginBottom="15dp"
                android:layout_marginStart="2dp"
                android:orientation="vertical"
                app:cardCornerRadius="11dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="5dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView

                            android:layout_width="match_parent"
                            android:layout_height="103dp"
                            android:scaleType="centerCrop"
                            android:background="@color/shimmer_color" />

                    </RelativeLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="15dp"
                        android:layout_marginTop="10dp"
                        android:background="@color/shimmer_color"
                        />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="11dp"
                        android:background="@color/shimmer_color" />
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:layout_marginTop="11dp"
                        android:background="@color/shimmer_color" />
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="123dp"
                        android:layout_height="36dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="1dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="1dp"
                        android:layout_marginBottom="16dp"
                        app:cardBackgroundColor="@color/shimmer_color"
                        app:cardCornerRadius="18dp"/>
                </LinearLayout>


            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="260dp"
                android:layout_weight="1"
                android:layout_marginEnd="5dp"
                android:layout_marginTop="2dp"
                android:layout_marginBottom="15dp"
                android:layout_marginStart="2dp"
                android:orientation="vertical"
                app:cardCornerRadius="11dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="5dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView

                            android:layout_width="match_parent"
                            android:layout_height="103dp"
                            android:scaleType="centerCrop"
                            android:background="@color/shimmer_color" />

                    </RelativeLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="15dp"
                        android:layout_marginTop="10dp"
                        android:background="@color/shimmer_color"
                        />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="11dp"
                        android:background="@color/shimmer_color" />
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:layout_marginTop="11dp"
                        android:background="@color/shimmer_color" />
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="123dp"
                        android:layout_height="36dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="1dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="1dp"
                        android:layout_marginBottom="16dp"
                        app:cardBackgroundColor="@color/shimmer_color"
                        app:cardCornerRadius="18dp"/>
                </LinearLayout>


            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </LinearLayout>
</com.facebook.shimmer.ShimmerFrameLayout>
