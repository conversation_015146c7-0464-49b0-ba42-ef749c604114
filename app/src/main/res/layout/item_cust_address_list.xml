<com.chauthai.swipereveallayout.SwipeRevealLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="horizontal"
    android:layout_marginBottom="10dp"
    app:dragEdge="right"
    app:mode="normal"
    xmlns:android="http://schemas.android.com/apk/res/android"
    >
    <FrameLayout
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="100dp">
        <LinearLayout
            android:id="@+id/layout_swipe_actions"
            android:layout_width="100dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="visible">
            <com.google.android.material.card.MaterialCardView
                android:layout_width="35dp"
                android:id="@+id/btn_delete"
                app:cardCornerRadius="35dp"
                app:cardBackgroundColor="#FF0000"
                android:layout_height="35dp">
                <ImageView
                    android:layout_gravity="center"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:tint="@color/white"
                    android:src="@drawable/icon_delete_cart"/>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="35dp"
                android:id="@+id/btn_edit"
                android:layout_marginTop="10dp"
                app:cardCornerRadius="35dp"
                app:cardBackgroundColor="#5DB245"
                android:layout_height="35dp">
                <ImageView
                    android:layout_gravity="center"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:tint="@color/white"
                    android:src="@drawable/edit_icon_single"/>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </FrameLayout>
    <FrameLayout
        android:visibility="visible"
        android:minHeight="100dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:orientation="vertical"
            app:cardBackgroundColor="#F8F5F5"
            app:cardCornerRadius="10dp"
            android:elevation="5dp"
            >
            <LinearLayout
                android:padding="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:text="Hashim Mohammed"
                            android:id="@+id/txt_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_bold"
                            android:lineHeight="20dp"
                            android:textColor="@color/black"
                            android:textSize="14dp" />


                    </LinearLayout>

                    <TextView
                        android:id="@+id/txt_number"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="+965 75598838"
                        android:fontFamily="@font/roboto_medium"
                        android:lineHeight="20dp"
                        android:textColor="#151515"
                        android:textSize="13dp" />
                    <TextView
                        android:id="@+id/txt_email"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="<EMAIL>"
                        android:fontFamily="@font/roboto_medium"
                        android:lineHeight="20dp"
                        android:textColor="#151515"
                        android:textSize="12sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="19dp"
                            android:layout_height="19dp"
                            android:src="@drawable/blue_loc"
                            app:tint="#0D4B30" />

                        <TextView
                            android:id="@+id/txt_address"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:fontFamily="@font/roboto_regular"
                            android:lineHeight="17dp"
                            android:text="Ahmadi , Abu Halifa, sfgf, sfgfg, ,fgetg"
                            android:maxWidth="243dp"
                            android:textColor="@color/black"
                            android:textSize="11dp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </FrameLayout>
</com.chauthai.swipereveallayout.SwipeRevealLayout>
