<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/img_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentStart="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                app:cardBackgroundColor="#F7F7F7"
                app:cardCornerRadius="10dp"
                app:strokeWidth="0dp">

                <ImageView
                    android:layout_width="6dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center"

                    android:src="@drawable/arrow_brown_new" />
            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:id="@+id/txt_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/roboto_bold"
                android:text="Order Details"
                android:textColor="@color/black"
                android:textSize="14sp" />

        </RelativeLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:layout_marginBottom="10dp"
            android:scrollbars="none">

            <LinearLayout
                android:layout_marginBottom="50dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="16dp"
                android:layout_marginTop="20dp"

                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Delivery Status"
                            android:textColor="@color/black"
                            android:textSize="15sp" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp">

                            <TextView
                                android:id="@+id/txt_delivery_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentStart="true"
                                android:layout_centerInParent="true"
                                android:fontFamily="@font/roboto_medium"
                                android:textColor="@color/main_color"
                                android:textSize="14sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="100dp"
                                android:layout_height="37dp"
                                android:id="@+id/card_change_delivery_status"
                                android:layout_alignParentEnd="true"
                                android:layout_centerInParent="true"
                                android:layout_margin="1dp"
                                app:cardBackgroundColor="#F1892C"
                                app:cardCornerRadius="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/roboto_medium"
                                    android:gravity="center"
                                    android:text="Change"
                                    android:textColor="@color/white"
                                    android:textSize="15sp" />
                            </com.google.android.material.card.MaterialCardView>
                        </RelativeLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="#40000000" />
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Assign Influencer"
                            android:textColor="@color/black"
                            android:textSize="15sp" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp">

                            <TextView
                                android:id="@+id/txt_assign_influencer"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentStart="true"
                                android:layout_centerInParent="true"
                                android:fontFamily="@font/roboto_medium"
                                android:textColor="@color/main_color"
                                android:textSize="14sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="100dp"
                                android:id="@+id/card_assign_influencer"
                                android:layout_height="37dp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerInParent="true"
                                android:layout_margin="1dp"
                                app:cardBackgroundColor="#F1892C"
                                app:cardCornerRadius="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/roboto_medium"
                                    android:gravity="center"
                                    android:text="Change"
                                    android:textColor="@color/white"
                                    android:textSize="15sp" />
                            </com.google.android.material.card.MaterialCardView>
                        </RelativeLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="#40000000" />
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Assign Delivery Staff"
                            android:textColor="@color/black"
                            android:textSize="15sp" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp">

                            <TextView
                                android:id="@+id/txt_delivery_staff"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentStart="true"
                                android:layout_centerInParent="true"
                                android:fontFamily="@font/roboto_medium"
                                android:textColor="@color/main_color"
                                android:textSize="14sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="100dp"
                                android:layout_height="37dp"
                                android:id="@+id/card_assign_delivery_staff"
                                android:layout_alignParentEnd="true"
                                android:layout_centerInParent="true"
                                android:layout_margin="1dp"
                                app:cardBackgroundColor="#F1892C"
                                app:cardCornerRadius="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/roboto_medium"
                                    android:gravity="center"
                                    android:text="Change"
                                    android:textColor="@color/white"
                                    android:textSize="15sp" />
                            </com.google.android.material.card.MaterialCardView>
                        </RelativeLayout>
                    </LinearLayout>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="#40000000" />
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Payment Status"
                            android:textColor="@color/black"
                            android:textSize="15sp" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp">

                            <TextView
                                android:id="@+id/txt_payment_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentStart="true"
                                android:layout_centerInParent="true"
                                android:fontFamily="@font/roboto_medium"
                                android:textColor="@color/main_color"
                                android:textSize="14sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="100dp"
                                android:id="@+id/card_change_payment_status"
                                android:layout_height="37dp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerInParent="true"
                                android:layout_margin="1dp"
                                app:cardBackgroundColor="#F1892C"
                                app:cardCornerRadius="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/roboto_medium"
                                    android:gravity="center"
                                    android:text="Change"
                                    android:textColor="@color/white"
                                    android:textSize="15sp" />
                            </com.google.android.material.card.MaterialCardView>
                        </RelativeLayout>
                    </LinearLayout>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="#40000000" />
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Select Lead"
                            android:textColor="@color/black"
                            android:textSize="15sp" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp">

                            <TextView
                                android:id="@+id/txt_lead"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentStart="true"
                                android:layout_centerInParent="true"
                                android:fontFamily="@font/roboto_medium"
                                android:textColor="@color/main_color"
                                android:textSize="14sp" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="100dp"
                                android:id="@+id/card_change_lead"
                                android:layout_height="37dp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerInParent="true"
                                android:layout_margin="1dp"
                                app:cardBackgroundColor="#F1892C"
                                app:cardCornerRadius="10dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/roboto_medium"
                                    android:gravity="center"
                                    android:text="Change"
                                    android:textColor="@color/white"
                                    android:textSize="15sp" />
                            </com.google.android.material.card.MaterialCardView>
                        </RelativeLayout>
                    </LinearLayout>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="#40000000" />
                </LinearLayout>
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_product"
                    android:layout_marginTop="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:itemCount="1"
                    tools:listitem="@layout/item_order_details" />

                <TextView
                    android:id="@+id/txt_order_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Order #12345"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Sub Total"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/txt_sub_total"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_medium"
                        android:text="KD 0.000"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_regular"
                        android:text="Cancelled Total"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/txt_cancelled_total"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_medium"
                        android:text="KD 0.000"
                        android:textColor="@color/black"
                        android:textSize="14sp" />
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_bold"
                        android:text="Total"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/txt_total"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerInParent="true"
                        android:fontFamily="@font/roboto_bold"
                        android:text="KD 0.000"
                        android:textColor="@color/black"
                        android:textSize="16sp" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="#40000000" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:text="Shipping Address"
                        android:textColor="@color/black"
                        android:textSize="15sp" />



                        <TextView
                            android:id="@+id/txt_address"
                            android:layout_marginTop="10dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Hawalli, Bayan, ttg"
                            android:textColor="@color/black"
                            android:textSize="14sp" />
                    <TextView
                        android:id="@+id/txt_address_two"
                        android:layout_marginTop="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:text="ggg, Lane, ttg"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

<LinearLayout
    android:visibility="gone"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">
    <TextView
        android:layout_marginTop="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_medium"
        android:text="Note : "
        android:textColor="@color/black"
        android:textSize="14sp" />
    <TextView
        android:id="@+id/txt_notes"
        android:layout_marginTop="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_regular"
        android:text="Lorem Ipsum"
        android:textColor="@color/black"
        android:textSize="14sp" />
</LinearLayout>
                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="#40000000" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:text="Order Date"
                        android:textColor="@color/black"
                        android:textSize="15sp" />



                    <TextView
                        android:id="@+id/txt_order_date"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="50dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:text="27-11-2024 05:09 AM"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>