<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:background="@color/white"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_back"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/pos_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="25dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_bold"
                android:text="POS Shop Cart"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <RelativeLayout
                android:layout_width="33dp"
                android:layout_height="27dp"
                android:layout_marginEnd="12dp">

                <ImageView
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/header_cart" />

                <RelativeLayout
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/cart_count_bg" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center"
                        android:maxLength="2"
                        android:text="2"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </RelativeLayout>
            </RelativeLayout>

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="20dp"
                android:src="@drawable/home_notif" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fitsSystemWindows="true"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="76dp"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginEnd="12dp"
                    android:layout_marginBottom="1dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#A61C5C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="12dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="37dp"
                            android:layout_height="37dp"
                            android:layout_margin="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="4dp"
                            app:cardElevation="1dp">

                            <ImageView
                                android:layout_width="37dp"
                                android:layout_height="37dp"
                                android:src="@drawable/loc_pin_brown" />
                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="8dp"
                            android:layout_marginEnd="11dp"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Shipping address"
                                android:textColor="#A61C5C"
                                android:textSize="14sp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="3dp"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Sorry, There is no shipping
address "
                                android:textColor="#5DB245"
                                android:textSize="11sp" />
                        </LinearLayout>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_add_address"
                            android:layout_width="93dp"
                            android:layout_height="30dp"
                            android:layout_margin="1dp"
                            app:cardBackgroundColor="#A61C5C"
                            app:cardCornerRadius="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:fontFamily="@font/roboto_medium"
                                android:gravity="center"
                                android:text="Add Address"
                                android:textColor="@color/white"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_rx_cart"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="27dp"
                    android:layout_marginEnd="14dp"
                    android:layout_marginBottom="320dp"
                    tools:itemCount="5"
                    tools:listitem="@layout/rx_cart_item" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="320dp"
        android:background="@drawable/shadow_img"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="40dp"
            android:fontFamily="@font/roboto_bold"
            android:text="Order info"
            android:textColor="#424546"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="21dp"
            android:layout_marginEnd="20dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_regular"
                android:text="Subtotal"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/txt_sub_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_regular"
                android:text="KWD  0.000"
                android:textColor="#5DB245"
                android:textSize="15sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="7dp"
            android:layout_marginEnd="20dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_regular"
                android:text="Discount"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/txt_discount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_regular"
                android:text="KWD  0.000"
                android:textColor="#5DB245"
                android:textSize="15sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="7dp"
            android:layout_marginEnd="20dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_regular"
                android:text="Delivery"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <TextView

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_regular"
                android:text="Free"
                android:textColor="#5DB245"
                android:textSize="15sp" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="15dp"
            android:background="#260D4B30" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="20dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_bold"
                android:text="Total"
                android:textColor="#424546"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/txt_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_bold"
                android:text="KWD  0.000"
                android:textColor="#5DB245"
                android:textSize="16sp" />

        </LinearLayout>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_proceed"
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:layout_gravity="center"
            android:layout_marginStart="22dp"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="22dp"
            android:layout_marginBottom="22dp"
            app:cardBackgroundColor="@color/main_color"
            app:cardCornerRadius="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/roboto_medium"
                android:text="Proceed to payment"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>
    <include layout="@layout/sheet_add_address"
        android:id="@+id/page_address"/>
</androidx.constraintlayout.widget.ConstraintLayout>