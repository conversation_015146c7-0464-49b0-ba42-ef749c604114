<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/img_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentStart="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                app:cardBackgroundColor="#F7F7F7"
                app:cardCornerRadius="10dp"
                app:strokeWidth="0dp">

                <ImageView
                    android:layout_width="6dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center"

                    android:src="@drawable/arrow_brown_new" />
            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:id="@+id/txt_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/roboto_bold"
                android:text="POS RX Cart"
                android:textColor="@color/black"
                android:textSize="14sp" />

        </RelativeLayout>
        <TextView
            android:visibility="gone"
            android:id="@+id/txt_no_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:fontFamily="@font/roboto_medium"
            android:text="No Data"
            android:layout_gravity="center"

            android:textColor="@color/black"
            android:textSize="14sp" />
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_rx_cart"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="27dp"
            android:layout_marginStart="14dp"
            tools:itemCount="1"
            tools:listitem="@layout/rx_cart_item"
            android:layout_marginEnd="14dp"
            android:layout_marginBottom="100dp"/>
    </LinearLayout>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_proceed"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:layout_gravity="center"
        android:layout_marginStart="22dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="22dp"
        android:layout_marginBottom="22dp"
        app:cardBackgroundColor="@color/main_color"
        app:cardCornerRadius="8dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:fontFamily="@font/roboto_medium"
            android:text="Rx Preview"
            android:textColor="@color/white"
            android:textSize="14sp" />
    </com.google.android.material.card.MaterialCardView>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>