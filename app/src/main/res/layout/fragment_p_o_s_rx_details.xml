<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:background="@color/white"
            android:elevation="5dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_back"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/pos_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="25dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_bold"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <RelativeLayout
                android:layout_width="33dp"
                android:layout_height="27dp"
                android:layout_marginEnd="12dp">

                <ImageView
                    android:id="@+id/img_cart"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/header_cart" />

                <RelativeLayout
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/cart_count_bg" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center"
                        android:maxLength="2"
                        android:text="2"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </RelativeLayout>
            </RelativeLayout>

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="20dp"
                android:src="@drawable/home_notif" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:orientation="vertical">
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:src="@drawable/dummy_image"
                        android:id="@+id/img_product"
                        android:layout_width="match_parent"
                        android:layout_height="322dp"
                        android:layout_marginTop="10dp"
                        />
                    <LinearLayout
                        android:id="@+id/lyt_photos"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <androidx.viewpager2.widget.ViewPager2
                            android:id="@+id/viewPager"
                            android:layout_width="match_parent"
                            android:layout_height="322dp"
                            android:layout_marginTop="10dp" />
                        <LinearLayout
                            android:id="@+id/dotContainer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:visibility="visible"
                            app:layout_constraintTop_toBottomOf="@id/viewPager"></LinearLayout>
                    </LinearLayout>

                </RelativeLayout>
                <TextView
                    android:id="@+id/txt_pd_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_bold"
                    android:maxLines="3"
                    android:text="Nexium Tablet 40  Mg14'S"
                    android:textColor="@color/black"
                    android:textSize="16dp" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_prize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:text="KD 0.000"
                        android:textColor="@color/main_color"
                        android:textSize="18dp" />

                    <TextView
                        android:id="@+id/txt_strike_prize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="KD 0.000"
                        android:textColor="#A7A7A7"
                        android:textSize="14dp" />

                </LinearLayout>
                <LinearLayout
                    android:layout_marginTop="15dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="20dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="70dp"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:text="Brand"
                        android:textColor="#1B9ED9"
                        android:textSize="14dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="15dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text=":"
                        android:textColor="#1B9ED9"
                        android:textSize="14dp" />

                    <TextView
                        android:id="@+id/txt_brand"
                        android:text="Crescina"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:textColor="@color/black"
                        android:textSize="14dp" />
                </LinearLayout>
                <TextView
                    android:id="@+id/txt_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="Crescina HFSC 100% 500 Man 10 TC +10 FL is the complete package for stimulating hair growth and preventing hair loss with a special patented formula for men. The treatment consists of two types of ampoules: The yellow ampoule: Contains Crescina Re-Growth HFSC which uses Cysteine, Lysine, Glycoprotein, and stem-stem engine to help strengthen and protect both hair and follicle from damage and promote hair growth."
                    android:fontFamily="@font/roboto_regular"
                    android:textColor="@color/black"
                    android:textSize="14dp" />
                <View
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    />
                <LinearLayout
                    android:layout_marginBottom="50dp"
                    android:layout_marginTop="15dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="wrap_content"
                        app:cardCornerRadius="39dp"
                        android:id="@+id/card_add_cart"
                        android:layout_marginTop="1dp"
                        android:layout_marginBottom="1dp"
                        android:layout_marginStart="1dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="10dp"
                        app:cardBackgroundColor="#424546"
                        android:layout_height="45dp">
                        <TextView
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:fontFamily="@font/roboto_bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Add to RX"
                            android:textColor="@color/white"/>
                    </com.google.android.material.card.MaterialCardView>
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="wrap_content"
                        app:cardCornerRadius="39dp"
                        android:id="@+id/card_buy_now"
                        android:layout_weight="1"
                        android:layout_marginTop="1dp"
                        android:layout_marginBottom="1dp"
                        android:layout_marginEnd="1dp"
                        android:layout_marginStart="10dp"
                        android:layout_gravity="center"
                        app:cardBackgroundColor="#5DB245"
                        android:layout_height="45dp">
                        <TextView
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:fontFamily="@font/roboto_bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Close"
                            android:textColor="@color/white"/>
                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>
            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>