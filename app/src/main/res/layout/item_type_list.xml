<com.chauthai.swipereveallayout.SwipeRevealLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="horizontal"
    android:layout_marginBottom="10dp"
    app:dragEdge="right"
    app:mode="normal"
    xmlns:android="http://schemas.android.com/apk/res/android"
    >
    <FrameLayout
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="70dp">
        <LinearLayout
            android:id="@+id/layout_swipe_actions"
            android:layout_width="100dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:visibility="visible">
            <com.google.android.material.card.MaterialCardView
                android:layout_width="35dp"
                android:id="@+id/btn_delete"
                app:cardCornerRadius="35dp"
                app:cardBackgroundColor="#FF0000"
                android:layout_height="35dp">
                <ImageView
                    android:layout_gravity="center"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:tint="@color/white"
                    android:src="@drawable/icon_delete_cart"/>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="35dp"
                android:id="@+id/btn_edit"
               android:layout_marginStart="10dp"
                app:cardCornerRadius="35dp"
                app:cardBackgroundColor="#5DB245"
                android:layout_height="35dp">
                <ImageView
                    android:layout_gravity="center"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:tint="@color/white"
                    android:src="@drawable/edit_icon_single"/>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </FrameLayout>
    <FrameLayout
        android:visibility="visible"
        android:minHeight="80dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:layout_marginBottom="10dp"
            android:orientation="vertical"
            app:cardBackgroundColor="#F8F5F5"
            app:cardCornerRadius="10dp"
            android:elevation="5dp"
            >
            <LinearLayout
                android:padding="10dp"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <TextView
                    android:layout_weight="1"
                    android:text="type"
                    android:singleLine="true"
                    android:layout_marginEnd="10dp"
                    android:id="@+id/txt_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_bold"
                    android:lineHeight="20dp"
                    android:textColor="@color/black"
                    android:textSize="14dp" />
                <Switch
                    android:layout_centerInParent="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="7dp"
                    android:checked="false"
                    android:thumbTint="@android:color/white"
                    android:track="@drawable/custom_switch_thumb" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </FrameLayout>
</com.chauthai.swipereveallayout.SwipeRevealLayout>
