<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="20dp"
    android:background="@color/white"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/roboto_bold"
            android:text="Add APIX SKU"
            android:textColor="#424546"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/img_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="27dp"
            android:src="@drawable/close_filter" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="15dp"
        android:background="#7D7C7C" />
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="22dp"
        android:layout_marginTop="15dp"
        android:fontFamily="@font/roboto_regular"
        android:text="SKU"
        android:textColor="@color/black"
        android:textSize="12sp" />

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:cardCornerRadius="5dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="1dp"
        app:cardBackgroundColor="@color/white"
        app:strokeColor="#7D7C7C"
        app:strokeWidth="1dp">

        <TextView
            android:id="@+id/txt_sku"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:background="@android:color/transparent"
            android:hint="SKU"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:textColorHint="#B4ABAB"
            android:textSize="12sp" />
    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="22dp"
        android:layout_marginTop="15dp"
        android:fontFamily="@font/roboto_regular"
        android:text="Count"
        android:textColor="@color/black"
        android:textSize="12sp" />

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:cardCornerRadius="5dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="1dp"
        app:cardBackgroundColor="@color/white"
        app:strokeColor="#7D7C7C"
        app:strokeWidth="1dp">

        <EditText
            android:id="@+id/edit_count"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:background="@android:color/transparent"
            android:hint="Count"
            android:singleLine="true"
            android:textColorHint="#B4ABAB"
            android:textSize="12sp" />
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:id="@+id/card_generate"
        android:layout_height="38dp"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="10dp"
        app:cardBackgroundColor="@color/doctor_main_color"
        app:cardCornerRadius="12dp">
        <RelativeLayout
            android:id="@+id/progressBar_small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone">

            <ProgressBar
                style="?android:attr/progressBarStyle"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:indeterminateDrawable="@drawable/rotating_icon"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </RelativeLayout>
        <TextView
            android:visibility="visible"
            android:id="@+id/txt_generate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:textColor="@color/white"
            android:text="Generate"
            android:textSize="16sp"
            android:fontFamily="@font/roboto_medium"/>
    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
