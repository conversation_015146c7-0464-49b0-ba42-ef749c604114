<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="109dp"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginBottom="6dp"
    app:cardBackgroundColor="#F8F5F5"
    app:cardCornerRadius="5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="26dp"
                android:layout_marginTop="5dp"
                android:fontFamily="@font/roboto_bold"
                android:text="Jithun Raj"
                android:textColor="@color/black"
                android:textSize="13dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="26dp"
                android:fontFamily="@font/roboto_regular"
                android:text="+96546365564"
                android:textColor="@color/black"
                android:textSize="13dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="37dp"
                android:layout_marginTop="2dp"
                android:fontFamily="@font/roboto_regular"
                android:maxWidth="150dp"
                android:maxLines="2"
                android:text="Hawalli ,Rumaithiya 46,Block: 4, Building: 46"
                android:textColor="@color/black"
                android:textSize="13dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginEnd="15dp"
            android:gravity="end"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="26dp"
                android:layout_marginTop="4dp"
                android:fontFamily="@font/roboto_bold"
                android:text="2023-09-19"
                android:textColor="@color/black"
                android:textSize="14dp" />

            <com.google.android.material.card.MaterialCardView
                android:layout_width="79dp"
                android:layout_height="19dp"
                android:layout_margin="1dp"
                app:cardBackgroundColor="#5DB245"
                app:cardCornerRadius="19dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:text="Pending"
                    android:textColor="@color/white"
                    android:textSize="11dp" />
            </com.google.android.material.card.MaterialCardView>

            <ImageView
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginTop="8dp"
                android:src="@drawable/home_cust_user" />
        </LinearLayout>
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>