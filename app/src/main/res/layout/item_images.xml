<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:layout_marginBottom="10dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="54dp"
        android:layout_height="45dp"
        app:cardBackgroundColor="#33D9D9D9"
        app:cardCornerRadius="10dp"
        app:strokeWidth="0dp">

        <ImageView
            android:id="@+id/img_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="10dp" />
    </com.google.android.material.card.MaterialCardView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <TextView
            android:id="@+id/txt_image_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_regular"
            android:textColor="@color/black"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/txt_image_size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:fontFamily="@font/roboto_regular"
            android:textColor="#B9B9B9"
            android:textSize="10sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/btn_dot"
        android:layout_marginEnd="10dp"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="center"
        android:gravity="center">
        <ImageView
            android:layout_gravity="center"
            android:layout_width="15dp"
            android:layout_height="3dp"
            android:src="@drawable/three_dot_image_list"/>
    </LinearLayout>


</LinearLayout>