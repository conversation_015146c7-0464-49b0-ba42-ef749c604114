<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:src="@drawable/success_pass"
            android:layout_width="152dp"
            android:layout_height="152dp" />
        <TextView
            android:textSize="24dp"
            android:fontFamily="@font/roboto_bold"
            android:text="Order Placed Successfully"
            android:textColor="@color/black"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="28dp"/>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:id="@+id/btn_back"
            android:layout_marginStart="25dp"
            android:layout_marginTop="28dp"
            android:layout_marginEnd="25dp"
            app:cardBackgroundColor="@color/main_color"
            app:cardCornerRadius="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:fontFamily="@font/roboto_medium"
                android:gravity="center"
                android:text="Go to Home"
                android:textColor="@color/white"
                android:textSize="18dp" />
        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>