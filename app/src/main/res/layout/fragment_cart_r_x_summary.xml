<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="65dp"
                android:layout_gravity="center_vertical"
                android:background="@color/white"
                android:elevation="5dp"
                android:gravity="center_vertical"

                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/img_back"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/pos_back" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_bold"
                    android:text="RX"
                    android:textColor="@color/black"
                    android:textSize="14sp" />


                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/home_notif" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="16dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Patient Info"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:elevation="1dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="1dp">

                    <LinearLayout

                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/txt_patient_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="15dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Soumya"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/txt_patient_email"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="3dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="<EMAIL>"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/txt_patient_phone"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="3dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="+965 75598838"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/txt_civil_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="3dp"
                            android:fontFamily="@font/roboto_regular"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/txt_dob"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="3dp"
                            android:layout_marginBottom="15dp"
                            android:fontFamily="@font/roboto_regular"
                            android:textColor="@color/black"
                            android:textSize="14sp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_summery"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_marginBottom="100dp" />


            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="50dp"
        android:gravity="center"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_send"
            android:layout_width="match_parent"
            android:layout_height="38dp"
            android:layout_marginEnd="5dp"
            android:layout_weight="1"

            app:cardBackgroundColor="@color/doctor_main_color"
            app:cardCornerRadius="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="19dp"
                    android:layout_height="19dp"
                    android:src="@drawable/print_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="10dp"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:text="Save &amp; Print"
                    android:textColor="@color/white"
                    android:textSize="16sp" />
            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_proceed"
            android:layout_width="match_parent"
            android:layout_height="38dp"
            android:layout_marginStart="5dp"
            android:layout_weight="1"
            android:visibility="visible"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="12dp"
            app:strokeColor="@color/doctor_main_color"
            app:strokeWidth="1dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/roboto_medium"
                android:gravity="center"
                android:text="Save"
                android:textColor="@color/doctor_main_color"
                android:textSize="16sp" />

        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>