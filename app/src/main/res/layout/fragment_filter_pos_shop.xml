<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/lyt_filter"
    android:clickable="false"
    android:visibility="gone"
    android:background="@color/white"
    android:orientation="vertical">
<androidx.core.widget.NestedScrollView
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:scrollbars="none"
    android:fillViewport="true">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="96dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="15dp">

                <ImageView
                    android:id="@+id/img_close"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentStart="true"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/close_filter" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Filters"
                    android:textColor="@color/black"
                    android:textSize="18sp" />
            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_marginStart="1dp"
            android:layout_marginEnd="1dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/rounded_top_corners"
            android:orientation="vertical"
            android:padding="20dp">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_filter_influencer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="15dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_filter_influencer"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Select Influencer"
                            android:textColorHint="#B4ABAB"
                            android:textSize="12sp" />

                        <ImageView
                            android:id="@+id/img_influencer_arrow"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="6dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#222222" />
                    </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/card_influencer_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#40000000"/>
                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_margin="10dp"
                            android:layout_height="wrap_content"
                            android:id="@+id/recycler_influencer"/>
                    </LinearLayout>
                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_search_by_sku"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="15dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">
                    <EditText
                        android:background="@android:color/transparent"
                        android:id="@+id/edit_filter_search_by_sku"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Search By SKU"
                        android:textColorHint="#B4ABAB"
                        android:textSize="12sp" />


                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_filter_search_by_products"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="15dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <EditText
                        android:background="@android:color/transparent"
                        android:id="@+id/edit_filter_by_products"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Search by Products"
                        android:textColorHint="#B4ABAB"
                        android:textSize="12sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_filter_by_brand"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="18dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_filter_by_brand"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="All Brands"
                            android:textColorHint="#B4ABAB"
                            android:textSize="12sp" />

                        <ImageView
                            android:id="@+id/img_filter_by_brand_arrow"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="6dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#222222" />
                    </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/card_filter_by_brand_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#40000000"/>
                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_margin="10dp"
                            android:layout_height="wrap_content"
                            android:id="@+id/recycler_filter_by_brand"/>
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_filter_seller"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="18dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_filter_by_seller"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Select Seller"
                            android:textColorHint="#B4ABAB"
                            android:textSize="12sp" />

                        <ImageView
                            android:id="@+id/img_filter_by_seller"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="6dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#222222" />
                    </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/card_filter_by_seller_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#40000000"/>
                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_margin="10dp"
                            android:layout_height="wrap_content"
                            android:id="@+id/recycler_filter_by_seller"/>
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_filter_medical_rep"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="18dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="50dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_filter_medical_rep"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Select Medical Rep"
                            android:textColorHint="#B4ABAB"
                            android:textSize="12sp" />

                        <ImageView
                            android:id="@+id/img_filter_medical_rep"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="6dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#222222" />
                    </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/card_filter_by_medical_rep_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#40000000"/>
                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_margin="10dp"
                            android:layout_height="wrap_content"
                            android:id="@+id/recycler_filter_by_medical_rep"/>
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"/>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_weight="1"
                    android:id="@+id/card_apply_filter"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="25dp"
                    app:cardCornerRadius="8dp"
                    app:cardBackgroundColor="#A61C5C"
                    android:layout_height="48dp">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:text="Apply Filter"
                        android:textColor="@color/white"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:layout_gravity="center"/>
                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="100dp"
                    android:id="@+id/card_clear"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="25dp"
                    app:cardCornerRadius="8dp"
                    android:layout_marginStart="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:strokeColor="@color/main_color"
                    app:strokeWidth="1dp"
                    android:layout_height="45dp">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:text="Clear"
                        android:textColor="@color/main_color"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:layout_gravity="center"/>
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>
</androidx.core.widget.NestedScrollView>


</androidx.coordinatorlayout.widget.CoordinatorLayout>