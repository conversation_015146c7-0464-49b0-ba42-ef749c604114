<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nested_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="65dp"
                android:layout_gravity="center_vertical"
                android:elevation="5dp"
                android:gravity="center_vertical"

                android:orientation="horizontal">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/img_back"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_alignParentStart="true"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="10dp"
                    app:cardBackgroundColor="#F7F7F7"
                    app:cardCornerRadius="10dp"
                    app:strokeWidth="0dp">

                    <ImageView
                        android:layout_width="6dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center"

                        android:src="@drawable/arrow_brown_new" />
                </com.google.android.material.card.MaterialCardView>


                <TextView
                    android:id="@+id/txt_header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Brand"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="100dp">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_brand"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginEnd="16dp"
                    tools:itemCount="5"
                    tools:listitem="@layout/item_brand_list" />

                <RelativeLayout
                    android:id="@+id/progressBar_small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="#50F9FEFF"
                    android:layout_gravity="center"
                    android:visibility="gone">

                    <ProgressBar
                        style="?android:attr/progressBarStyle"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:indeterminateDrawable="@drawable/rotating_icon"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </RelativeLayout>
            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_add"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:layout_gravity="center"
        android:layout_marginStart="22dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="22dp"
        android:layout_marginBottom="50dp"
        app:cardBackgroundColor="@color/main_color"
        app:cardCornerRadius="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:fontFamily="@font/roboto_medium"
            android:text="Add Brand"
            android:textColor="@color/white"
            android:textSize="14sp" />
    </com.google.android.material.card.MaterialCardView>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>