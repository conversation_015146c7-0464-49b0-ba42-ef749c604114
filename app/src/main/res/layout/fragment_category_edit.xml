<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:layout_gravity="center_vertical"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/img_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentStart="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                app:cardBackgroundColor="#F7F7F7"
                app:cardCornerRadius="10dp"
                app:strokeWidth="0dp">

                <ImageView
                    android:layout_width="6dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center"

                    android:src="@drawable/arrow_brown_new" />
            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:id="@+id/txt_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/roboto_bold"
                android:text="Edit Category"
                android:textColor="@color/black"
                android:textSize="14sp" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentTop="true"

                android:fillViewport="true"
                android:scrollbars="none">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="19dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="150dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="5dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="21dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Edit Category"
                            android:textColor="#424546"
                            android:textSize="16sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="10dp"
                            android:background="#40000000" />


                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="15dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Name *"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="11dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <EditText
                                android:id="@+id/edit_name"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:hint="Name"
                                android:singleLine="true"
                                android:textColorHint="#B4ABAB"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="15dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Arabic Name *"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="11dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <EditText
                                android:id="@+id/edit_arabic_name"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:hint="Arabic Name "
                                android:singleLine="true"
                                android:textColorHint="#B4ABAB"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="15dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Parent Category *"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:id="@+id/card_parent"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="11dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/text_parent"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="10dp"
                                    android:layout_weight="1"
                                    android:fontFamily="@font/roboto_regular"
                                    android:gravity="center_vertical"
                                    android:hint="No Parent"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="12sp" />

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:layout_marginEnd="6dp"
                                    android:src="@drawable/arrow_down"
                                    app:tint="#222222" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="15dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Ordering Number"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="11dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <EditText
                                android:id="@+id/edit_order_level"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:hint="Order Level"
                                android:inputType="number"
                                android:singleLine="true"
                                android:textColorHint="#B4ABAB"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="18dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Higher number has high priority"
                            android:textColor="@color/black"
                            android:textSize="10sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="15dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Type *"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:id="@+id/card_type"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="11dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/text_type"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="10dp"
                                    android:layout_weight="1"
                                    android:fontFamily="@font/roboto_regular"
                                    android:gravity="center_vertical"
                                    android:hint="Nothing Selected"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="12sp" />

                                <ImageView
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:layout_marginEnd="6dp"
                                    android:src="@drawable/arrow_down"
                                    app:tint="#222222" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="10dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Banner (200x200)"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:id="@+id/card_banner"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="7dp"
                            android:layout_marginEnd="15dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="53dp"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:background="#DADADA"
                                    android:gravity="center">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="22dp"
                                        android:layout_gravity="center"
                                        android:fontFamily="@font/roboto_bold"
                                        android:gravity="center"
                                        android:text="Browse"
                                        android:textColor="@color/black"
                                        android:textSize="12sp" />
                                </LinearLayout>

                                <TextView
                                    android:id="@+id/txt_banner_name"
                                    android:layout_width="wrap_content"
                                    android:layout_height="22dp"
                                    android:layout_gravity="center"
                                    android:layout_marginStart="7dp"
                                    android:fontFamily="@font/roboto_regular"
                                    android:gravity="center"
                                    android:hint="Choose file"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="12sp" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="10dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Icon (32x32)"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:id="@+id/card_icon"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="7dp"
                            android:layout_marginEnd="15dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="53dp"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:background="#DADADA"
                                    android:gravity="center">

                                    <TextView

                                        android:layout_width="wrap_content"
                                        android:layout_height="22dp"
                                        android:layout_gravity="center"
                                        android:fontFamily="@font/roboto_bold"
                                        android:gravity="center"
                                        android:text="Browse"
                                        android:textColor="@color/black"
                                        android:textSize="12sp" />
                                </LinearLayout>

                                <TextView
                                    android:id="@+id/txt_icon"
                                    android:layout_width="wrap_content"
                                    android:layout_height="22dp"
                                    android:layout_gravity="center"
                                    android:layout_marginStart="7dp"
                                    android:fontFamily="@font/roboto_regular"
                                    android:gravity="center"
                                    android:hint="Choose file"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="12sp" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="15dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Meta Title"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="11dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <EditText
                                android:id="@+id/edit_meta_title"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:hint="Meta Title"
                                android:singleLine="true"
                                android:textColorHint="#B4ABAB"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="20dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Meta Description"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="157dp"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="7dp"
                            android:layout_marginEnd="15dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <EditText
                                android:id="@+id/edit_meta_desc"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_vertical|top"
                                android:layout_margin="12dp"
                                android:background="@android:color/transparent"
                                android:fontFamily="@font/roboto_regular"
                                android:gravity="center_vertical|top"
                                android:hint="Meta Description"
                                android:textColorHint="#B4ABAB"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="15dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Arabic Meta Title"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="11dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <EditText
                                android:id="@+id/edit_arabic_meta_title"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:hint="Arabic Meta Title"
                                android:singleLine="true"
                                android:textColorHint="#B4ABAB"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="20dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Arabic Meta Description"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="157dp"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="7dp"
                            android:layout_marginEnd="15dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <EditText
                                android:id="@+id/edit_arabic_meta_desc"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_vertical|top"
                                android:layout_margin="12dp"
                                android:background="@android:color/transparent"
                                android:fontFamily="@font/roboto_regular"
                                android:gravity="center_vertical|top"
                                android:hint="Arabic Meta Description"
                                android:textColorHint="#B4ABAB"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="18dp"
                            android:layout_marginTop="15dp"
                            android:fontFamily="@font/roboto_regular"
                            android:text="Commission Rate"
                            android:textColor="@color/black"
                            android:textSize="12sp" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="11dp"
                            android:layout_marginBottom="50dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <EditText
                                    android:id="@+id/edit_commission_rate"
                                    android:background="@android:color/transparent"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="10dp"
                                    android:layout_weight="1"
                                    android:inputType="number"
                                    android:fontFamily="@font/roboto_regular"
                                    android:gravity="center_vertical"
                                    android:hint="Commission Rate"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="12sp" />

                                <LinearLayout
                                    android:background="#edeeee"
                                    android:layout_gravity="center"
                                    android:gravity="center"
                                    android:layout_width="50dp"
                                    android:layout_height="match_parent">

                                    <ImageView
                                        android:padding="2dp"
                                        android:layout_width="15dp"
                                        android:layout_height="15dp"
                                        android:layout_gravity="center"
                                        android:src="@drawable/percentage"
                                        app:tint="#222222" />
                                </LinearLayout>

                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>


                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            </androidx.core.widget.NestedScrollView>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="83dp"
                android:layout_alignParentBottom="true"
                android:background="@color/white"
                android:elevation="10dp"
                android:visibility="visible">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_save"
                    android:layout_width="145dp"
                    android:layout_height="54dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerInParent="true"
                    android:layout_marginEnd="28dp"
                    app:cardBackgroundColor="#A61C5C"
                    app:cardCornerRadius="51dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:text="Save"
                        android:textColor="@color/white"
                        android:textSize="18sp" />
                </com.google.android.material.card.MaterialCardView>
            </RelativeLayout>
        </RelativeLayout>

    </LinearLayout>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>