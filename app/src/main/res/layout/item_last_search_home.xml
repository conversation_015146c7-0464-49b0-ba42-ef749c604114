<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="30dp"
    android:layout_marginTop="1dp"
    android:layout_marginBottom="1dp"
    android:layout_marginStart="1dp"
    android:layout_marginEnd="3dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="34dp"
    app:cardBackgroundColor="#424546">
    <TextView
        android:layout_gravity="center"
        android:gravity="center"
        android:textSize="14sp"
        android:fontFamily="@font/roboto_bold"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="2dp"
        android:text="Wash Gel "
        android:layout_marginStart="19dp"
        android:layout_marginEnd="19dp"
        android:textColor="@color/white"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        />

</com.google.android.material.card.MaterialCardView>