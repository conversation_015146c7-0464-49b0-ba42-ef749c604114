<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@android:color/transparent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/img_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentStart="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                app:cardBackgroundColor="#F7F7F7"
                app:cardCornerRadius="10dp"
                app:strokeWidth="0dp">

                <ImageView
                    android:layout_width="6dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center"

                    android:src="@drawable/arrow_brown_new" />
            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:id="@+id/txt_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/roboto_bold"
                android:text="All uploaded files"
                android:textColor="@color/black"
                android:textSize="14sp" />

        </RelativeLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nested_scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="16dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_marginEnd="12dp"

                        android:layout_marginBottom="1dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="5dp"
                        app:strokeColor="#7D7C7C"
                        app:strokeWidth="1dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/edit_search"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="15dp"
                                android:layout_weight="1"
                                android:background="@android:color/transparent"
                                android:fontFamily="@font/roboto_regular"
                                android:gravity="center_vertical"
                                android:hint="Search"
                                android:textColorHint="#B4ABAB"
                                android:textSize="12sp" />

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_marginEnd="17dp"
                                android:src="@drawable/search_green" />
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_sort_by"
                        android:layout_width="95dp"
                        android:layout_height="45dp"
                        android:layout_marginEnd="6dp"
                        app:cardBackgroundColor="@color/nav_selected"
                        app:cardCornerRadius="10dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="15dp"
                                android:layout_marginEnd="5dp"
                                android:src="@drawable/filter_home" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Sort By"
                                android:textColor="@color/white"
                                android:textSize="15sp" />
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_media"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp" />

                <RelativeLayout
                    android:id="@+id/progressBar_small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="#50F9FEFF"
                    android:visibility="gone">

                    <ProgressBar
                        style="?android:attr/progressBarStyle"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:indeterminateDrawable="@drawable/rotating_icon"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </RelativeLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>

    <ImageView
        android:visibility="gone"
        android:id="@+id/fab_move_back"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_margin="20dp"
        android:background="@drawable/fab_background"
        android:clickable="true"
        android:contentDescription="Floating Back"
        android:elevation="8dp"
        android:rotation="180"
        android:padding="18dp"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:src="@drawable/arrow_white_right" />

</androidx.constraintlayout.widget.ConstraintLayout>