<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="18dp"
    android:layout_weight="1"
    android:layout_marginEnd="13dp"
    app:cardBackgroundColor="@color/white"
    android:elevation="0dp"
    app:strokeWidth="0dp"
    android:layout_marginBottom="14dp"
    app:cardElevation="0dp"
    android:layout_height="wrap_content">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="138dp">
            <ImageView
                android:id="@+id/img_product"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                />
            <com.google.android.material.card.MaterialCardView
                android:layout_width="77dp"
                android:id="@+id/card_stock"
                android:layout_marginEnd="11dp"
                app:cardCornerRadius="19dp"
                app:cardBackgroundColor="#5DB245"
                android:layout_marginTop="6dp"
                android:layout_alignParentEnd="true"
                android:layout_height="22dp">
                <TextView
                    android:id="@+id/txt_stock"
                    android:layout_width="wrap_content"
                    android:textSize="11sp"
                    android:text="In stock: 174"
                    android:layout_gravity="center"
                    android:textColor="@color/white"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:layout_height="wrap_content"/>
            </com.google.android.material.card.MaterialCardView>
           <LinearLayout
               android:id="@+id/lyt_view"
               android:layout_width="30dp"
               android:layout_alignParentBottom="true"
               android:layout_marginBottom="21dp"
               android:layout_marginStart="6dp"
               android:layout_height="30dp">
               <ImageView
                   android:layout_gravity="center"
                   android:layout_width="24dp"
                   android:layout_height="24dp"

                   android:src="@drawable/view_icon"
                 />
           </LinearLayout>


        </RelativeLayout>
        <TextView
            android:id="@+id/txt_product_name"
            android:layout_marginTop="3dp"
            android:text="Nexium Tablet 40  Mg14'S"
            android:textAlignment="center"
            android:textSize="13sp"
            android:textColor="@color/black"
            android:fontFamily="@font/roboto_bold"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:maxLines="2"/>
        <TextView
            android:id="@+id/txt_prize"
            android:text="KD 3.900"
            android:textAlignment="center"
            android:textSize="14sp"
            android:textColor="#A61C5C"
            android:fontFamily="@font/roboto_bold"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_add_cart"
                android:layout_centerInParent="true"
                android:layout_width="123dp"
                app:cardCornerRadius="18dp"
                android:layout_marginTop="15dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="6dp"
                app:cardBackgroundColor="@color/main_color"
                android:layout_gravity="center"
                android:layout_height="36dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:textSize="14sp"
                    android:text="Add to RX"
                    android:layout_gravity="center"
                    android:textColor="@color/white"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:layout_height="wrap_content"/>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_not_available"
                android:layout_width="123dp"
                android:visibility="gone"
                android:layout_centerInParent="true"
                android:layout_height="36dp"
                android:layout_gravity="center"
                android:layout_marginStart="1dp"
                android:layout_marginTop="15dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="#EC1448"
                app:cardCornerRadius="18dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:text="Not Available"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
            </com.google.android.material.card.MaterialCardView>

        </RelativeLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>