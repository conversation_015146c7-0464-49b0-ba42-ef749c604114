<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FAFAFE"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/img_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentStart="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                app:cardBackgroundColor="#F7F7F7"
                app:cardCornerRadius="10dp"
                app:strokeWidth="0dp">

                <ImageView
                    android:layout_width="6dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center"

                    android:src="@drawable/arrow_brown_new" />
            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:id="@+id/txt_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/roboto_bold"
                android:text="POS RX"
                android:textColor="@color/black"
                android:textSize="16sp" />
            <RelativeLayout
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:layout_width="33dp"
                android:layout_height="27dp"
                android:layout_marginEnd="12dp">

                <ImageView
                    android:id="@+id/img_cart"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/rx_icon_new" />

                <RelativeLayout
                    android:visibility="gone"
                    android:id="@+id/rlt_cart_count"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/cart_count_bg" />

                    <TextView
                        android:id="@+id/txt_cart_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center"
                        android:maxLength="2"
                        android:text="2"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </RelativeLayout>
            </RelativeLayout>
        </RelativeLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nested_scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_marginBottom="50dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|top"
                    android:layout_marginTop="30dp"

                    android:gravity="center_vertical|top"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_cust_box"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="12dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginEnd="12dp"
                        android:layout_marginBottom="1dp"
                        android:layout_weight="1"
                        android:elevation="2dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="2dp"
                        app:strokeWidth="1dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="15dp"
                                android:layout_marginTop="10dp"
                                android:layout_marginBottom="10dp"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Patient Info"
                                android:textColor="@color/black"
                                android:textSize="18sp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#1F000000" />



                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#1F000000" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_marginStart="14dp"
                                android:layout_marginTop="30dp"
                                android:layout_marginEnd="11dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="5dp"
                                app:strokeColor="#1A000000"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:orientation="horizontal">

                                    <LinearLayout
                                        android:layout_width="53dp"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center"
                                        android:background="#DADADA"
                                        android:gravity="center">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="22dp"
                                            android:layout_gravity="center"
                                            android:fontFamily="@font/roboto_bold"
                                            android:gravity="center"
                                            android:text="+965"
                                            android:textColor="@color/black"
                                            android:textSize="12sp" />
                                    </LinearLayout>

                                    <EditText
                                        android:id="@+id/edit_phone"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="13dp"
                                        android:background="@android:color/transparent"
                                        android:fontFamily="@font/roboto_regular"
                                        android:gravity="center_vertical"
                                        android:hint="Contact Number"
                                        android:inputType="phone"
                                        android:maxLength="8"
                                        android:textColorHint="#B4ABAB"
                                        android:textSize="12sp" />
                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:id="@+id/txt_phone_empty"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="20dp"
                                android:layout_marginTop="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:gravity="center_vertical"
                                android:hint="Please Enter Contact Number"
                                android:singleLine="true"
                                android:textColorHint="#FF0000"
                                android:textSize="12sp"
                                android:visibility="gone" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_marginStart="14dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginEnd="11dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="5dp"
                                app:strokeColor="#1A000000"
                                app:strokeWidth="1dp">

                                <EditText
                                    android:id="@+id/edit_civil_id"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:background="@android:color/transparent"
                                    android:hint="Civil ID"
                                    android:inputType="number"
                                    android:singleLine="true"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="12sp" />
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:id="@+id/txt_civil_empty"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="20dp"
                                android:layout_marginTop="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:gravity="center_vertical"
                                android:hint="Please Enter Civil Id"
                                android:singleLine="true"
                                android:textColorHint="#FF0000"
                                android:textSize="12sp"
                                android:visibility="gone" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_marginStart="14dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginEnd="11dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="5dp"
                                app:strokeColor="#1A000000"
                                app:strokeWidth="1dp">

                                <EditText
                                    android:id="@+id/edit_full_name"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:background="@android:color/transparent"
                                    android:hint="Full Name"
                                    android:singleLine="true"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="12sp" />
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:id="@+id/txt_name_empty"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="20dp"
                                android:layout_marginTop="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:gravity="center_vertical"
                                android:hint="Please Enter Full Name"
                                android:singleLine="true"
                                android:textColorHint="#FF0000"
                                android:textSize="12sp"
                                android:visibility="gone" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_marginStart="14dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginEnd="11dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="5dp"
                                app:strokeColor="#1A000000"
                                app:strokeWidth="1dp">

                                <EditText
                                    android:id="@+id/edit_email"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:background="@android:color/transparent"
                                    android:hint="Email"
                                    android:inputType="textEmailAddress"
                                    android:singleLine="true"
                                    android:textColorHint="#B4ABAB"
                                    android:textSize="12sp" />
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:id="@+id/txt_email_empty"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="20dp"
                                android:layout_marginTop="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:gravity="center_vertical"
                                android:hint="Please Enter Email"
                                android:singleLine="true"
                                android:textColorHint="#FF0000"
                                android:textSize="12sp"
                                android:visibility="gone" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_marginStart="14dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginEnd="11dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="5dp"
                                app:strokeColor="#1A000000"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/txt_dob"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginEnd="10dp"
                                        android:layout_weight="1"
                                        android:background="@android:color/transparent"
                                        android:gravity="center_vertical"
                                        android:hint="DOB"
                                        android:singleLine="true"
                                        android:textColorHint="#B4ABAB"
                                        android:textSize="12sp" />

                                    <LinearLayout
                                        android:id="@+id/lyt_calendar"
                                        android:layout_width="40dp"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center"
                                        android:layout_marginEnd="5dp"
                                        android:gravity="center">

                                        <ImageView
                                            android:layout_width="20dp"
                                            android:layout_height="20dp"
                                            android:src="@drawable/calendar_new" />
                                    </LinearLayout>
                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:id="@+id/txt_dob_empty"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="20dp"
                                android:layout_marginTop="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:gravity="center_vertical"
                                android:hint="Please Choose DOB"
                                android:singleLine="true"
                                android:textColorHint="#FF0000"
                                android:textSize="12sp"
                                android:visibility="gone" />

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_marginStart="14dp"
                                android:layout_marginTop="12dp"
                                android:layout_marginEnd="11dp"
                                android:layout_marginBottom="25dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="5dp"
                                app:strokeColor="#1A000000"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:orientation="horizontal">

                                    <LinearLayout
                                        android:layout_width="53dp"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center"
                                        android:background="#DADADA"
                                        android:gravity="center">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="22dp"
                                            android:layout_gravity="center"
                                            android:fontFamily="@font/roboto_bold"
                                            android:gravity="center"
                                            android:text="+965"
                                            android:textColor="@color/black"
                                            android:textSize="12sp" />
                                    </LinearLayout>

                                    <EditText
                                        android:singleLine="true"
                                        android:id="@+id/edit_alt_phone"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center_vertical"
                                        android:layout_marginStart="13dp"
                                        android:background="@android:color/transparent"
                                        android:fontFamily="@font/roboto_regular"
                                        android:gravity="center_vertical"
                                        android:hint="Alternative Number"
                                        android:textColorHint="#B4ABAB"
                                        android:textSize="12sp" />
                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>

                            <RelativeLayout
                                android:id="@+id/rlt_btn"
                                android:paddingStart="14dp"
                                android:paddingEnd="11dp"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="25dp"
                                android:visibility="invisible">

                                <com.google.android.material.card.MaterialCardView
                                    android:id="@+id/card_create"
                                    android:visibility="visible"
                                    android:layout_width="218dp"
                                    android:layout_height="39dp"
                                    android:layout_alignParentStart="true"
                                    android:layout_centerInParent="true"
                                    android:layout_marginEnd="6dp"
                                    app:cardBackgroundColor="@color/main_color"
                                    app:cardCornerRadius="10dp"
                                    app:strokeWidth="0dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/roboto_medium"
                                            android:singleLine="true"
                                            android:text="Create Patient"
                                            android:textColor="@color/white"
                                            android:textSize="18sp" />
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>

                                <com.google.android.material.card.MaterialCardView
                                    android:id="@+id/card_update"
                                    android:layout_width="218dp"
                                    android:layout_height="39dp"
                                    android:layout_alignParentStart="true"
                                    android:layout_centerInParent="true"
                                    android:layout_marginEnd="6dp"
                                    android:visibility="gone"
                                    app:cardBackgroundColor="@color/main_color"
                                    app:cardCornerRadius="10dp"
                                    app:strokeWidth="0dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_gravity="center"
                                            android:fontFamily="@font/roboto_medium"
                                            android:gravity="center"
                                            android:text="Update"
                                            android:textColor="@color/white"
                                            android:textSize="18sp" />
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>

                                <com.google.android.material.card.MaterialCardView
                                    android:id="@+id/card_next"
                                    android:layout_width="218dp"
                                    android:layout_height="39dp"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerInParent="true"
                                    android:layout_marginEnd="6dp"
                                    android:visibility="gone"
                                    app:cardBackgroundColor="@color/main_color"
                                    app:cardCornerRadius="10dp"
                                    app:strokeWidth="0dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center"
                                        android:layout_marginStart="10dp"
                                        android:layout_marginEnd="10dp"
                                        android:gravity="center"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_gravity="center"
                                            android:fontFamily="@font/roboto_medium"
                                            android:gravity="center"
                                            android:text="Next"
                                            android:textColor="@color/white"
                                            android:textSize="18sp" />
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>
                            </RelativeLayout>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_order_box"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="12dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginEnd="12dp"
                        android:layout_marginBottom="1dp"
                        android:layout_weight="1"
                        android:elevation="2dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="2dp"
                        app:strokeWidth="0.5dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="15dp"
                                android:layout_marginTop="10dp"
                                android:layout_marginBottom="10dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="RX History"
                                android:textColor="@color/black"
                                android:textSize="18sp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#1F000000" />

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <RelativeLayout
                                    android:layout_centerInParent="true"
                                    android:id="@+id/progressBarOrder"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="#50F9FEFF"
                                    android:visibility="gone">

                                    <ProgressBar
                                        style="?android:attr/progressBarStyle"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_centerInParent="true"
                                        android:layout_gravity="center"
                                        android:indeterminateDrawable="@drawable/rotating_icon"
                                        android:visibility="visible"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        app:layout_constraintEnd_toEndOf="parent"
                                        app:layout_constraintStart_toStartOf="parent"
                                        app:layout_constraintTop_toTopOf="parent" />
                                </RelativeLayout>

                                <TextView
                                    android:layout_centerInParent="true"
                                    android:id="@+id/txt_no_data_order"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:layout_marginTop="25dp"
                                    android:fontFamily="@font/roboto_medium"
                                    android:text="No Data"
                                    android:textColor="@color/black"
                                    android:gravity="center"
                                    android:textSize="14sp"
                                    android:visibility="gone" />

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/recycler_all_orders"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_margin="15dp"
                                    tools:itemCount="3"

                                    tools:listitem="@layout/tab_pos_rx" />
                            </RelativeLayout>


                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/card_view_all_order"
                                android:layout_width="218dp"
                                android:layout_height="39dp"
                                android:layout_centerInParent="true"
                                android:layout_gravity="center"
                                android:layout_marginBottom="20dp"

                                app:cardBackgroundColor="@color/main_color"

                                app:cardCornerRadius="10dp"
                                app:strokeWidth="0dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_gravity="center"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:gravity="center"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:fontFamily="@font/roboto_medium"
                                        android:gravity="center"
                                        android:text="View All Orders"
                                        android:textColor="@color/white"
                                        android:textSize="18sp" />
                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>



                <RelativeLayout
                    android:id="@+id/progressBar_small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="#50F9FEFF"
                    android:visibility="gone">

                    <ProgressBar
                        style="?android:attr/progressBarStyle"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:indeterminateDrawable="@drawable/rotating_icon"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </RelativeLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>