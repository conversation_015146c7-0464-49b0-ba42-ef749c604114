<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="129dp"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginBottom="21dp"
    android:elevation="5dp"
    app:cardBackgroundColor="@color/white"
    android:orientation="vertical"
    app:cardCornerRadius="8dp"
    app:cardElevation="5dp"
    app:strokeColor="@color/main_color"
    app:strokeWidth="1dp"

    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="10dp">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_reminder"
            android:layout_width="24dp"
            android:layout_height="24dp"
            app:cardBackgroundColor="@android:color/transparent"
            app:cardCornerRadius="50dp"
            app:strokeColor="@color/main_color"
            app:strokeWidth="1dp">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_reminder_selected"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_gravity="center"
                android:visibility="gone"
                app:cardBackgroundColor="@color/main_color"
                app:cardCornerRadius="50dp" />

        </com.google.android.material.card.MaterialCardView>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="5dp"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/txt_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_bold"
                    android:lineHeight="20dp"
                    android:textColor="@color/black"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/txt_edit"
                    android:layout_width="40dp"
                    android:layout_height="30dp"
                    android:fontFamily="@font/roboto_bold"
                    android:lineHeight="20dp"
                    android:text="Edit"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:textColor="@color/main_color"
                    android:textSize="12dp" />
            </LinearLayout>

            <TextView
                android:id="@+id/txt_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:fontFamily="@font/roboto_bold"
                android:lineHeight="20dp"
                android:textColor="#151515"
                android:textSize="14dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="14dp"
                    android:layout_height="19dp"
                    android:src="@drawable/blue_loc"
                    app:tint="#0D4B30" />

                <TextView
                    android:id="@+id/txt_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:fontFamily="@font/roboto_regular"
                    android:lineHeight="17dp"
                    android:maxWidth="243dp"
                    android:textColor="@color/black"
                    android:textSize="11dp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>


</com.google.android.material.card.MaterialCardView>
