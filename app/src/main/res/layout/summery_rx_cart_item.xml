<?xml version="1.0" encoding="utf-8"?>


<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"

    app:cardBackgroundColor="#FEFEFE"
    app:cardCornerRadius="10dp"
    app:cardElevation="5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="10dp">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="100dp"
            android:layout_height="85dp"
            app:cardBackgroundColor="#F5F6FA"
            app:cardCornerRadius="10dp"
            app:cardElevation="1dp">

            <ImageView
                android:layout_width="77dp"
                android:layout_height="74dp"
                android:layout_gravity="center"
                android:id="@+id/img_product" />
        </com.google.android.material.card.MaterialCardView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_medium"
                android:singleLine="true"
                android:id="@+id/txt_pd_name"
                android:textColor="#1D1E20"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/txt_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="11dp"
                android:fontFamily="@font/roboto_regular"
                android:text="Qty : 1"
                android:textColor="#8F959E"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/txt_dose_details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="11dp"
                android:fontFamily="@font/roboto_medium"

                android:text="Dose:Daily 1 Dose Time: Before Meal Duration:1 week"
                android:textColor="#1D1E20"
                android:textSize="12sp" />

        </LinearLayout>

        <ImageView
            android:id="@+id/img_delete"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginStart="5dp"
            android:src="@drawable/icon_delete_cart" />
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>

