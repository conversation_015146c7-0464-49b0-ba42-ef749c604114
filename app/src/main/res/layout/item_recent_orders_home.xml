<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="78dp"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginBottom="6dp"
    app:cardBackgroundColor="#F8F5F5"
    app:cardCornerRadius="5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="24dp"

                android:layout_marginTop="3dp"
                android:fontFamily="@font/roboto_medium"
                android:text="@string/dummy_order_id"
                android:textColor="@color/nav_selected"
                android:textSize="11dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="26dp"
                android:fontFamily="@font/roboto_bold"
                android:singleLine="true"
                android:text="Yellow Rose"
                android:textColor="@color/black"
                android:textSize="13dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_regular"
                android:singleLine="true"
                android:text="2024-03-12"
                android:textColor="@color/black"
                android:textSize="13dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginEnd="15dp"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="26dp"
                android:fontFamily="@font/roboto_bold"
                android:text="KD 3.9"
                android:textColor="@color/black"
                android:textSize="14dp" />

            <com.google.android.material.card.MaterialCardView
                android:layout_width="79dp"
                android:layout_height="19dp"
                android:layout_margin="1dp"
                app:cardBackgroundColor="@color/nav_selected"
                app:cardCornerRadius="19dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:textColor="@color/white"
                    android:textSize="11dp"
                    android:text="Pending"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"/>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>