<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="170dp"
    android:layout_height="227dp"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="11dp"
    android:layout_marginBottom="1dp"
    android:elevation="1dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="18dp"
    app:cardElevation="1dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="5dp">
        <ImageView
            android:id="@+id/img_product"
            android:layout_alignParentTop="true"
            android:layout_width="match_parent"
            android:layout_height="138dp"
            android:src="@drawable/dummy_image"
            />
        <com.google.android.material.card.MaterialCardView
            android:layout_width="77dp"
            android:layout_height="19dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="9dp"
            android:layout_marginEnd="13dp"
            app:cardBackgroundColor="#5DB245">

            <TextView
                android:textSize="11dp"
                android:textColor="@color/white"
                android:fontFamily="@font/roboto_bold"
                android:text="stock 174"
                android:layout_gravity="center"
                android:textAlignment="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </com.google.android.material.card.MaterialCardView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_below="@+id/img_product">
            <TextView
                android:text="Nexium Tablet 40
Mg 14'S"
                android:layout_width="match_parent"
                android:textSize="13dp"
                android:textColor="@color/black"
                android:layout_height="40dp"
                android:fontFamily="@font/roboto_medium"
                android:layout_marginStart="6dp"
                android:layout_marginEnd="10dp"/>
            <LinearLayout
                android:layout_marginTop="3dp"
                android:layout_marginStart="6dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="26dp"
                    android:text="KD 3.900"
                    android:textColor="#A61C5C"
                    android:textSize="16dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_bold"/>
                <ImageView
                    android:layout_width="25dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="6dp"
                    android:src="@drawable/sale_icon"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="26dp"
                    android:text="392"
                    android:textColor="@color/black"
                    android:textSize="14dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_bold"/>
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>

</com.google.android.material.card.MaterialCardView>