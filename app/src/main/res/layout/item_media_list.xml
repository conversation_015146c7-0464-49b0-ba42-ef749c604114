<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginEnd="13dp"
    android:layout_marginBottom="14dp"
    android:layout_weight="1"
    android:id="@+id/card_media"
    android:elevation="1dp"
    app:strokeColor="@color/white"
    app:strokeWidth="1dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="18dp"
    app:cardElevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_margin="10dp"
            app:cardBackgroundColor="#F7F7F7"
            app:strokeWidth="0dp">

            <ImageView
                android:id="@+id/img_media"
                android:src="@drawable/dummy_image_new"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </com.google.android.material.card.MaterialCardView>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/txt_media"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="5dp"
            android:textSize="12dp"
            android:layout_marginBottom="10dp"
            android:fontFamily="@font/roboto_regular"
            android:layout_gravity="center"
            />
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>