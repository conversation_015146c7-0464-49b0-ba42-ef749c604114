<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/main_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="65dp"
                android:layout_gravity="center_vertical"
                android:elevation="5dp"
                android:gravity="center_vertical"

                android:orientation="horizontal">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/img_back"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_alignParentStart="true"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="10dp"
                    app:cardBackgroundColor="#F7F7F7"
                    app:cardCornerRadius="10dp"
                    app:strokeWidth="0dp">

                    <ImageView
                        android:layout_width="6dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center"

                        android:src="@drawable/arrow_brown_new" />
                </com.google.android.material.card.MaterialCardView>


                <TextView
                    android:id="@+id/txt_header"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Shop"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

            </RelativeLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:layout_marginEnd="18dp"
                android:fitsSystemWindows="true"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Customer "
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="18dp"
                    android:layout_marginBottom="1dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/lyt_choose_customer"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_margin="2dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="5dp"
                        app:strokeColor="#DBE0E6"
                        app:strokeWidth="1dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/txt_customer"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="15dp"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_regular"
                                android:gravity="center_vertical"
                                android:hint="--- Walk In Customer ---"
                                android:textColorHint="#B4ABAB"
                                android:textSize="14sp" />

                            <ImageView
                                android:id="@+id/img_delivery_staff_arrow"
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_marginEnd="6dp"
                                android:src="@drawable/arrow_down"
                                app:tint="#222222" />
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/add_cust"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginStart="10dp"
                        app:cardBackgroundColor="@color/main_color"
                        app:cardCornerRadius="5dp"
                        app:strokeWidth="0dp">

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_gravity="center"
                            android:src="@drawable/add_cust_icon"

                            />
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="30dp"
                    android:layout_marginBottom="20dp"
                    android:background="#DBE0E6" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Items"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/txt_no_data"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_marginTop="50dp"
                    android:fontFamily="@font/roboto_regular"
                    android:gravity="center|top"
                    android:text="No Data"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:visibility="gone" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_rx_cart"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="27dp"
                    android:layout_marginBottom="10dp"
                    android:visibility="visible"
                    tools:itemCount="1"
                    tools:listitem="@layout/pos_shop_cart_item" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="#9EF5F5F5"
                    android:elevation="1dp"
                    app:cardCornerRadius="8dp"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="10dp"
                        android:visibility="visible">


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="21dp"
                            android:layout_marginEnd="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Sub Total"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/txt_sub_total"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="KWD  0.000"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Item Count"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/txt_item_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Tax"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/txt_tax"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="KWD  0.000"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Shipping"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/txt_shipping"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="KWD  0.000"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Discount"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/txt_discount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="KWD  0.000"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                        </LinearLayout>


                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.8dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="#80000000" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="20dp"
                            android:layout_marginBottom="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Total"
                                android:textColor="@color/black"
                                android:textSize="19sp" />

                            <TextView
                                android:id="@+id/txt_total"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_bold"
                                android:text="KWD  0.000"
                                android:textColor="@color/black"
                                android:textSize="19sp" />

                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_proceed"
                    android:layout_width="match_parent"
                    android:layout_height="54dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="40dp"
                    android:layout_marginBottom="22dp"
                    app:cardBackgroundColor="@color/main_color"
                    app:cardCornerRadius="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Proceed to payment"
                        android:textColor="@color/white"
                        android:textSize="14sp" />
                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <include layout="@layout/sheet_add_address_customer"
        android:id="@+id/page_address_new"/>
    <include layout="@layout/fragment_shipping_address_cart"
        android:id="@+id/page_list"/>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>