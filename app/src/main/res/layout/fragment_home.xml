<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    tools:context=".ui.home.HomeFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:background="@color/white"
            android:elevation="5dp"

            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_home_menu"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_alignParentStart="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                android:src="@drawable/home_menu" />

            <ImageView
                android:layout_width="65dp"
                android:layout_height="35dp"
                android:layout_centerInParent="true"
                android:src="@drawable/wasfa_logo" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:layout_marginEnd="20dp"
                android:src="@drawable/home_notif" />
        </RelativeLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nested_scroll"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="200dp"
                android:orientation="vertical">
                <!--daily operation-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="13dp"
                        android:layout_marginTop="31dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="DAILY OPERATIONAL"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <include
                        android:id="@+id/page_daily_operation"
                        layout="@layout/daily_operation" />
                </LinearLayout>
                <!--data management-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="13dp"
                        android:layout_marginTop="26dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="DATA MANAGEMENT"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <include
                        android:id="@+id/page_data_management"
                        layout="@layout/data_management" />
                </LinearLayout>
                <!--general-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="13dp"
                        android:layout_marginTop="21dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="GENERAL"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_filter"
                        android:layout_width="77dp"
                        android:layout_height="37dp"
                        android:layout_marginStart="13dp"
                        android:layout_marginTop="6dp"
                        app:cardBackgroundColor="@color/nav_selected"
                        app:cardCornerRadius="10dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="15dp"
                                android:layout_height="15dp"
                                android:src="@drawable/filter_home" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Filter"
                                android:textColor="@color/white"
                                android:textSize="15sp" />
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                  <include android:id="@+id/page_general"
                      layout="@layout/general"/>
                </LinearLayout>

                <!--no of sales graph-->

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="352dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="29dp"
                    android:layout_marginEnd="16dp"
                    android:elevation="2dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="14dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="6sp"
                            android:text="Sales Analytics"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="15dp"
                            android:background="#2B000000" />

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/chart_no_of_sales"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="30dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>


                <!--sales analysis apix-->

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="352dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="29dp"
                    android:layout_marginEnd="16dp"
                    android:elevation="2dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="14dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="6sp"
                            android:text="Sales Analytics"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="15dp"
                            android:background="#2B000000" />

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/lineChart"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="30dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <!--docter graph-->

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="352dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="29dp"
                    android:layout_marginEnd="16dp"
                    android:elevation="2dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="14dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="6sp"
                            android:text="Doctor Performance"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="15dp"
                            android:background="#2B000000" />

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/chart_docter"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="30dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <!--sales hourly basics-->

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="352dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="29dp"
                    android:layout_marginEnd="16dp"
                    android:elevation="2dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="14dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="6sp"
                            android:text="Sales Hourly Basis"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="15dp"
                            android:background="#2B000000" />

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/chart_sales_hourly_basics"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="30dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <!--monthly sales 2-->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="352dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:elevation="2dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="14dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="6sp"
                            android:text="Monthly Sales"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="15dp"
                            android:background="#2B000000" />

                        <com.github.mikephil.charting.charts.BarChart
                            android:id="@+id/chart_monthly_sales"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="30dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <!--bar chart-->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="352dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:elevation="2dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="14dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="6sp"
                            android:text="Monthly Sales"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="15dp"
                            android:background="#2B000000" />

                        <com.github.mikephil.charting.charts.BarChart
                            android:id="@+id/barChart"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="30dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <!--monthly with 2-->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="352dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:elevation="2dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="14dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="6sp"
                            android:text="Monthly Sales"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="15dp"
                            android:background="#2B000000" />

                        <com.github.mikephil.charting.charts.BarChart
                            android:id="@+id/chart_monthly_sales_2"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="30dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <!--pie chart-->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="352dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:elevation="2dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="14dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="6sp"
                            android:text="Revenue and Sales"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="15dp"
                            android:background="#2B000000" />

                        <com.github.mikephil.charting.charts.PieChart
                            android:id="@+id/pie_chart"
                            android:layout_width="250dp"
                            android:layout_gravity="center"
                            android:layout_height="250dp"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="30dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <!-- revenue bar chart-->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="352dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:elevation="2dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="14dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="6sp"
                            android:text="Revenue and Sales"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="15dp"
                            android:background="#2B000000" />

                        <com.github.mikephil.charting.charts.BarChart
                            android:id="@+id/chart_bar_revenue"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="30dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <!--best seller-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="26dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="7sp"
                            android:text="Best Selling Products"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:fontFamily="@font/roboto_medium"
                            android:text="View All"
                            android:textColor="@color/nav_selected"
                            android:textSize="14sp" />

                    </RelativeLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_best_selling_product"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        tools:itemCount="1"
                        tools:listitem="@layout/item_best_selling_product" />
                </LinearLayout>
                <!--recent orders-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="26dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="26dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="7sp"
                            android:text="Recent Orders"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:fontFamily="@font/roboto_medium"
                            android:text="All Orders"
                            android:textColor="@color/nav_selected"
                            android:textSize="14sp" />

                    </RelativeLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_recent_orders"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        tools:itemCount="5"
                        tools:listitem="@layout/item_recent_orders_home" />
                </LinearLayout>
                <!--new customer-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="30dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="26dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="7sp"
                            android:text="New Customers"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:fontFamily="@font/roboto_medium"
                            android:text="View All"
                            android:textColor="@color/nav_selected"
                            android:textSize="14sp" />

                    </RelativeLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_new_customers"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        tools:itemCount="5"
                        tools:listitem="@layout/item_new_customers_home" />
                </LinearLayout>
                <!--top search term-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="26dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="7sp"
                            android:text="Top Search Term"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:fontFamily="@font/roboto_medium"
                            android:text="View All"
                            android:textColor="@color/nav_selected"
                            android:textSize="14sp" />

                    </RelativeLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_top_search"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        tools:itemCount="1"
                        tools:listitem="@layout/item_last_search_home" />
                </LinearLayout>
                <!--newly added vendors-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="26dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="7sp"
                            android:text="Newly Added Vendors"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:id="@+id/new_vendor_view_all"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:fontFamily="@font/roboto_medium"
                            android:text="View All"
                            android:textColor="@color/nav_selected"
                            android:textSize="14sp" />

                    </RelativeLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_new_vendor"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        tools:itemCount="1"
                        tools:listitem="@layout/item_new_vendor_home" />
                </LinearLayout>
                <!--influencer-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="26dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="7sp"
                            android:text="Influencers"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:id="@+id/influencer_view_all"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:fontFamily="@font/roboto_medium"
                            android:text="View All"
                            android:textColor="@color/nav_selected"
                            android:textSize="14sp" />

                    </RelativeLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_influencer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        tools:itemCount="1"
                        tools:listitem="@layout/item_new_vendor_home" />
                </LinearLayout>
                <!--last search term-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="26dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="7sp"
                            android:text="Last Search Term"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:fontFamily="@font/roboto_medium"
                            android:text="View All"
                            android:textColor="@color/nav_selected"
                            android:textSize="14sp" />

                    </RelativeLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_last_search"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        tools:itemCount="1"
                        tools:listitem="@layout/item_last_search_home" />
                </LinearLayout>
                <!--best seller 2-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="26dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="7sp"
                            android:text="Best Selling Products"
                            android:textColor="@color/black"
                            android:textSize="16sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:fontFamily="@font/roboto_medium"
                            android:text="View All"
                            android:textColor="@color/nav_selected"
                            android:textSize="14sp" />

                    </RelativeLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_best_selling_product_2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        tools:itemCount="1"
                        tools:listitem="@layout/item_best_selling_2_product" />
                </LinearLayout>
                <!--conversion rate-->

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="352dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="29dp"
                    android:layout_marginEnd="16dp"
                    android:elevation="2dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:layout_marginStart="14dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:lineSpacingExtra="6sp"
                            android:text="Conversion Rate"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="15dp"
                            android:background="#2B000000" />

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/lineChartConversion"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="5dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="5dp"
                            android:layout_marginBottom="30dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>