<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    android:background="#a3a3a3"
    android:id="@+id/lyt_address">
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="20dp"
        app:cardCornerRadius="28dp">
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="26dp"
                    android:layout_marginStart="18dp"
                    android:layout_marginEnd="18dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_bold"
                        android:text="Shipping Address"
                        android:textColor="#424546"
                        android:textSize="16sp" />

                    <ImageView
                        android:id="@+id/img_close_date"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginStart="27dp"
                        android:src="@drawable/close_filter" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="15dp"
                    android:background="#40008F96" />
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Address Title *"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:id="@+id/card_address_title"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    app:cardCornerRadius="15dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_address_title"
                            android:textColor="#424546"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Home/Apartment"
                            android:textColorHint="#C49C9C9C"
                            android:textSize="12sp" />

                        <ImageView
                            app:tint="#424546"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/arrow_down" />
                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>
                <TextView
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="11dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="First Name *"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    app:cardCornerRadius="15dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_first_name"
                        android:background="@android:color/transparent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="First Name"
                        android:textColor="#424546"
                        android:textColorHint="#C49C9C9C"
                        android:textSize="12sp" />
                </com.google.android.material.card.MaterialCardView>
                <TextView
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="11dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Last Name *"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    app:cardCornerRadius="15dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_last_name"
                        android:background="@android:color/transparent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Last Name"
                        android:textColor="#424546"
                        android:textColorHint="#C49C9C9C"
                        android:textSize="12sp" />
                </com.google.android.material.card.MaterialCardView>
                <TextView
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Email *"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    app:cardCornerRadius="15dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:background="@android:color/transparent"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Email"
                        android:textColor="#424546"
                        android:textColorHint="#C49C9C9C"
                        android:textSize="14dp" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Phone *"
                    android:textColor="#424546"
                    android:textSize="14sp" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="53dp"
                        app:cardCornerRadius="15dp"
                        android:layout_marginStart="2.5dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginEnd="1dp"
                        android:layout_marginBottom="1dp"
                        app:strokeColor="#424546"
                        app:strokeWidth="1dp">

                        <EditText
                            android:maxLength="8"
                            android:inputType="phone"
                            android:id="@+id/edit_phone"
                            android:background="@android:color/transparent"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="5dp"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Phone"
                            android:textColorHint="#C49C9C9C"
                            android:textColor="#424546"
                            android:textSize="12sp" />
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>
                <TextView
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Alternate Phone"
                    android:textColor="#424546"
                    android:textSize="14sp" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">
                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="53dp"
                        app:cardCornerRadius="15dp"
                        android:layout_marginStart="2.5dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginEnd="1dp"
                        android:layout_marginBottom="1dp"
                        app:strokeColor="#424546"
                        app:strokeWidth="1dp">

                        <EditText
                            android:maxLength="8"
                            android:inputType="phone"
                            android:id="@+id/edit_alter_phone"
                            android:background="@android:color/transparent"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="5dp"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Phone"
                            android:textColorHint="#C49C9C9C"
                            android:textColor="#424546"
                            android:textSize="12sp" />
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Governorate *"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:id="@+id/card_governorate"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    app:cardCornerRadius="15dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:id="@+id/lyt_governorate"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_governorate"
                            android:textColor="#424546"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Select Governorate"
                            android:textColorHint="#C49C9C9C"
                            android:textSize="12sp" />

                        <ImageView
                            app:tint="#424546"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/arrow_down" />
                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Area *"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:id="@+id/card_area"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    app:cardCornerRadius="15dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:id="@+id/lyt_area"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_area"
                            android:textColor="#424546"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Select Area"
                            android:textColorHint="#C49C9C9C"
                            android:textSize="12sp" />

                        <ImageView
                            app:tint="#424546"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/arrow_down" />
                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Block *"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    app:cardCornerRadius="15dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_block"
                        android:background="@android:color/transparent"
                        android:textColor="#424546"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Block"
                        android:textColorHint="#C49C9C9C"
                        android:textSize="12sp" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Street Name *"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    app:cardCornerRadius="15dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_street"
                        android:background="@android:color/transparent"
                        android:textColor="#424546"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Street name"
                        android:textColorHint="#C49C9C9C"
                        android:textSize="12sp" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Building *"
                    android:textColor="#424546"
                    android:textSize="12sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    app:cardCornerRadius="15dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_building"
                        android:background="@android:color/transparent"
                        android:textColor="#424546"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Building"
                        android:textColorHint="#C49C9C9C"
                        android:textSize="12sp" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_marginStart="18dp"
                    android:layout_marginEnd="18dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Lane"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    app:cardCornerRadius="15dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_apartment"
                        android:background="@android:color/transparent"
                        android:textColor="#424546"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Lane"
                        android:textColorHint="#C49C9C9C"
                        android:textSize="12sp" />
                </com.google.android.material.card.MaterialCardView>
                <TextView
                    android:layout_marginStart="18dp"
                    android:layout_marginEnd="18dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Floor"
                    android:textColor="#424546"
                    android:textSize="14sp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="53dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginTop="5dp"
                    app:cardCornerRadius="15dp"
                    app:strokeColor="#424546"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_floor"
                        android:background="@android:color/transparent"
                        android:textColor="#424546"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Floor"
                        android:textColorHint="#C49C9C9C"
                        android:textSize="12sp" />
                </com.google.android.material.card.MaterialCardView>


                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="47dp"
                    app:cardCornerRadius="8dp"
                    android:id="@+id/card_add_address"
                    android:layout_marginStart="25dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="25dp"
                    android:layout_marginBottom="15dp"
                    app:cardBackgroundColor="@color/main_color">

                    <TextView
                        android:id="@+id/txt_button"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Add address"
                        android:textColor="@color/white"
                        android:textSize="18sp" />
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </com.google.android.material.card.MaterialCardView>
</FrameLayout>