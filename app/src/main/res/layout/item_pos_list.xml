<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="127dp"
    android:layout_marginStart="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginBottom="10dp"
    android:elevation="1dp"
    app:cardElevation="1dp"
    app:cardCornerRadius="12dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/dummy_pos" />
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#52000000"/>
        <LinearLayout
            android:layout_centerInParent="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/txt_title"
                android:layout_marginStart="18dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="POS RX"
                android:textColor="@color/white"
                android:fontFamily="@font/roboto_bold"
                android:textSize="20sp"
                android:layout_weight="1"/>

            <ImageView
                android:layout_width="29dp"
                android:layout_height="29dp"
                android:layout_marginEnd="11dp"
                android:src="@drawable/pos_arrow"/>
        </LinearLayout>
    </RelativeLayout>

</com.google.android.material.card.MaterialCardView>