<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:background="@color/white"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_back"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/pos_back" />

            <TextView
                android:id="@+id/txt_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="25dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_bold"
                android:text="All  Products"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <RelativeLayout
                android:layout_width="33dp"
                android:layout_height="27dp"
                android:layout_marginEnd="12dp">

                <ImageView
                    android:id="@+id/img_cart"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/header_cart" />

                <RelativeLayout
                    android:id="@+id/rlt_cart_count"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/cart_count_bg" />

                    <TextView
                        android:id="@+id/txt_cart_count"
                        android:layout_centerInParent="true"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center"
                        android:maxLength="2"
                        android:text="0"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </RelativeLayout>
            </RelativeLayout>

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="20dp"
                android:src="@drawable/home_notif" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nested_scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none"
            android:fillViewport="true">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    app:cardCornerRadius="16dp"
                    app:strokeWidth="0dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginStart="13dp"
                    android:layout_marginEnd="13dp"
                    app:cardBackgroundColor="#F8F8F8"
                    android:layout_height="43dp">

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/search_icon_new"/>


                        <EditText
                            android:fontFamily="@font/roboto_regular"
                            android:layout_width="match_parent"
                            android:textSize="16dp"
                            android:textColorHint="#AEAEAE"
                            android:hint="Search"
                            android:layout_marginStart="10dp"
                            android:layout_height="match_parent"
                            android:background="@android:color/transparent"
                            android:id="@+id/edit_search"/>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_add_product"
                    android:layout_width="match_parent"
                    android:layout_height="38dp"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    app:strokeWidth="0dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="20dp"
                    app:cardBackgroundColor="#F7EBF0"
                    app:cardCornerRadius="12dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:src="@drawable/new_plus_icon" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:textSize="16sp"
                            android:text="Add Product"
                            android:layout_marginStart="10dp"
                            android:layout_gravity="center"
                            android:textColor="@color/main_color"
                            android:fontFamily="@font/roboto_bold"
                            android:gravity="center"
                            android:layout_height="wrap_content"/>
                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>
                <TextView
                    android:visibility="gone"
                    android:id="@+id/txt_no_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_bold"
                    android:text="No Data Found"
                    android:layout_gravity="center"
                    android:layout_marginTop="50dp"
                    android:gravity="center"
                    android:layout_centerInParent="true"
                    android:textColor="@color/black"
                    android:textSize="14sp" />
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_product"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="13dp"
                    android:layout_marginTop="1dp"
                    android:layout_marginEnd="13dp"
                    android:layout_marginBottom="13dp"
                    tools:itemCount="5"
                    tools:listitem="@layout/item_all_product" />
                <RelativeLayout
                    android:id="@+id/progressBar_small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="#50F9FEFF"
                    android:layout_gravity="center"
                    android:visibility="gone">

                    <ProgressBar
                        style="?android:attr/progressBarStyle"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:indeterminateDrawable="@drawable/rotating_icon"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </RelativeLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </LinearLayout>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>