<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginBottom="10dp"
    app:cardBackgroundColor="#F8F5F5"
    app:cardCornerRadius="5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="10dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="CUST10059"
                    android:textColor="@color/nav_selected"
                    android:textSize="11sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/roboto_bold"
                    android:singleLine="true"
                    android:text="Hashim Mohammed"
                    android:textColor="@color/black"
                    android:textSize="13sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/roboto_regular"
                    android:singleLine="true"
                    android:text="+965 66805546"
                    android:textColor="@color/black"
                    android:textSize="13sp" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/roboto_regular"
                    android:singleLine="true"
                    android:text="<EMAIL>"
                    android:textColor="@color/black"
                    android:textSize="12sp" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/roboto_regular"
                    android:singleLine="true"
                    android:text="Hawalli,Salmiya,Al Dehak,12,1,"
                    android:textColor="@color/black"
                    android:textSize="11sp" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center|end"
                android:layout_marginEnd="15dp"
                android:gravity="center|end"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_marginEnd="2dp"
                    android:id="@+id/img_edit"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:src="@drawable/icon_edit" />
                </LinearLayout>
                <LinearLayout
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_marginEnd="2dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:id="@+id/img_delete"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content">
                <ImageView

                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:src="@drawable/icon_delete" />
                </LinearLayout>
                <LinearLayout
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_marginEnd="2dp"
                    android:id="@+id/img_ban"
                    android:layout_width="40dp"
                    android:layout_height="wrap_content">
                <ImageView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:src="@drawable/home_cust_user" />
                </LinearLayout>
            </LinearLayout>


        </LinearLayout>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            android:elevation="1dp"
            app:cardElevation="1dp"
            app:cardBackgroundColor="@color/white"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp">

            <LinearLayout
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="35dp"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_centerInParent="true"
                        android:layout_alignParentStart="true"
                        android:layout_marginStart="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:text="Created by"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                    <TextView
                        android:layout_centerInParent="true"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:singleLine="true"
                        android:text=":"
                        android:textColor="@color/black"
                        android:textSize="15sp" />
                    <TextView
                        android:layout_centerInParent="true"
                        android:layout_alignParentEnd="true"
                        android:textAlignment="center"
                        android:layout_weight="1"
                        android:layout_marginEnd="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:text="Admin"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="35dp"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_alignParentStart="true"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:text="Created From"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                    <TextView
                        android:layout_centerInParent="true"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:singleLine="true"
                        android:text=":"
                        android:textColor="@color/black"
                        android:textSize="15sp" />
                    <TextView
                        android:textAlignment="center"
                        android:layout_alignParentEnd="true"
                        android:layout_centerInParent="true"
                        android:layout_marginEnd="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:text="Website"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="35dp"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_centerInParent="true"
                        android:layout_alignParentStart="true"
                        android:layout_marginStart="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:text="Spend amount"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                    <TextView

                        android:layout_centerInParent="true"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:singleLine="true"
                        android:text=":"
                        android:textColor="@color/black"
                        android:textSize="15sp" />
                    <TextView
                        android:layout_centerInParent="true"
                        android:textAlignment="center"
                        android:layout_alignParentEnd="true"
                        android:layout_marginEnd="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:text="0.000"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="35dp"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_centerInParent="true"
                        android:layout_alignParentStart="true"
                        android:layout_marginStart="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:text="Cart amount"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                    <TextView

                        android:layout_centerInParent="true"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_bold"
                        android:singleLine="true"
                        android:text=":"
                        android:textColor="@color/black"
                        android:textSize="15sp" />
                    <TextView
                        android:layout_centerInParent="true"
                        android:textAlignment="center"
                        android:layout_alignParentEnd="true"
                        android:layout_marginEnd="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_regular"
                        android:singleLine="true"
                        android:text="0.000"
                        android:textColor="@color/black"
                        android:textSize="12sp" />
                </RelativeLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_delivery_change"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/white"
            android:layout_marginTop="15dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="1dp"
            android:elevation="1dp"
            app:cardElevation="1dp"
            app:cardCornerRadius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_d_c"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:text="Select Type"
                        android:textColor="#B4ABAB"
                        android:textSize="12sp" />

                    <ImageView
                        android:id="@+id/img_d_c"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="6dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#222222" />
                </LinearLayout>
                <LinearLayout
                    android:visibility="gone"
                    android:id="@+id/card_d_c_hide"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#40000000"/>
                    <androidx.recyclerview.widget.RecyclerView
                        android:layout_width="match_parent"
                        android:layout_margin="10dp"
                        android:layout_height="wrap_content"
                        android:id="@+id/recycler_d_c"/>
                </LinearLayout>
            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_payment_change"
            app:cardCornerRadius="8dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/white"
            android:layout_marginTop="15dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="1dp"
            android:elevation="1dp"
            app:cardElevation="1dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_payment_change"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:text="Select Segment"
                        android:textColor="#B4ABAB"
                        android:textSize="12sp" />

                    <ImageView
                        android:id="@+id/img_p_c"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="6dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#222222" />
                </LinearLayout>
                <LinearLayout
                    android:visibility="gone"
                    android:id="@+id/card_p_c_hide"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#40000000"/>
                    <androidx.recyclerview.widget.RecyclerView
                        android:layout_width="match_parent"
                        android:layout_margin="10dp"
                        android:layout_height="wrap_content"
                        android:id="@+id/recycler_p_c"/>
                </LinearLayout>
            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>