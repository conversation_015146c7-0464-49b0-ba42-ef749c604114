<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:background="@color/white"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_back"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/pos_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="25dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_bold"
                android:text="All Type"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <RelativeLayout
                android:layout_width="33dp"
                android:layout_height="27dp"
                android:layout_marginEnd="12dp">

                <ImageView
                    android:id="@+id/img_cart"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/header_cart" />

                <RelativeLayout
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/cart_count_bg" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center"
                        android:maxLength="2"
                        android:text="2"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </RelativeLayout>
            </RelativeLayout>

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="20dp"
                android:src="@drawable/home_notif" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="16dp"
                android:orientation="vertical">


                <androidx.recyclerview.widget.RecyclerView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:itemCount="5"
                    android:layout_marginBottom="100dp"
                    tools:listitem="@layout/item_segment_list"
                    android:id="@+id/recycler_list"/>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
    <LinearLayout
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:background="@android:color/transparent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="50dp"
        android:orientation="horizontal">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_add"
            android:layout_width="match_parent"
            android:layout_height="54dp"
            android:layout_gravity="center"
            android:layout_marginStart="22dp"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="22dp"
            android:layout_marginBottom="22dp"
            app:cardBackgroundColor="@color/main_color"
            app:cardCornerRadius="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/roboto_medium"
                android:text="Add Type"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>
    <include android:id="@+id/page_segment_add"
        layout="@layout/sheet_add_type"/>
    <include android:id="@+id/page_segment_edit"
        layout="@layout/sheet_edit_type"/>
</androidx.constraintlayout.widget.ConstraintLayout>