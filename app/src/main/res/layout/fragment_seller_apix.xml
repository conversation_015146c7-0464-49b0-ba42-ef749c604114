<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:background="@color/white"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_back"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/pos_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="25dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_bold"

                android:textColor="@color/black"
                android:textSize="14sp" />

            <RelativeLayout
                android:layout_width="33dp"
                android:layout_height="27dp"
                android:layout_marginEnd="12dp">

                <ImageView
                    android:id="@+id/img_cart"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/header_cart" />

                <RelativeLayout
                    android:id="@+id/rlt_cart_count"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/cart_count_bg" />

                    <TextView
                        android:id="@+id/txt_cart_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center"
                        android:maxLength="2"
                        android:text="0"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </RelativeLayout>
            </RelativeLayout>

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="20dp"
                android:src="@drawable/home_notif" />
        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nested_scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="16dp"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="1dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="9dp"
                    app:cardElevation="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="21dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Margin Calculator"
                            android:textColor="#424546"
                            android:textSize="16sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="10dp"
                            android:background="#40000000" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="25dp"
                            android:layout_marginEnd="20dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <EditText
                                android:id="@+id/edit_selling_price"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:hint="Selling Price"
                                android:singleLine="true"
                                android:textColorHint="#B4ABAB"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginEnd="20dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <EditText
                                android:id="@+id/edit_purchase_price"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:hint="Purchase Price"
                                android:singleLine="true"
                                android:textColorHint="#B4ABAB"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginEnd="20dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <TextView
                                android:id="@+id/txt_margin"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:fontFamily="@font/roboto_medium"
                                android:gravity="center_vertical"
                                android:singleLine="true"
                                android:text="Margin"
                                android:textColor="@color/main_color"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_calculate"
                            android:layout_width="80dp"
                            android:layout_height="30dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="25dp"
                            android:elevation="1dp"
                            app:cardBackgroundColor="#A61C5C"
                            app:cardCornerRadius="9dp"
                            app:cardElevation="1dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:fontFamily="@font/roboto_medium"
                                android:gravity="center"
                                android:text="Calculate"
                                android:textColor="@color/white"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="1dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="1dp"
                    android:layout_marginBottom="1dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="9dp"
                    app:cardElevation="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="21dp"
                            android:layout_marginTop="11dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Conditions"
                            android:textColor="#424546"
                            android:textSize="16sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="10dp"
                            android:background="#40000000" />

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="25dp"
                            android:layout_marginEnd="20dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="5dp"
                            app:strokeColor="#7D7C7C"
                            app:strokeWidth="1dp">

                            <EditText
                                android:id="@+id/edit_commisions"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:background="@android:color/transparent"
                                android:hint="Commission Value %"
                                android:singleLine="true"
                                android:textColorHint="#B4ABAB"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:id="@+id/radio_all"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="50dp"
                                app:strokeColor="@color/main_color"
                                app:strokeWidth="1dp">

                                <com.google.android.material.card.MaterialCardView
                                    android:layout_width="12dp"
                                    android:id="@+id/card_radio_all"
                                    android:layout_height="12dp"
                                    android:layout_gravity="center"
                                    app:cardBackgroundColor="@color/main_color"
                                    app:cardCornerRadius="50dp" />
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:fontFamily="@font/roboto_regular"
                                android:text="All"
                                android:textColor="#424546"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/radio_pharma"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="50dp"
                                app:strokeColor="@color/main_color"
                                app:strokeWidth="1dp">

                                <com.google.android.material.card.MaterialCardView
                                    android:layout_width="12dp"
                                    android:id="@+id/card_radio_pharma"
                                    android:layout_height="12dp"
                                    android:layout_gravity="center"
                                    android:visibility="gone"
                                    app:cardBackgroundColor="@color/main_color"
                                    app:cardCornerRadius="50dp" />
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Pharmaceutical"
                                android:textColor="#424546"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/radio_non_pharma"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="50dp"
                                app:strokeColor="@color/main_color"
                                app:strokeWidth="1dp">

                                <com.google.android.material.card.MaterialCardView
                                    android:layout_width="12dp"
                                    android:id="@+id/card_radio_non_pharma"
                                    android:layout_height="12dp"
                                    android:layout_gravity="center"
                                    android:visibility="gone"
                                    app:cardBackgroundColor="@color/main_color"
                                    app:cardCornerRadius="50dp" />
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Non pharma"
                                android:textColor="#424546"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/radio_nil"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="50dp"
                                app:strokeColor="@color/main_color"
                                app:strokeWidth="1dp">

                                <com.google.android.material.card.MaterialCardView
                                    android:layout_width="12dp"
                                    android:id="@+id/card_radio_nil"
                                    android:layout_height="12dp"
                                    android:layout_gravity="center"
                                    android:visibility="gone"
                                    app:cardBackgroundColor="@color/main_color"
                                    app:cardCornerRadius="50dp" />
                            </com.google.android.material.card.MaterialCardView>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Nil"
                                android:textColor="#424546"
                                android:textSize="12sp" />
                        </LinearLayout>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_submit_condition"
                            android:layout_width="80dp"
                            android:layout_height="30dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="25dp"
                            android:elevation="1dp"
                            app:cardBackgroundColor="#A61C5C"
                            app:cardCornerRadius="9dp"
                            app:cardElevation="1dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:fontFamily="@font/roboto_medium"
                                android:gravity="center"
                                android:text="Submit"
                                android:textColor="@color/white"
                                android:textSize="12sp" />
                        </com.google.android.material.card.MaterialCardView>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    app:cardCornerRadius="16dp"
                    app:strokeWidth="0dp"
                    android:layout_marginTop="22dp"
                    android:layout_marginStart="1dp"
                    android:layout_marginEnd="1dp"
                    app:cardBackgroundColor="#F8F8F8"
                    android:layout_height="43dp">

                    <LinearLayout
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/search_icon_new"/>


                        <EditText
                            android:fontFamily="@font/roboto_regular"
                            android:layout_width="match_parent"
                            android:textSize="16dp"
                            android:textColorHint="#AEAEAE"
                            android:hint="Search"
                            android:layout_marginStart="10dp"
                            android:layout_height="match_parent"
                            android:background="@android:color/transparent"
                            android:id="@+id/edit_search"/>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_list"
                    android:layout_marginTop="20dp"
                    tools:itemCount="5"
                    android:layout_marginBottom="1dp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
                <RelativeLayout
                    android:id="@+id/progressBar_small"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="#50F9FEFF"
                    android:layout_gravity="center"
                    android:visibility="gone">

                    <ProgressBar
                        style="?android:attr/progressBarStyle"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:indeterminateDrawable="@drawable/rotating_icon"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </RelativeLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>