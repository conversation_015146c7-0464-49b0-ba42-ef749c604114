<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginBottom="21dp"
    app:cardBackgroundColor="#FEFEFE"
    app:cardCornerRadius="10dp"
    app:cardElevation="5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="100dp"
                android:layout_height="85dp"
                app:cardBackgroundColor="#F5F6FA"
                app:cardCornerRadius="10dp"
                app:cardElevation="1dp">

                <ImageView
                    android:id="@+id/img_cart"
                    android:layout_width="77dp"
                    android:layout_height="74dp"
                    android:layout_gravity="center" />
            </com.google.android.material.card.MaterialCardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txt_product_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_medium"
                    android:singleLine="true"
                    android:textColor="#1D1E20"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/txt_prize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="11dp"
                    android:fontFamily="@font/roboto_regular"
                    android:textColor="#8F959E"
                    android:textSize="13sp" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="14dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_decrease_count"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginStart="1dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="1dp"
                        app:cardBackgroundColor="@color/white"
                        app:strokeColor="#DEDEDE"
                        app:strokeWidth="1dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:src="@drawable/arrow_down"

                            app:tint="#8F959E" />
                    </com.google.android.material.card.MaterialCardView>

                    <TextView
                        android:id="@+id/txt_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/roboto_medium"
                        android:singleLine="true"
                        android:text="1"
                        android:textColor="#1D1E20"
                        android:textSize="13sp" />

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_increase_count"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginEnd="1dp"
                        android:layout_marginBottom="1dp"
                        app:cardBackgroundColor="@color/white"
                        app:strokeColor="#DEDEDE"
                        app:strokeWidth="1dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            android:src="@drawable/arrow_down"
android:rotation="180"
                            app:tint="#8F959E" />
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/img_delete"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="5dp"
                android:gravity="center">

                <ImageView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_gravity="center"
                    android:src="@drawable/icon_delete_cart" />
            </LinearLayout>

        </LinearLayout>


    </LinearLayout>
</com.google.android.material.card.MaterialCardView>