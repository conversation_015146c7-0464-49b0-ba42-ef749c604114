<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="65dp"
                android:layout_gravity="center_vertical"
                android:background="@color/white"
                android:elevation="5dp"
                android:gravity="center_vertical"

                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/img_back"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/pos_back" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Order Summary"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <RelativeLayout
                    android:layout_width="33dp"
                    android:layout_height="27dp"
                    android:layout_marginEnd="12dp">

                    <ImageView
                        android:layout_width="26dp"
                        android:layout_height="26dp"
                        android:src="@drawable/header_cart" />

                    <RelativeLayout
                        android:id="@+id/rlt_cart_count"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_alignParentEnd="true">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:src="@drawable/cart_count_bg" />

                        <TextView
                            android:id="@+id/txt_cart_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_gravity="center"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center"
                            android:maxLength="2"
                            android:text="2"
                            android:textColor="@color/white"
                            android:textSize="10sp" />
                    </RelativeLayout>
                </RelativeLayout>

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/home_notif" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="16dp"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_rx_cart"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:itemCount="1"
                    tools:listitem="@layout/pos_shop_summery_cart_item" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:id="@+id/card_lead_name"
                    android:layout_height="40dp"
                    android:layout_marginStart="1dp"
                    android:layout_marginBottom="1dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_lead_name"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Select Lead"
                            android:textColorHint="#B4ABAB"
                            android:textSize="12sp" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="6dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#222222" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:id="@+id/card_delivery_staff"
                    android:layout_height="40dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginStart="1dp"
                    android:layout_marginBottom="1dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_delivery_staff"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Select Delivery Staff"
                            android:textColorHint="#B4ABAB"
                            android:textSize="12sp" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="6dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#222222" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:id="@+id/card_search_booklet"
                    android:layout_height="40dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginStart="1dp"
                    android:layout_marginBottom="1dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <EditText
                        android:background="@android:color/transparent"
                        android:id="@+id/edit_booklet"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Search Booklet No"
                        android:textColorHint="#B4ABAB"
                        android:textSize="12sp" />
                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:id="@+id/card_search_prescription"
                    android:layout_height="40dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginStart="1dp"
                    android:layout_marginBottom="1dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <EditText
                        android:background="@android:color/transparent"
                        android:id="@+id/edit_prescription"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Search Prescriptions Id"
                        android:textColorHint="#B4ABAB"
                        android:textSize="12sp" />
                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:id="@+id/card_influencer"
                    android:layout_height="40dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginStart="1dp"
                    android:layout_marginBottom="1dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_influencer"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:hint="Select Influencer"
                            android:textColorHint="#B4ABAB"
                            android:textSize="12sp" />

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="6dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#222222" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Customer Info"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout

                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/txt_customer"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="15dp"
                            android:fontFamily="@font/roboto_regular"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/txt_email"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="3dp"
                            android:fontFamily="@font/roboto_regular"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/txt_phone"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="3dp"
                            android:fontFamily="@font/roboto_regular"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/txt_address"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="3dp"
                            android:fontFamily="@font/roboto_regular"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/txt_address_1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginTop="3dp"
                            android:fontFamily="@font/roboto_regular"
                            android:textColor="@color/black"
                            android:textSize="14sp" />


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:orientation="horizontal">

                            <CheckBox
                                android:id="@+id/check_deliver"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text=" Deliver to Apix"
                                android:textColor="@color/black"
                                android:textSize="14sp" />

                            <CheckBox
                                android:id="@+id/check_apix"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Pickup by Apix"
                                android:textColor="@color/black"
                                android:textSize="14sp" />
                        </LinearLayout>

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Additional Notes"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="80dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_additional_notes"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@android:color/transparent"
                        android:fontFamily="@font/roboto_regular"
                        android:hint="Additional Notes"
                        android:padding="10dp"
                        android:textSize="14sp" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Internal Notes"
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="80dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <EditText
                        android:id="@+id/edit_internal_notes"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@android:color/transparent"
                        android:fontFamily="@font/roboto_regular"
                        android:hint="Internal Notes"
                        android:padding="10dp"
                        android:textSize="14sp" />
                </com.google.android.material.card.MaterialCardView>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Payment method "
                    android:textColor="@color/black"
                    android:textSize="16dp" />

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_quick_pay"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:layout_marginTop="21dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_reminder"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:cardBackgroundColor="#FEFEFE"
                            app:cardCornerRadius="50dp"
                            app:strokeColor="#E3E7EC"
                            app:strokeWidth="1dp">

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/card_quick_pay_selected"
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                android:layout_gravity="center"
                                android:visibility="visible"
                                app:cardBackgroundColor="@color/main_color"
                                app:cardCornerRadius="50dp" />

                        </com.google.android.material.card.MaterialCardView>


                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:lineHeight="20dp"
                            android:text="Quick Pay"
                            android:textColor="@color/black"
                            android:textSize="14dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_go_tap"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:layout_marginTop="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:cardBackgroundColor="#FEFEFE"
                            app:cardCornerRadius="50dp"
                            app:strokeColor="#E3E7EC"
                            app:strokeWidth="1dp">

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/card_go_tap_selected"
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                android:layout_gravity="center"
                                android:visibility="gone"
                                app:cardBackgroundColor="@color/main_color"
                                app:cardCornerRadius="50dp" />

                        </com.google.android.material.card.MaterialCardView>


                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:lineHeight="20dp"
                            android:text="Go Tap"
                            android:textColor="@color/black"
                            android:textSize="14dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_cod"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="50dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="8dp"
                    app:strokeColor="#7D7C7C"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:cardBackgroundColor="#FEFEFE"
                            app:cardCornerRadius="50dp"
                            app:strokeColor="#E3E7EC"
                            app:strokeWidth="1dp">

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/card_cod_selected"
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                android:layout_gravity="center"
                                android:visibility="gone"
                                app:cardBackgroundColor="@color/main_color"
                                app:cardCornerRadius="50dp" />

                        </com.google.android.material.card.MaterialCardView>


                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_medium"
                            android:lineHeight="20dp"
                            android:text="Confirm with COD"
                            android:textColor="@color/black"
                            android:textSize="14dp" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="#9EF5F5F5"
                    android:elevation="1dp"
                    app:cardCornerRadius="8dp"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="10dp"
                        android:visibility="visible">


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="21dp"
                            android:layout_marginEnd="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Sub Total"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/txt_sub_total"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="KWD  0.000"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Item Count"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/txt_item_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Tax"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/txt_tax"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="KWD  0.000"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Shipping"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/txt_shipping"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="KWD  0.000"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_medium"
                                android:text="Discount"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/txt_discount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_medium"
                                android:text="KWD  0.000"
                                android:textColor="#A0A0A0"
                                android:textSize="14sp" />

                        </LinearLayout>


                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.8dp"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="20dp"
                            android:background="#80000000" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="20dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginEnd="20dp"
                            android:layout_marginBottom="20dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:fontFamily="@font/roboto_bold"
                                android:text="Total"
                                android:textColor="@color/black"
                                android:textSize="19sp" />

                            <TextView
                                android:id="@+id/txt_total"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_bold"
                                android:text="KWD  0.000"
                                android:textColor="@color/black"
                                android:textSize="19sp" />

                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_proceed"
                    android:layout_width="match_parent"
                    android:layout_height="54dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="40dp"
                    android:layout_marginBottom="22dp"
                    app:cardBackgroundColor="@color/main_color"
                    app:cardCornerRadius="12dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_medium"
                        android:text="Proceed"
                        android:textColor="@color/white"
                        android:textSize="14sp" />
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>



    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>