<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FAFAFE"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nested_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/img_menu"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/menu"
                android:visibility="gone" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="40dp"
                android:fontFamily="@font/roboto_bold"
                android:gravity="center"
                android:text="@string/reports"
                android:textColor="@color/black"
                android:textSize="16dp" />


            <TextView
                android:id="@+id/txt_no_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:layout_marginTop="50dp"
                android:fontFamily="@font/roboto_bold"
                android:gravity="center"
                android:text="No Data Found"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:visibility="gone" />

            <HorizontalScrollView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fillViewport="true"
                android:scrollbars="none">

                <LinearLayout
                    android:gravity="start"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="45dp"
                        android:layout_marginTop="50dp"
                        android:background="#1b1b28"
                        android:orientation="horizontal">

                        <!-- Order ID -->
                        <TextView
                            android:layout_width="120dp"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="OrderID"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:singleLine="true"
                            android:ellipsize="end"
                            android:maxLines="1" />

                        <!-- Date -->
                        <TextView
                            android:layout_width="100dp"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Date"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:singleLine="true"
                            android:ellipsize="end"
                            android:maxLines="1" />

                        <!-- Item Name -->
                        <TextView
                            android:layout_width="250dp"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Item Name"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:singleLine="true"
                            android:ellipsize="end"
                            android:maxLines="1" />

                        <!-- Selling Price -->
                        <TextView
                            android:layout_width="180dp"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Selling Price after all discounts"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:singleLine="true"
                            android:ellipsize="end"
                            android:maxLines="1" />

                        <!-- Doctor Appreciation -->
                        <TextView
                            android:layout_width="220dp"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Doctor Appreciation Amount (KD)"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:singleLine="true"
                            android:ellipsize="end"
                            android:maxLines="1" />

                        <!-- Pharmaceutical -->
                        <TextView
                            android:layout_width="140dp"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Pharmaceutical"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:singleLine="true"
                            android:ellipsize="end"
                            android:maxLines="1" />

                        <!-- Cleared -->
                        <TextView
                            android:layout_width="100dp"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical"
                            android:fontFamily="@font/roboto_bold"
                            android:text="Cleared"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            android:singleLine="true"
                            android:ellipsize="end"
                            android:maxLines="1" />
                    </LinearLayout>


                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_pres"
                        android:layout_width="match_parent"

                        android:layout_height="match_parent"
                        tools:itemCount="3"
                        tools:listitem="@layout/item_report" />
                </LinearLayout>
            </HorizontalScrollView>

            <RelativeLayout
                android:id="@+id/progressBar_small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="#50F9FEFF"
                android:visibility="gone">

                <ProgressBar
                    style="?android:attr/progressBarStyle"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    android:indeterminateDrawable="@drawable/rotating_icon"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </RelativeLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
