<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="1dp"
    android:layout_marginBottom="5dp"
    android:orientation="vertical"
    android:background="?android:attr/selectableItemBackground">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        app:cardCornerRadius="5dp"
        app:cardBackgroundColor="@color/white"
        app:strokeWidth="1dp"
        app:strokeColor="@color/white"
        android:layout_height="35dp">
        <TextView
            android:layout_marginStart="0dp"
            android:layout_marginEnd="10dp"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:id="@+id/tvSkuName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="SKU Name"
            android:fontFamily="@font/roboto_medium"
            android:textSize="14sp"
            android:textColor="@android:color/black" />
    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
