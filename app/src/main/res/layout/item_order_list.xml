<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="1dp"
    android:layout_marginBottom="15dp"
    android:elevation="2dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="10dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="51dp"
                android:layout_height="51dp"
                app:cardCornerRadius="50dp">

                <ImageView
                    android:id="@+id/img_cust"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/dummy_image" />
            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:id="@+id/txt_patient_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_bold"
                android:textColor="@color/black"
                android:textSize="18sp" />



        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="20dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"

                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_regular"
                    android:text="OrderID"
                    android:textColor="#B9B9B9"
                    android:textSize="12sp" />

                <TextView
                    android:layout_gravity="end"
                    android:gravity="end"
                    android:id="@+id/txt_order_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_medium"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"

                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Order Amount"
                    android:textColor="#B9B9B9"
                    android:textSize="12sp" />

                <TextView
                    android:layout_gravity="end"
                    android:gravity="end"
                    android:id="@+id/txt_order_amount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_medium"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"

                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Quantity"
                    android:textColor="#B9B9B9"
                    android:textSize="12sp" />

                <TextView
                    android:layout_gravity="end"
                    android:gravity="end"
                    android:id="@+id/txt_order_quantity"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_medium"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Address"
                    android:textColor="#B9B9B9"
                    android:textSize="12sp" />

                <TextView
                    android:layout_gravity="end"
                    android:gravity="end"
                    android:id="@+id/txt_patient_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_medium"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Mobile Number"
                    android:textColor="#B9B9B9"
                    android:textSize="12sp" />

                <TextView
                    android:layout_gravity="end"
                    android:gravity="end"
                    android:id="@+id/txt_patient_number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_medium"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Pick-up Points"
                    android:textColor="#B9B9B9"
                    android:textSize="12sp" />

                <TextView
                    android:layout_gravity="end"
                    android:gravity="end"
                    android:id="@+id/txt_pick_up_point"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_medium"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/roboto_regular"
                    android:text="Delivery Status"
                    android:textColor="#B9B9B9"
                    android:textSize="12sp" />

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_change_status"
                    android:layout_width="100dp"
                    android:layout_height="wrap_content"
                    android:minHeight="15dp"
                    app:cardBackgroundColor="#f8f5c5"
                    app:cardCornerRadius="16dp"
                    app:strokeWidth="0dp">

                    <LinearLayout

                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:paddingStart="10dp"
                        android:paddingTop="2dp"
                        android:paddingEnd="10dp"
                        android:paddingBottom="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/txt_order_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/roboto_regular"
                                android:text="Pending"
                                android:textColor="#A5A724"
                                android:textSize="9sp" />

                            <ImageView
                                android:layout_width="7dp"
                                android:layout_height="7dp"
                                android:layout_marginStart="2dp"
                                android:layout_marginTop="1dp"
                                android:rotation="270"
                                android:src="@drawable/arrow_brown_new"
                                app:tint="#A5A724" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/lyt_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginTop="5dp"
                                android:layout_marginBottom="5dp"
                                android:background="#20000000" />

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/txt_pending"
                                android:layout_width="match_parent"
                                android:layout_height="15dp"
                                android:layout_marginTop="5dp"
                                android:elevation="1dp"
                                app:cardBackgroundColor="#F5EF9B"
                                app:cardCornerRadius="5dp"
                                app:strokeWidth="0dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/roboto_regular"
                                    android:gravity="center"
                                    android:text="Pending"
                                    android:textColor="#000"
                                    android:textSize="10sp" />
                            </com.google.android.material.card.MaterialCardView>

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/txt_confirmed"
                                android:layout_width="match_parent"
                                android:layout_height="15dp"
                                android:layout_marginTop="5dp"
                                android:elevation="1dp"
                                app:cardBackgroundColor="#F5EF9B"
                                app:cardCornerRadius="5dp"
                                app:strokeWidth="0dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/roboto_regular"
                                    android:gravity="center"
                                    android:text="Confirmed"
                                    android:textColor="#000"
                                    android:textSize="10sp" />
                            </com.google.android.material.card.MaterialCardView>

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/txt_picked_up"
                                android:layout_width="match_parent"
                                android:layout_height="15dp"
                                android:layout_marginTop="5dp"
                                android:elevation="1dp"
                                app:cardBackgroundColor="#F5EF9B"
                                app:cardCornerRadius="5dp"
                                app:strokeWidth="0dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/roboto_regular"
                                    android:text="Picked Up"
                                    android:textColor="#000"
                                    android:textSize="10sp" />
                            </com.google.android.material.card.MaterialCardView>

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/txt_on_the_way"
                                android:layout_width="match_parent"
                                android:layout_height="15dp"
                                android:layout_marginTop="5dp"
                                android:layout_marginBottom="10dp"
                                android:elevation="1dp"
                                app:cardBackgroundColor="#F5EF9B"
                                app:cardCornerRadius="5dp"
                                app:strokeWidth="0dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/roboto_regular"
                                    android:text="On The Way"

                                    android:textColor="#000"
                                    android:textSize="10sp" />
                            </com.google.android.material.card.MaterialCardView>
                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>



            </LinearLayout>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:layout_marginTop="20dp"
                android:id="@+id/card_view"
                app:cardBackgroundColor="@color/doctor_main_color"
                app:cardCornerRadius="12dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="View Details"
                    android:textColor="@color/white"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:textSize="16sp"
                    android:fontFamily="@font/roboto_regular"/>
            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>