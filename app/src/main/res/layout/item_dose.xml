<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="79dp"
    android:layout_height="35dp"
    android:id="@+id/card_dose"
    android:layout_marginStart="1dp"
    android:layout_marginTop="1dp"
    android:layout_marginEnd="10dp"
    app:strokeWidth="1dp"
    app:cardElevation="1dp"
    android:layout_marginBottom="1dp"
    app:cardBackgroundColor="#F8F8F8"
    app:cardCornerRadius="6dp">

    <TextView
        android:id="@+id/txt_dose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_regular"
        android:text="250mg"
        android:layout_gravity="center"
        android:gravity="center"
        android:textColor="#BF000000"
        android:textSize="16sp" />

</com.google.android.material.card.MaterialCardView>