<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FAFAFE"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/img_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentStart="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                app:cardBackgroundColor="#F7F7F7"
                app:cardCornerRadius="10dp"
                app:strokeWidth="0dp">

                <ImageView
                    android:layout_width="6dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center"

                    android:src="@drawable/arrow_brown_new" />
            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:id="@+id/txt_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/roboto_bold"
                android:text="POS RX"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <RelativeLayout
                android:layout_width="33dp"
                android:layout_height="27dp"
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:layout_marginEnd="12dp">

                <ImageView
                    android:id="@+id/img_cart"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/rx_icon_new" />

                <RelativeLayout
                    android:id="@+id/rlt_cart_count"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/cart_count_bg" />

                    <TextView
                        android:id="@+id/txt_cart_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center"
                        android:maxLength="2"
                        android:text="2"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </RelativeLayout>
            </RelativeLayout>
        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">


            <LinearLayout

                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical|top"
                android:layout_marginTop="30dp"


                android:gravity="center_vertical|top"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="10dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="All PRODUCTS"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="match_parent"
                            android:layout_height="71dp"
                            android:layout_marginStart="12dp"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="12dp"
                            android:layout_marginBottom="1dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="10dp"
                            app:cardElevation="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_vertical"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <com.google.android.material.card.MaterialCardView
                                    android:id="@+id/card_influencer"
                                    android:layout_width="match_parent"
                                    android:layout_height="40dp"
                                    android:layout_marginStart="13dp"
                                    android:layout_marginEnd="5dp"
                                    android:layout_weight="1"
                                    app:cardBackgroundColor="@color/white"
                                    app:cardCornerRadius="5dp"
                                    app:strokeColor="#7D7C7C"
                                    app:strokeWidth="1dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:id="@+id/txt_influencer_product"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_marginStart="10dp"
                                            android:layout_weight="1"
                                            android:fontFamily="@font/roboto_regular"
                                            android:gravity="center_vertical"
                                            android:hint="Search influencer"
                                            android:singleLine="true"
                                            android:textColorHint="#B4ABAB"
                                            android:textSize="12sp" />

                                        <ImageView
                                            android:layout_width="24dp"
                                            android:layout_height="24dp"
                                            android:layout_marginEnd="6dp"
                                            android:src="@drawable/arrow_down"
                                            app:tint="#222222" />
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>

                                <com.google.android.material.card.MaterialCardView
                                    android:layout_width="match_parent"
                                    android:layout_height="40dp"
                                    android:layout_marginStart="12dp"
                                    android:layout_marginEnd="12dp"
                                    android:layout_marginBottom="1dp"
                                    android:layout_weight="1"
                                    app:cardBackgroundColor="@color/white"
                                    app:cardCornerRadius="5dp"
                                    app:strokeColor="#7D7C7C"
                                    app:strokeWidth="1dp">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <EditText
                                            android:id="@+id/edit_search"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_marginStart="15dp"
                                            android:layout_weight="1"
                                            android:background="@android:color/transparent"
                                            android:fontFamily="@font/roboto_regular"
                                            android:gravity="center_vertical"
                                            android:hint="Search Products"
                                            android:singleLine="true"
                                            android:textColorHint="#B4ABAB"
                                            android:textSize="12sp" />

                                        <ImageView
                                            android:layout_width="24dp"
                                            android:layout_height="24dp"
                                            android:layout_marginEnd="17dp"
                                            android:src="@drawable/search_green" />
                                    </LinearLayout>
                                </com.google.android.material.card.MaterialCardView>
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="12dp"
                            android:layout_marginTop="24dp"
                            android:layout_marginEnd="12dp"
                            android:layout_marginBottom="30dp">

                            <include
                                android:id="@+id/page_shimmer"
                                layout="@layout/shimmer_pos_shop_product" />

                            <TextView
                                android:id="@+id/txt_no_data"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:layout_marginTop="100dp"
                                android:fontFamily="@font/roboto_bold"
                                android:gravity="center"
                                android:text="No Data Found"
                                android:textColor="@color/black"
                                android:textSize="14sp"
                                android:visibility="gone" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recycler_products"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:nestedScrollingEnabled="false"
                                tools:itemCount="1"

                                tools:listitem="@layout/item_pos_shop_product" />
                        </RelativeLayout>
                        <RelativeLayout
                            android:id="@+id/progressBar_small"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="#50F9FEFF"
                            android:layout_gravity="center"
                            android:visibility="gone">

                            <ProgressBar
                                style="?android:attr/progressBarStyle"
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_centerInParent="true"
                                android:layout_gravity="center"
                                android:indeterminateDrawable="@drawable/rotating_icon"
                                android:visibility="visible"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />
                        </RelativeLayout>
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="10dp"
                        android:fontFamily="@font/roboto_bold"
                        android:text="CART"
                        android:textColor="@color/black"
                        android:textSize="18sp" />

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:elevation="2dp"
                        android:minHeight="300dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="2dp"
                        app:strokeWidth="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1">

                                <RelativeLayout
                                    android:id="@+id/progressBarCart"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="#50F9FEFF"
                                    android:visibility="gone">

                                    <ProgressBar
                                        style="?android:attr/progressBarStyle"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_centerInParent="true"
                                        android:layout_gravity="center"
                                        android:indeterminateDrawable="@drawable/rotating_icon"
                                        android:visibility="visible"
                                        app:layout_constraintBottom_toBottomOf="parent"
                                        app:layout_constraintEnd_toEndOf="parent"
                                        app:layout_constraintStart_toStartOf="parent"
                                        app:layout_constraintTop_toTopOf="parent" />
                                </RelativeLayout>

                                <TextView
                                    android:id="@+id/txt_no_data_cart"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerInParent="true"
                                    android:layout_gravity="center"
                                    android:layout_marginTop="25dp"
                                    android:fontFamily="@font/roboto_medium"
                                    android:text="No Data"
                                    android:textColor="@color/black"
                                    android:textSize="14sp"
                                    android:visibility="gone" />

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/recycler_cart"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_alignParentTop="true"
                                    tools:itemCount="1"

                                    tools:listitem="@layout/rx_cart_item" />
                            </RelativeLayout>

                            <com.google.android.material.card.MaterialCardView
                                android:id="@+id/card_proceed"
                                android:layout_width="match_parent"
                                android:layout_height="54dp"
                                android:layout_alignParentBottom="true"
                                android:layout_gravity="center"
                                android:layout_marginStart="22dp"
                                android:layout_marginTop="40dp"
                                android:layout_marginEnd="22dp"
                                android:layout_marginBottom="22dp"
                                app:cardBackgroundColor="@color/main_color"
                                app:cardCornerRadius="8dp"
                                app:layout_constraintBottom_toBottomOf="parent">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:fontFamily="@font/roboto_medium"
                                    android:text="Rx Preview"
                                    android:textColor="@color/white"
                                    android:textSize="14sp" />
                            </com.google.android.material.card.MaterialCardView>
                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>
                </LinearLayout>
            </LinearLayout>


        </LinearLayout>


    </LinearLayout>

    <RelativeLayout
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#50F9FEFF"
        android:visibility="gone">

        <ProgressBar
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:indeterminateDrawable="@drawable/rotating_icon"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>