<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_gravity="center_vertical"
            android:background="@color/white"
            android:elevation="5dp"
            android:gravity="center_vertical"

            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_back"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/pos_back" />

            <TextView
                android:id="@+id/txt_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="25dp"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_bold"
                android:text="Add New Products"
                android:textColor="@color/black"
                android:textSize="14sp" />

            <RelativeLayout
                android:id="@+id/img_cart"
                android:layout_width="33dp"
                android:layout_height="27dp"
                android:layout_marginEnd="12dp">

                <ImageView
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/header_cart" />

                <RelativeLayout
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_alignParentEnd="true">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:src="@drawable/cart_count_bg" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center"
                        android:maxLength="2"
                        android:text="2"
                        android:textColor="@color/white"
                        android:textSize="10sp" />
                </RelativeLayout>
            </RelativeLayout>

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="20dp"
                android:src="@drawable/home_notif" />
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentTop="true"

                android:fillViewport="true"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="19dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="17dp"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="5dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="21dp"
                                android:layout_marginTop="11dp"
                                android:fontFamily="@font/roboto_bold"
                                android:text="PDF Specification"
                                android:textColor="#424546"
                                android:textSize="16sp" />

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:layout_marginTop="10dp"
                                android:background="#40000000" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="22dp"
                                android:layout_marginStart="21dp"
                                android:layout_marginTop="22dp"
                                android:fontFamily="@font/roboto_regular"
                                android:text="PDF Specification"
                                android:textColor="@color/black"
                                android:textSize="12sp" />
                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_marginStart="15dp"
                                android:layout_marginTop="7dp"
                                android:layout_marginEnd="15dp"
                                app:cardBackgroundColor="@color/white"
                                app:cardCornerRadius="5dp"
                                android:layout_marginBottom="27dp"
                                app:strokeColor="#7D7C7C"
                                app:strokeWidth="1dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:orientation="horizontal">

                                    <LinearLayout
                                        android:layout_width="53dp"
                                        android:layout_height="match_parent"
                                        android:layout_gravity="center"
                                        android:background="#DADADA"
                                        android:gravity="center">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="22dp"
                                            android:layout_gravity="center"
                                            android:fontFamily="@font/roboto_bold"
                                            android:gravity="center"
                                            android:text="Browse"
                                            android:textColor="@color/black"
                                            android:textSize="12sp" />
                                    </LinearLayout>

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="22dp"
                                        android:layout_gravity="center"
                                        android:layout_marginStart="7dp"
                                        android:fontFamily="@font/roboto_regular"
                                        android:gravity="center"
                                        android:text="Choose file"
                                        android:textColor="#B4ABAB"
                                        android:textSize="12sp" />
                                </LinearLayout>
                            </com.google.android.material.card.MaterialCardView>

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="83dp"
                android:layout_alignParentBottom="true"
                android:background="@color/white"
                android:elevation="10dp"
                android:visibility="visible">
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_back"
                    android:layout_width="145dp"
                    android:layout_height="54dp"
                    android:elevation="1dp"
                    app:cardElevation="1dp"
                    android:layout_alignParentStart="true"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="17dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="51dp"
                    app:strokeColor="#A61C5C"
                    app:strokeWidth="1dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:text="Back"
                        android:textColor="#A61C5C"
                        android:textSize="18sp" />
                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_next"
                    android:layout_width="145dp"
                    android:layout_height="54dp"
                    android:elevation="1dp"
                    app:cardElevation="1dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerInParent="true"
                    android:layout_marginEnd="28dp"
                    app:cardBackgroundColor="#A61C5C"
                    app:cardCornerRadius="51dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:text="Next"
                        android:textColor="@color/white"
                        android:textSize="18sp" />
                </com.google.android.material.card.MaterialCardView>
            </RelativeLayout>
        </RelativeLayout>

    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>