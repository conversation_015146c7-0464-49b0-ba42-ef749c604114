<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="2dp"
    android:layout_height="35dp"
    app:strokeWidth="0dp"
    android:orientation="horizontal"
    app:cardBackgroundColor="#EEEEEE"
    android:layout_margin="4dp"
    android:paddingStart="10dp"
    android:paddingEnd="5dp"
    android:gravity="center_vertical">
<LinearLayout
    android:layout_marginStart="10dp"
    android:layout_marginEnd="10dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:gravity="center_vertical"
    android:orientation="horizontal">
    <TextView
        android:id="@+id/chipText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Product"
        android:fontFamily="@font/roboto_regular"
        android:textSize="12sp"
        android:textColor="#000000" />

    <ImageView
        android:id="@+id/chipClose"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="6dp"
        android:src="@drawable/close_filter"
        android:contentDescription="Remove" />
</LinearLayout>

</com.google.android.material.card.MaterialCardView>
