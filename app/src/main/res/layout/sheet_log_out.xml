<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="200dp"
    android:background="@drawable/sheet_bg"
    android:orientation="vertical"
    android:paddingStart="25dp"
    android:paddingEnd="25dp">
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="23dp"
        android:fontFamily="@font/roboto_bold"
        android:letterSpacing="-0.02"
        android:text="@string/log_out"
        android:textAlignment="center"
        android:textColor="@color/main_color"
        android:textSize="16dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="20dp"
        android:background="#40008F96" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="17dp"
        android:fontFamily="@font/roboto_regular"
        android:text="@string/are_you_sure_you_want_to_logout"
        android:textAlignment="center"
        android:textColor="#B2000000"
        android:textSize="14dp" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="28dp"
        android:layout_marginBottom="20dp">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_cancel"
            android:layout_width="129dp"
            android:layout_height="35dp"
            android:layout_alignParentStart="true"
            app:cardCornerRadius="19dp"
            app:cardBackgroundColor="@color/white"
            app:strokeColor="@color/main_color"
            app:strokeWidth="1dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:fontFamily="@font/roboto_bold"
                android:gravity="center"
                android:text="@string/cancel"
                android:textColor="@color/main_color"
                android:textSize="14dp" />
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_yes"
            android:layout_width="129dp"
            android:layout_height="35dp"
            android:layout_alignParentEnd="true"
            app:cardBackgroundColor="@color/main_color"
            app:cardCornerRadius="19dp"
            app:strokeColor="@color/main_color"
            app:strokeWidth="1dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:fontFamily="@font/roboto_bold"
                android:gravity="center"
                android:text="@string/log_out"
                android:textColor="@color/white"
                android:textSize="14dp" />
        </com.google.android.material.card.MaterialCardView>
    </RelativeLayout>
</LinearLayout>
