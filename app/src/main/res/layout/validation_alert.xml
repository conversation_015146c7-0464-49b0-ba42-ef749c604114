<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="#F9FEFF"
    android:layout_height="match_parent">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:strokeWidth="0dp"
        android:layout_centerInParent="true"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:orientation="vertical">


            <TextView
                android:textAlignment="center"
                android:layout_margin="10dp"
                android:id="@+id/text_validation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_regular"
                android:textColor="@color/black"
                android:textSize="16dp" />





            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="23dp"
                android:gravity="center"
                android:orientation="horizontal">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/view_cart"
                    android:layout_width="124dp"
                    android:layout_height="34dp"
                    app:cardBackgroundColor="@color/main_color"
                    app:cardCornerRadius="5dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:text="OK"
                        android:textColor="@color/white"
                        android:textSize="12dp" />
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>
</RelativeLayout>