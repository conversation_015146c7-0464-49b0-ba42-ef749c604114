<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardBackgroundColor="@color/black"
    android:padding="10dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_margin="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tvDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2025-06-06"
            android:textColor="@android:color/white"
            android:textSize="13sp"
            android:textStyle="bold"/>

        <TextView
            android:layout_marginTop="5dp"
            android:id="@+id/tvValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Number of Rx : 1"
            android:textColor="@android:color/white"
            android:textSize="13sp"/>
    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
