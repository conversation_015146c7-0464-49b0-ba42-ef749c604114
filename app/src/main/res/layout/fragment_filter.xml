<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/lyt_filter"
    android:visibility="gone"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="96dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="15dp">

                <ImageView
                    android:id="@+id/img_close"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentStart="true"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/close_filter" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:fontFamily="@font/roboto_bold"
                    android:text="Filters"
                    android:textColor="@color/black"
                    android:textSize="18sp" />
            </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_marginStart="1dp"
            android:layout_marginEnd="1dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/rounded_top_corners"
            android:orientation="vertical"
            android:padding="20dp">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_filter_delivery_staff"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="15dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/txt_filter_delivery_staff"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/roboto_regular"
                            android:gravity="center_vertical"
                            android:text="Filter by Delivery Staff"
                            android:textColor="#B4ABAB"
                            android:textSize="12sp" />

                        <ImageView
                            android:id="@+id/img_delivery_staff_arrow"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="6dp"
                            android:src="@drawable/arrow_down"
                            app:tint="#222222" />
                    </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/card_delivery_staff_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#40000000"/>
                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_margin="10dp"
                            android:layout_height="wrap_content"
                            android:id="@+id/recycler_delivery_staff"/>
                    </LinearLayout>
                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_nothing_selected"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="15dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/txt_filter_nothing_selected"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:hint="Filter By Delivery"
                        android:textColorHint="#B4ABAB"
                        android:textSize="12sp" />

                    <ImageView
                        android:id="@+id/img_nothing_selected_arrow"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="6dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#222222" />
                </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/card_nothing_selected_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#40000000"/>
                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_margin="10dp"
                            android:layout_height="wrap_content"
                            android:id="@+id/recycler_nothing_selected"/>
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_filter_payment_status"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="15dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_payment_status"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:text="Filter by Payment Status"
                        android:textColor="#B4ABAB"
                        android:textSize="12sp" />

                    <ImageView
                        android:id="@+id/img_payment_status_arrow"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="6dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#222222" />
                </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/card_filter_payment_status_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#40000000"/>
                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_margin="10dp"
                            android:layout_height="wrap_content"
                            android:id="@+id/recycler_payment_status"/>
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_filter_by_date"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="18dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_filter_by_date"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:text="Filter by date"
                        android:textColor="#B4ABAB"
                        android:textSize="12sp" />

                    <ImageView
                        android:id="@+id/img_filter_by_date_arrow"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="6dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#222222" />
                </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/card_filter_by_date_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#40000000"/>
                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_margin="10dp"
                            android:layout_height="wrap_content"
                            android:id="@+id/recycler_filter_by_date"/>
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_filter_payment_change"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="18dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_filter_by_pc"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:text="Filter by Payment Change"
                        android:textColor="#B4ABAB"
                        android:textSize="12sp" />

                    <ImageView
                        android:id="@+id/img_filter_by_pc"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="6dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#222222" />
                </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/card_filter_by_p_c_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#40000000"/>
                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_margin="10dp"
                            android:layout_height="wrap_content"
                            android:id="@+id/recycler_filter_by_p_c"/>
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:id="@+id/card_filter_payment_method"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                android:layout_marginTop="18dp"
                android:layout_marginStart="1dp"
                android:layout_marginEnd="1dp"
                android:layout_marginBottom="1dp"
                app:strokeColor="#7D7C7C"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txt_filter_p_m"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginStart="10dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/roboto_regular"
                        android:gravity="center_vertical"
                        android:text="Filter by Payment method"
                        android:textColor="#B4ABAB"
                        android:textSize="12sp" />

                    <ImageView
                        android:id="@+id/img_filter_p_m"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="6dp"
                        android:src="@drawable/arrow_down"
                        app:tint="#222222" />
                </LinearLayout>
                    <LinearLayout
                        android:visibility="gone"
                        android:id="@+id/card_filter_by_p_m_hide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="#40000000"/>
                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_margin="10dp"
                            android:layout_height="wrap_content"
                            android:id="@+id/recycler_filter_by_p_m"/>
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"/>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_weight="1"
                    android:id="@+id/card_apply_filter"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="25dp"
                    app:cardCornerRadius="8dp"
                    app:cardBackgroundColor="#A61C5C"
                    android:layout_height="48dp">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:text="Apply Filter"
                        android:textColor="@color/white"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:layout_gravity="center"/>
                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="100dp"
                    android:id="@+id/card_clear"
                    android:layout_gravity="center"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="25dp"
                    app:cardCornerRadius="8dp"
                    android:layout_marginStart="10dp"
                    app:cardBackgroundColor="@color/white"
                    app:strokeColor="@color/main_color"
                    app:strokeWidth="1dp"
                    android:layout_height="45dp">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:text="Clear"
                        android:textColor="@color/main_color"
                        android:fontFamily="@font/roboto_medium"
                        android:gravity="center"
                        android:layout_gravity="center"/>
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>        </LinearLayout>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>