<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginEnd="15dp"
    android:gravity="center"

    >

    <com.google.android.material.card.MaterialCardView
        android:layout_width="174dp"
        android:layout_height="99dp"
        android:layout_gravity="center"
        android:layout_margin="1dp"
        android:gravity="center"
        app:cardBackgroundColor="#F7F7F7"
        app:cardCornerRadius="22dp"
        app:cardElevation="1dp"
        app:strokeColor="#F7F7F7"
        app:strokeWidth="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="10dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="27dp"
                    android:layout_height="27dp"
                    android:src="@drawable/dummy_graph" />

                <TextView
                    android:maxLines="2"
                    android:id="@+id/txt_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:fontFamily="@font/roboto_medium"
                    android:text="Total Prescriptions"
                    android:textColor="@color/black"
                    android:textSize="16dp" />
            </LinearLayout>

            <TextView
                android:singleLine="true"
                android:id="@+id/txt_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="5dp"
                android:fontFamily="@font/roboto_bold"
                android:text="15"
                android:textColor="@color/doctor_main_color"
                android:textSize="20sp" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>
</FrameLayout>