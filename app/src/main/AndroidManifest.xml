<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <application
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.WasfaAdminApp"
        tools:targetApi="31">
        <activity
            android:name=".ui.order.OrderListActivity"
            android:exported="false" />
        <activity
            android:name=".ui.images.ImageListActivity"
            android:exported="false" />
        <activity
            android:name=".ui.products.add.AddProductActivity"
            android:exported="false" />
        <activity
            android:name=".ui.products.category.CategoryActivity"
            android:exported="false" />
        <activity
            android:name=".ui.products.category.CategoryAddActivity"
            android:exported="false" />
        <activity
            android:name=".ui.products.category.CategoryEditActivity"
            android:exported="false" />
        <activity
            android:name=".ui.products.brand.EditBrandActivity"
            android:exported="false" />
        <activity
            android:name=".ui.products.brand.AddBrandActivity"
            android:exported="false" />
        <activity
            android:name=".ui.products.brand.BrandActivity"
            android:exported="false" />
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
        <activity
            android:name=".ui.doctor.main.DoctorHomeActivity"
            android:exported="false" />
        <activity
            android:name=".ui.login.LoginActivity"
            android:exported="false" />
        <activity
            android:name=".ui.splash.SplashActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.WasfaAdminApp.Fullscreen">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.cart.SucessActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustResize" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>