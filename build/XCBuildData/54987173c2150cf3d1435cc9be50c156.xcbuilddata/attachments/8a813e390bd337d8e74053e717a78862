{"": {"diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire-master.dia", "emit-module-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire-master.swiftdeps"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Alamofire.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Alamofire~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/AFError.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AFError.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AFError.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AFError.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AFError.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AFError.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AFError.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AFError.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AFError~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/DataRequest.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataRequest.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataRequest.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataRequest.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataRequest.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataRequest.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataRequest~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/DataStreamRequest.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataStreamRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataStreamRequest.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataStreamRequest.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataStreamRequest.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataStreamRequest.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataStreamRequest.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataStreamRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DataStreamRequest~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/DownloadRequest.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DownloadRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DownloadRequest.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DownloadRequest.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DownloadRequest.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DownloadRequest.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DownloadRequest.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DownloadRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DownloadRequest~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/HTTPHeaders.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPHeaders.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPHeaders.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPHeaders.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPHeaders.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPHeaders.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPHeaders.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPHeaders.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPHeaders~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/HTTPMethod.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPMethod.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPMethod.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPMethod.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPMethod.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPMethod.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPMethod.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPMethod.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/HTTPMethod~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/Notifications.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Notifications.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Notifications.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Notifications.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Notifications.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Notifications.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Notifications.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Notifications.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Notifications~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/ParameterEncoder.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoder.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoder.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoder.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoder.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoder.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoder.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoder.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoder~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/ParameterEncoding.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoding.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoding.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoding.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoding.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoding.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoding.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoding.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ParameterEncoding~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/Protected.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Protected.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Protected.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Protected.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Protected.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Protected.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Protected.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Protected.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Protected~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/Request.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Request.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Request.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Request.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Request.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Request.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Request.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Request.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Request~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/RequestTaskMap.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestTaskMap.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestTaskMap.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestTaskMap.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestTaskMap.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestTaskMap.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestTaskMap.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestTaskMap.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestTaskMap~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/Response.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Response.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Response.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Response.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Response.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Response.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Response.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Response.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Response~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/Session.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Session.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Session.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Session.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Session.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Session.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Session.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Session.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Session~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/SessionDelegate.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/SessionDelegate.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/SessionDelegate.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/SessionDelegate.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/SessionDelegate.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/SessionDelegate.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/SessionDelegate.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/SessionDelegate.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/SessionDelegate~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/URLConvertible+URLRequestConvertible.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLConvertible+URLRequestConvertible.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLConvertible+URLRequestConvertible.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLConvertible+URLRequestConvertible.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLConvertible+URLRequestConvertible.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLConvertible+URLRequestConvertible.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLConvertible+URLRequestConvertible.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLConvertible+URLRequestConvertible.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLConvertible+URLRequestConvertible~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/UploadRequest.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/UploadRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/UploadRequest.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/UploadRequest.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/UploadRequest.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/UploadRequest.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/UploadRequest.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/UploadRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/UploadRequest~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Core/WebSocketRequest.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/WebSocketRequest.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/WebSocketRequest.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/WebSocketRequest.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/WebSocketRequest.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/WebSocketRequest.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/WebSocketRequest.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/WebSocketRequest.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/WebSocketRequest~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Extensions/DispatchQueue+Alamofire.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DispatchQueue+Alamofire.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DispatchQueue+Alamofire.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DispatchQueue+Alamofire.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DispatchQueue+Alamofire.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DispatchQueue+Alamofire.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DispatchQueue+Alamofire.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DispatchQueue+Alamofire.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/DispatchQueue+Alamofire~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Extensions/OperationQueue+Alamofire.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/OperationQueue+Alamofire.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/OperationQueue+Alamofire.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/OperationQueue+Alamofire.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/OperationQueue+Alamofire.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/OperationQueue+Alamofire.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/OperationQueue+Alamofire.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/OperationQueue+Alamofire.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/OperationQueue+Alamofire~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Extensions/Result+Alamofire.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Result+Alamofire.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Result+Alamofire.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Result+Alamofire.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Result+Alamofire.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Result+Alamofire.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Result+Alamofire.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Result+Alamofire.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Result+Alamofire~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Extensions/StringEncoding+Alamofire.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/StringEncoding+Alamofire.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/StringEncoding+Alamofire.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/StringEncoding+Alamofire.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/StringEncoding+Alamofire.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/StringEncoding+Alamofire.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/StringEncoding+Alamofire.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/StringEncoding+Alamofire.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/StringEncoding+Alamofire~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Extensions/URLRequest+Alamofire.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLRequest+Alamofire.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLRequest+Alamofire.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLRequest+Alamofire.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLRequest+Alamofire.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLRequest+Alamofire.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLRequest+Alamofire.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLRequest+Alamofire.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLRequest+Alamofire~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Extensions/URLSessionConfiguration+Alamofire.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLSessionConfiguration+Alamofire.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLSessionConfiguration+Alamofire.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLSessionConfiguration+Alamofire.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLSessionConfiguration+Alamofire.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLSessionConfiguration+Alamofire.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLSessionConfiguration+Alamofire.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLSessionConfiguration+Alamofire.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLSessionConfiguration+Alamofire~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/AlamofireExtended.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AlamofireExtended.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AlamofireExtended.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AlamofireExtended.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AlamofireExtended.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AlamofireExtended.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AlamofireExtended.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AlamofireExtended.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AlamofireExtended~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/AuthenticationInterceptor.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AuthenticationInterceptor.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AuthenticationInterceptor.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AuthenticationInterceptor.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AuthenticationInterceptor.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AuthenticationInterceptor.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AuthenticationInterceptor.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AuthenticationInterceptor.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/AuthenticationInterceptor~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/CachedResponseHandler.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/CachedResponseHandler.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/CachedResponseHandler.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/CachedResponseHandler.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/CachedResponseHandler.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/CachedResponseHandler.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/CachedResponseHandler.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/CachedResponseHandler.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/CachedResponseHandler~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/Combine.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Combine.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Combine.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Combine.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Combine.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Combine.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Combine.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Combine.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Combine~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/Concurrency.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Concurrency.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Concurrency.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Concurrency.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Concurrency.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Concurrency.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Concurrency.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Concurrency.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Concurrency~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/EventMonitor.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/EventMonitor.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/EventMonitor.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/EventMonitor.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/EventMonitor.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/EventMonitor.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/EventMonitor.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/EventMonitor.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/EventMonitor~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/MultipartFormData.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartFormData.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartFormData.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartFormData.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartFormData.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartFormData.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartFormData.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartFormData.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartFormData~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/MultipartUpload.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartUpload.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartUpload.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartUpload.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartUpload.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartUpload.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartUpload.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartUpload.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/MultipartUpload~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/NetworkReachabilityManager.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/NetworkReachabilityManager.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/NetworkReachabilityManager.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/NetworkReachabilityManager.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/NetworkReachabilityManager.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/NetworkReachabilityManager.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/NetworkReachabilityManager.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/NetworkReachabilityManager.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/NetworkReachabilityManager~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/RedirectHandler.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RedirectHandler.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RedirectHandler.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RedirectHandler.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RedirectHandler.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RedirectHandler.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RedirectHandler.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RedirectHandler.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RedirectHandler~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/RequestCompression.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestCompression.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestCompression.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestCompression.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestCompression.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestCompression.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestCompression.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestCompression.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestCompression~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/RequestInterceptor.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestInterceptor.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestInterceptor.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestInterceptor.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestInterceptor.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestInterceptor.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestInterceptor.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestInterceptor.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RequestInterceptor~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/ResponseSerialization.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ResponseSerialization.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ResponseSerialization.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ResponseSerialization.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ResponseSerialization.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ResponseSerialization.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ResponseSerialization.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ResponseSerialization.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ResponseSerialization~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/RetryPolicy.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RetryPolicy.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RetryPolicy.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RetryPolicy.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RetryPolicy.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RetryPolicy.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RetryPolicy.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RetryPolicy.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/RetryPolicy~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/ServerTrustEvaluation.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ServerTrustEvaluation.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ServerTrustEvaluation.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ServerTrustEvaluation.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ServerTrustEvaluation.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ServerTrustEvaluation.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ServerTrustEvaluation.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ServerTrustEvaluation.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/ServerTrustEvaluation~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/URLEncodedFormEncoder.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLEncodedFormEncoder.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLEncodedFormEncoder.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLEncodedFormEncoder.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLEncodedFormEncoder.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLEncodedFormEncoder.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLEncodedFormEncoder.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLEncodedFormEncoder.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/URLEncodedFormEncoder~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/Source/Features/Validation.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Validation.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Validation.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Validation.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Validation.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Validation.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Validation.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Validation.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/Validation~partial.swiftmodule"}, "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/DerivedSources/resource_bundle_accessor.swift": {"const-values": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/resource_bundle_accessor.swiftconstvalues", "dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/resource_bundle_accessor.d", "diagnostics": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/resource_bundle_accessor.dia", "index-unit-output-path": "/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/resource_bundle_accessor.o", "llvm-bc": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/resource_bundle_accessor.bc", "object": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/resource_bundle_accessor.o", "swift-dependencies": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/resource_bundle_accessor.swiftdeps", "swiftmodule": "/Users/<USER>/Library/Developer/Xcode/DerivedData/WasfaAdmin-grfhfhushxgzofdxoqvdbzpbgyvk/SourcePackages/checkouts/Alamofire/build/Alamofire.build/Debug-iphoneos/Alamofire.build/Objects-normal/arm64/resource_bundle_accessor~partial.swiftmodule"}}