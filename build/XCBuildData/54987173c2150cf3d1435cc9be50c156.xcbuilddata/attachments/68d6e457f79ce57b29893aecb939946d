/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/View/MainScrollBody.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/Model/SuperModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/View/SuperView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Double+Extension.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/AppState/enums.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/Route/Route.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/Route/RouterManager.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/View/DashboardView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Data+Extension.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/View/HeaderView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/CustomInputField/CustomInputField.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Font+Extensions.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/Route/RouterManagerPerformanceTest.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/TabExtension.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/View/SignInView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/View/CustomTabBar.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Prescriptions/Model/PrescriptionsModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/TokenManager/TokenManager.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/ViewModel/SignInViewModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/Repositories/APIEndPoints.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/Repositories/RepositoriesAPI.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/Repositories/RepositoriesNetworking.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/ActivityLoader/ActivityLoaderView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/Toast/ToastModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/Toast/ToastModifier.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/Toast/ToastView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Prescriptions/ViewModel/PrescriptionsViewModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/ViewModel/SuperViewModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/Model/HomeModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Constants/AppConstants.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Constants/ColorConstants.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Constants/FontScheme.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/BaseAPI/TargetType.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Medications/Model/MedicationsModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Prescriptions/View/PrescriptionsView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/String+Extension.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/View/HomeView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/Logger/Logger.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Utilities/Utilities.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/BaseAPI/BaseAPI.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Center/View/CenterView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/View+Extension.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/View/PrescriptionCardView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/AlertView/AlertView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/Logger/NetworkLogger.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/Viewport/ViewportHelper.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Settings/ViewModel/SettingsViewModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Center/ViewModel/CenterViewModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Medications/ViewModel/MedicationsViewModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/ViewModel/DashboardViewModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/ActivityLoader/ActivityLoader.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Color+Extension.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Model/DashbordModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/ViewModel/HomeViewModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/Model/SignInModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/AppState/AppState.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/UserDefaults/UserDefaults.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Application/WasfaAdminApp.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Medications/View/MedicationsView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Settings/View/SettingsView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Main/MainView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Splash/SplashView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/ImagePicker/ImagePicker.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/ImagePicker/ImageSelectionSheet.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/PermissionManager/PermissionManager.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/CachedAsyncImage/CachedAsyncImageView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Settings/Model/SettingsModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Application/AppDelegate.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/View/ScrollProgressIndicator.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Center/Model/CenterModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Date+Extension.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/View/LazyTabView.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Main/MainViewModel.swift
/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/AppState/AppState+Extention.swift
/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/DerivedSources/GeneratedAssetSymbols.swift
