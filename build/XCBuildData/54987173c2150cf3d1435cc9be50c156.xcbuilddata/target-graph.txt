Target dependency graph (11 targets)
Target 'WasfaAdmin' in project 'WasfaAdmin'
➜ Explicit dependency on target 'SwiftyJSO<PERSON>' in project 'SwiftyJSON'
➜ Explicit dependency on target 'SecureDefaults' in project 'SecureDefaults'
➜ Explicit dependency on target 'SwipeActions' in project 'SwipeActions'
➜ Explicit dependency on target 'Alamofire' in project 'Alamofire'
Target 'Alamofire' in project 'Alamofire'
➜ Explicit dependency on target 'Alamofire' in project 'Alamofire'
➜ Explicit dependency on target 'Alamofire_Alamofire' in project 'Alamofire'
Target 'Alamofire' in project 'Alamofire'
➜ Explicit dependency on target 'Alamofire_Alamofire' in project 'Alamofire'
Target 'Alamofire_Alamofire' in project 'Alamofire' (no dependencies)
Target 'SwipeActions' in project 'SwipeActions'
➜ Explicit dependency on target 'SwipeActions' in project 'SwipeActions'
Target 'SwipeActions' in project 'SwipeActions' (no dependencies)
Target 'SecureDefaults' in project 'SecureDefaults'
➜ Explicit dependency on target 'SecureDefaults' in project 'SecureDefaults'
Target 'SecureDefaults' in project 'SecureDefaults' (no dependencies)
Target 'SwiftyJSON' in project 'SwiftyJSON'
➜ Explicit dependency on target 'SwiftyJSON' in project 'SwiftyJSON'
➜ Explicit dependency on target 'SwiftyJSON_SwiftyJSON' in project 'SwiftyJSON'
Target 'SwiftyJSON' in project 'SwiftyJSON'
➜ Explicit dependency on target 'SwiftyJSON_SwiftyJSON' in project 'SwiftyJSON'
Target 'SwiftyJSON_SwiftyJSON' in project 'SwiftyJSON' (no dependencies)