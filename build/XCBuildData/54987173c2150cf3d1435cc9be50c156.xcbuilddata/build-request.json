{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildAndRun"}, "configuredTargets": [{"guid": "deea7ef3cb9dfc2d1e19180838fc8e9420f9cb52af678423ce7b0f0b493dd42c"}], "containerPath": "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO"}}}}, "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": false, "useLegacyBuildLocations": false, "useParallelTargets": true}