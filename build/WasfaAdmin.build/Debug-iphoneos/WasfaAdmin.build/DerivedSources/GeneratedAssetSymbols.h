#import <Foundation/Foundation.h>

#if __has_attribute(swift_private)
#define AC_SWIFT_PRIVATE __attribute__((swift_private))
#else
#define AC_SWIFT_PRIVATE
#endif

/// The resource bundle ID.
static NSString * const ACBundleID AC_SWIFT_PRIVATE = @"com.wasfa.dashboard.app.s.WasfaApixrx";

/// The "AdminMainColor" asset catalog color resource.
static NSString * const ACColorNameAdminMainColor AC_SWIFT_PRIVATE = @"AdminMainColor";

/// The "Black900" asset catalog color resource.
static NSString * const ACColorNameBlack900 AC_SWIFT_PRIVATE = @"Black900";

/// The "Black90001" asset catalog color resource.
static NSString * const ACColorNameBlack90001 AC_SWIFT_PRIVATE = @"Black90001";

/// The "Black9000f" asset catalog color resource.
static NSString * const ACColorNameBlack9000F AC_SWIFT_PRIVATE = @"Black9000f";

/// The "Black9001e" asset catalog color resource.
static NSString * const ACColorNameBlack9001E AC_SWIFT_PRIVATE = @"Black9001e";

/// The "Black90021" asset catalog color resource.
static NSString * const ACColorNameBlack90021 AC_SWIFT_PRIVATE = @"Black90021";

/// The "Black90023" asset catalog color resource.
static NSString * const ACColorNameBlack90023 AC_SWIFT_PRIVATE = @"Black90023";

/// The "Black9002b" asset catalog color resource.
static NSString * const ACColorNameBlack9002B AC_SWIFT_PRIVATE = @"Black9002b";

/// The "Black9003d" asset catalog color resource.
static NSString * const ACColorNameBlack9003D AC_SWIFT_PRIVATE = @"Black9003d";

/// The "Black9003f" asset catalog color resource.
static NSString * const ACColorNameBlack9003F AC_SWIFT_PRIVATE = @"Black9003f";

/// The "Black90042" asset catalog color resource.
static NSString * const ACColorNameBlack90042 AC_SWIFT_PRIVATE = @"Black90042";

/// The "Black90054" asset catalog color resource.
static NSString * const ACColorNameBlack90054 AC_SWIFT_PRIVATE = @"Black90054";

/// The "Black9005b" asset catalog color resource.
static NSString * const ACColorNameBlack9005B AC_SWIFT_PRIVATE = @"Black9005b";

/// The "Black900Bf" asset catalog color resource.
static NSString * const ACColorNameBlack900Bf AC_SWIFT_PRIVATE = @"Black900Bf";

/// The "Blue.light" asset catalog color resource.
static NSString * const ACColorNameBlueLight AC_SWIFT_PRIVATE = @"Blue.light";

/// The "Blue300" asset catalog color resource.
static NSString * const ACColorNameBlue300 AC_SWIFT_PRIVATE = @"Blue300";

/// The "Blue600" asset catalog color resource.
static NSString * const ACColorNameBlue600 AC_SWIFT_PRIVATE = @"Blue600";

/// The "Blue6002b" asset catalog color resource.
static NSString * const ACColorNameBlue6002B AC_SWIFT_PRIVATE = @"Blue6002b";

/// The "Blue60038" asset catalog color resource.
static NSString * const ACColorNameBlue60038 AC_SWIFT_PRIVATE = @"Blue60038";

/// The "Blue6003a" asset catalog color resource.
static NSString * const ACColorNameBlue6003A AC_SWIFT_PRIVATE = @"Blue6003a";

/// The "Blue60054" asset catalog color resource.
static NSString * const ACColorNameBlue60054 AC_SWIFT_PRIVATE = @"Blue60054";

/// The "Blue6009e" asset catalog color resource.
static NSString * const ACColorNameBlue6009E AC_SWIFT_PRIVATE = @"Blue6009e";

/// The "BlueGray100" asset catalog color resource.
static NSString * const ACColorNameBlueGray100 AC_SWIFT_PRIVATE = @"BlueGray100";

/// The "BlueGray10001" asset catalog color resource.
static NSString * const ACColorNameBlueGray10001 AC_SWIFT_PRIVATE = @"BlueGray10001";

/// The "BlueGray10002" asset catalog color resource.
static NSString * const ACColorNameBlueGray10002 AC_SWIFT_PRIVATE = @"BlueGray10002";

/// The "BlueGray900" asset catalog color resource.
static NSString * const ACColorNameBlueGray900 AC_SWIFT_PRIVATE = @"BlueGray900";

/// The "Cyan4000a" asset catalog color resource.
static NSString * const ACColorNameCyan4000A AC_SWIFT_PRIVATE = @"Cyan4000a";

/// The "Cyan8003f" asset catalog color resource.
static NSString * const ACColorNameCyan8003F AC_SWIFT_PRIVATE = @"Cyan8003f";

/// The "DescriptionText" asset catalog color resource.
static NSString * const ACColorNameDescriptionText AC_SWIFT_PRIVATE = @"DescriptionText";

/// The "DoctorMainColor" asset catalog color resource.
static NSString * const ACColorNameDoctorMainColor AC_SWIFT_PRIVATE = @"DoctorMainColor";

/// The "Gray20001" asset catalog color resource.
static NSString * const ACColorNameGray20001 AC_SWIFT_PRIVATE = @"Gray20001";

/// The "Gray600" asset catalog color resource.
static NSString * const ACColorNameGray600 AC_SWIFT_PRIVATE = @"Gray600";

/// The "Gray800" asset catalog color resource.
static NSString * const ACColorNameGray800 AC_SWIFT_PRIVATE = @"Gray800";

/// The "Green900" asset catalog color resource.
static NSString * const ACColorNameGreen900 AC_SWIFT_PRIVATE = @"Green900";

/// The "GreenA700" asset catalog color resource.
static NSString * const ACColorNameGreenA700 AC_SWIFT_PRIVATE = @"GreenA700";

/// The "LightGreen600" asset catalog color resource.
static NSString * const ACColorNameLightGreen600 AC_SWIFT_PRIVATE = @"LightGreen600";

/// The "Pink300C1" asset catalog color resource.
static NSString * const ACColorNamePink300C1 AC_SWIFT_PRIVATE = @"Pink300C1";

/// The "Red600" asset catalog color resource.
static NSString * const ACColorNameRed600 AC_SWIFT_PRIVATE = @"Red600";

/// The "RedA700" asset catalog color resource.
static NSString * const ACColorNameRedA700 AC_SWIFT_PRIVATE = @"RedA700";

/// The "Teal900" asset catalog color resource.
static NSString * const ACColorNameTeal900 AC_SWIFT_PRIVATE = @"Teal900";

/// The "WhiteA70093" asset catalog color resource.
static NSString * const ACColorNameWhiteA70093 AC_SWIFT_PRIVATE = @"WhiteA70093";

/// The "add_icon" asset catalog image resource.
static NSString * const ACImageNameAddIcon AC_SWIFT_PRIVATE = @"add_icon";

/// The "add_icon_alt" asset catalog image resource.
static NSString * const ACImageNameAddIconAlt AC_SWIFT_PRIVATE = @"add_icon_alt";

/// The "admin_sale_icon_basket" asset catalog image resource.
static NSString * const ACImageNameAdminSaleIconBasket AC_SWIFT_PRIVATE = @"admin_sale_icon_basket";

/// The "admin_tab_account" asset catalog image resource.
static NSString * const ACImageNameAdminTabAccount AC_SWIFT_PRIVATE = @"admin_tab_account";

/// The "admin_tab_pos" asset catalog image resource.
static NSString * const ACImageNameAdminTabPos AC_SWIFT_PRIVATE = @"admin_tab_pos";

/// The "admin_tab_products" asset catalog image resource.
static NSString * const ACImageNameAdminTabProducts AC_SWIFT_PRIVATE = @"admin_tab_products";

/// The "admin_tab_sale" asset catalog image resource.
static NSString * const ACImageNameAdminTabSale AC_SWIFT_PRIVATE = @"admin_tab_sale";

/// The "arrow_left_icon" asset catalog image resource.
static NSString * const ACImageNameArrowLeftIcon AC_SWIFT_PRIVATE = @"arrow_left_icon";

/// The "arrow_left_icon_alt" asset catalog image resource.
static NSString * const ACImageNameArrowLeftIconAlt AC_SWIFT_PRIVATE = @"arrow_left_icon_alt";

/// The "arrow_right_icon" asset catalog image resource.
static NSString * const ACImageNameArrowRightIcon AC_SWIFT_PRIVATE = @"arrow_right_icon";

/// The "bell_icon" asset catalog image resource.
static NSString * const ACImageNameBellIcon AC_SWIFT_PRIVATE = @"bell_icon";

/// The "card_wave_overlay" asset catalog image resource.
static NSString * const ACImageNameCardWaveOverlay AC_SWIFT_PRIVATE = @"card_wave_overlay";

/// The "delete_icon" asset catalog image resource.
static NSString * const ACImageNameDeleteIcon AC_SWIFT_PRIVATE = @"delete_icon";

/// The "edit_icon" asset catalog image resource.
static NSString * const ACImageNameEditIcon AC_SWIFT_PRIVATE = @"edit_icon";

/// The "file_icon" asset catalog image resource.
static NSString * const ACImageNameFileIcon AC_SWIFT_PRIVATE = @"file_icon";

/// The "filter_icon" asset catalog image resource.
static NSString * const ACImageNameFilterIcon AC_SWIFT_PRIVATE = @"filter_icon";

/// The "filter_icon_alt" asset catalog image resource.
static NSString * const ACImageNameFilterIconAlt AC_SWIFT_PRIVATE = @"filter_icon_alt";

/// The "folder_copy_icon" asset catalog image resource.
static NSString * const ACImageNameFolderCopyIcon AC_SWIFT_PRIVATE = @"folder_copy_icon";

/// The "home.graph" asset catalog image resource.
static NSString * const ACImageNameHomeGraph AC_SWIFT_PRIVATE = @"home.graph";

/// The "home_icon" asset catalog image resource.
static NSString * const ACImageNameHomeIcon AC_SWIFT_PRIVATE = @"home_icon";

/// The "influence_icon" asset catalog image resource.
static NSString * const ACImageNameInfluenceIcon AC_SWIFT_PRIVATE = @"influence_icon";

/// The "load_circle_icon" asset catalog image resource.
static NSString * const ACImageNameLoadCircleIcon AC_SWIFT_PRIVATE = @"load_circle_icon";

/// The "margin_icon" asset catalog image resource.
static NSString * const ACImageNameMarginIcon AC_SWIFT_PRIVATE = @"margin_icon";

/// The "menu_icon" asset catalog image resource.
static NSString * const ACImageNameMenuIcon AC_SWIFT_PRIVATE = @"menu_icon";

/// The "pin_icon" asset catalog image resource.
static NSString * const ACImageNamePinIcon AC_SWIFT_PRIVATE = @"pin_icon";

/// The "pos_icon" asset catalog image resource.
static NSString * const ACImageNamePosIcon AC_SWIFT_PRIVATE = @"pos_icon";

/// The "product_image" asset catalog image resource.
static NSString * const ACImageNameProductImage AC_SWIFT_PRIVATE = @"product_image";

/// The "product_image_1" asset catalog image resource.
static NSString * const ACImageNameProductImage1 AC_SWIFT_PRIVATE = @"product_image_1";

/// The "sale_icon" asset catalog image resource.
static NSString * const ACImageNameSaleIcon AC_SWIFT_PRIVATE = @"sale_icon";

/// The "search_icon" asset catalog image resource.
static NSString * const ACImageNameSearchIcon AC_SWIFT_PRIVATE = @"search_icon";

/// The "sort.icon" asset catalog image resource.
static NSString * const ACImageNameSortIcon AC_SWIFT_PRIVATE = @"sort.icon";

/// The "tab_center" asset catalog image resource.
static NSString * const ACImageNameTabCenter AC_SWIFT_PRIVATE = @"tab_center";

/// The "tab_home" asset catalog image resource.
static NSString * const ACImageNameTabHome AC_SWIFT_PRIVATE = @"tab_home";

/// The "tab_indicator" asset catalog image resource.
static NSString * const ACImageNameTabIndicator AC_SWIFT_PRIVATE = @"tab_indicator";

/// The "tab_medications" asset catalog image resource.
static NSString * const ACImageNameTabMedications AC_SWIFT_PRIVATE = @"tab_medications";

/// The "tab_prescriptions" asset catalog image resource.
static NSString * const ACImageNameTabPrescriptions AC_SWIFT_PRIVATE = @"tab_prescriptions";

/// The "tab_settings" asset catalog image resource.
static NSString * const ACImageNameTabSettings AC_SWIFT_PRIVATE = @"tab_settings";

/// The "trash_icon" asset catalog image resource.
static NSString * const ACImageNameTrashIcon AC_SWIFT_PRIVATE = @"trash_icon";

/// The "user_icon" asset catalog image resource.
static NSString * const ACImageNameUserIcon AC_SWIFT_PRIVATE = @"user_icon";

/// The "view_icon" asset catalog image resource.
static NSString * const ACImageNameViewIcon AC_SWIFT_PRIVATE = @"view_icon";

/// The "wasfa_logo" asset catalog image resource.
static NSString * const ACImageNameWasfaLogo AC_SWIFT_PRIVATE = @"wasfa_logo";

#undef AC_SWIFT_PRIVATE
