<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>colors</key>
	<array>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameAdminMainColor</string>
			<key>relativePath</key>
			<string>./AdminMainColor.colorset</string>
			<key>swiftSymbol</key>
			<string>adminMain</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack900</string>
			<key>relativePath</key>
			<string>./Black900.colorset</string>
			<key>swiftSymbol</key>
			<string>black900</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack90001</string>
			<key>relativePath</key>
			<string>./Black90001.colorset</string>
			<key>swiftSymbol</key>
			<string>black90001</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack9000F</string>
			<key>relativePath</key>
			<string>./Black9000f.colorset</string>
			<key>swiftSymbol</key>
			<string>black9000F</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack9001E</string>
			<key>relativePath</key>
			<string>./Black9001e.colorset</string>
			<key>swiftSymbol</key>
			<string>black9001E</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack90021</string>
			<key>relativePath</key>
			<string>./Black90021.colorset</string>
			<key>swiftSymbol</key>
			<string>black90021</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack90023</string>
			<key>relativePath</key>
			<string>./Black90023.colorset</string>
			<key>swiftSymbol</key>
			<string>black90023</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack9002B</string>
			<key>relativePath</key>
			<string>./Black9002b.colorset</string>
			<key>swiftSymbol</key>
			<string>black9002B</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack9003D</string>
			<key>relativePath</key>
			<string>./Black9003d.colorset</string>
			<key>swiftSymbol</key>
			<string>black9003D</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack9003F</string>
			<key>relativePath</key>
			<string>./Black9003f.colorset</string>
			<key>swiftSymbol</key>
			<string>black9003F</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack90042</string>
			<key>relativePath</key>
			<string>./Black90042.colorset</string>
			<key>swiftSymbol</key>
			<string>black90042</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack90054</string>
			<key>relativePath</key>
			<string>./Black90054.colorset</string>
			<key>swiftSymbol</key>
			<string>black90054</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack9005B</string>
			<key>relativePath</key>
			<string>./Black9005b.colorset</string>
			<key>swiftSymbol</key>
			<string>black9005B</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlack900Bf</string>
			<key>relativePath</key>
			<string>./Black900Bf.colorset</string>
			<key>swiftSymbol</key>
			<string>black900Bf</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlueLight</string>
			<key>relativePath</key>
			<string>./Blue.light.colorset</string>
			<key>swiftSymbol</key>
			<string>blueLight</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlue300</string>
			<key>relativePath</key>
			<string>./Blue300.colorset</string>
			<key>swiftSymbol</key>
			<string>blue300</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlue600</string>
			<key>relativePath</key>
			<string>./Blue600.colorset</string>
			<key>swiftSymbol</key>
			<string>blue600</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlue6002B</string>
			<key>relativePath</key>
			<string>./Blue6002b.colorset</string>
			<key>swiftSymbol</key>
			<string>blue6002B</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlue60038</string>
			<key>relativePath</key>
			<string>./Blue60038.colorset</string>
			<key>swiftSymbol</key>
			<string>blue60038</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlue6003A</string>
			<key>relativePath</key>
			<string>./Blue6003a.colorset</string>
			<key>swiftSymbol</key>
			<string>blue6003A</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlue60054</string>
			<key>relativePath</key>
			<string>./Blue60054.colorset</string>
			<key>swiftSymbol</key>
			<string>blue60054</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlue6009E</string>
			<key>relativePath</key>
			<string>./Blue6009e.colorset</string>
			<key>swiftSymbol</key>
			<string>blue6009E</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlueGray100</string>
			<key>relativePath</key>
			<string>./BlueGray100.colorset</string>
			<key>swiftSymbol</key>
			<string>blueGray100</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlueGray10001</string>
			<key>relativePath</key>
			<string>./BlueGray10001.colorset</string>
			<key>swiftSymbol</key>
			<string>blueGray10001</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlueGray10002</string>
			<key>relativePath</key>
			<string>./BlueGray10002.colorset</string>
			<key>swiftSymbol</key>
			<string>blueGray10002</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameBlueGray900</string>
			<key>relativePath</key>
			<string>./BlueGray900.colorset</string>
			<key>swiftSymbol</key>
			<string>blueGray900</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameCyan4000A</string>
			<key>relativePath</key>
			<string>./Cyan4000a.colorset</string>
			<key>swiftSymbol</key>
			<string>cyan4000A</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameCyan8003F</string>
			<key>relativePath</key>
			<string>./Cyan8003f.colorset</string>
			<key>swiftSymbol</key>
			<string>cyan8003F</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameDescriptionText</string>
			<key>relativePath</key>
			<string>./DescriptionText.colorset</string>
			<key>swiftSymbol</key>
			<string>descriptionText</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameDoctorMainColor</string>
			<key>relativePath</key>
			<string>./DoctorMainColor.colorset</string>
			<key>swiftSymbol</key>
			<string>doctorMain</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameGray20001</string>
			<key>relativePath</key>
			<string>./Gray20001.colorset</string>
			<key>swiftSymbol</key>
			<string>gray20001</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameGray600</string>
			<key>relativePath</key>
			<string>./Gray600.colorset</string>
			<key>swiftSymbol</key>
			<string>gray600</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameGray800</string>
			<key>relativePath</key>
			<string>./Gray800.colorset</string>
			<key>swiftSymbol</key>
			<string>gray800</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameGreen900</string>
			<key>relativePath</key>
			<string>./Green900.colorset</string>
			<key>swiftSymbol</key>
			<string>green900</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameGreenA700</string>
			<key>relativePath</key>
			<string>./GreenA700.colorset</string>
			<key>swiftSymbol</key>
			<string>greenA700</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameLightGreen600</string>
			<key>relativePath</key>
			<string>./LightGreen600.colorset</string>
			<key>swiftSymbol</key>
			<string>lightGreen600</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNamePink300C1</string>
			<key>relativePath</key>
			<string>./Pink300C1.colorset</string>
			<key>swiftSymbol</key>
			<string>pink300C1</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameRed600</string>
			<key>relativePath</key>
			<string>./Red600.colorset</string>
			<key>swiftSymbol</key>
			<string>red600</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameRedA700</string>
			<key>relativePath</key>
			<string>./RedA700.colorset</string>
			<key>swiftSymbol</key>
			<string>redA700</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameTeal900</string>
			<key>relativePath</key>
			<string>./Teal900.colorset</string>
			<key>swiftSymbol</key>
			<string>teal900</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACColorNameWhiteA70093</string>
			<key>relativePath</key>
			<string>./WhiteA70093.colorset</string>
			<key>swiftSymbol</key>
			<string>whiteA70093</string>
		</dict>
	</array>
	<key>images</key>
	<array>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameAddIcon</string>
			<key>relativePath</key>
			<string>./add_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>addIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameAddIconAlt</string>
			<key>relativePath</key>
			<string>./add_icon_alt.imageset</string>
			<key>swiftSymbol</key>
			<string>addIconAlt</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameAdminSaleIconBasket</string>
			<key>relativePath</key>
			<string>./admin_sale_icon_basket.imageset</string>
			<key>swiftSymbol</key>
			<string>adminSaleIconBasket</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameAdminTabAccount</string>
			<key>relativePath</key>
			<string>./admin_tab_account.imageset</string>
			<key>swiftSymbol</key>
			<string>adminTabAccount</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameAdminTabPos</string>
			<key>relativePath</key>
			<string>./admin_tab_pos.imageset</string>
			<key>swiftSymbol</key>
			<string>adminTabPos</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameAdminTabProducts</string>
			<key>relativePath</key>
			<string>./admin_tab_products.imageset</string>
			<key>swiftSymbol</key>
			<string>adminTabProducts</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameAdminTabSale</string>
			<key>relativePath</key>
			<string>./admin_tab_sale.imageset</string>
			<key>swiftSymbol</key>
			<string>adminTabSale</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameArrowLeftIcon</string>
			<key>relativePath</key>
			<string>./arrow_left_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>arrowLeftIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameArrowLeftIconAlt</string>
			<key>relativePath</key>
			<string>./arrow_left_icon_alt.imageset</string>
			<key>swiftSymbol</key>
			<string>arrowLeftIconAlt</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameArrowRightIcon</string>
			<key>relativePath</key>
			<string>./arrow_right_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>arrowRightIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameBellIcon</string>
			<key>relativePath</key>
			<string>./bell_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>bellIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameCardWaveOverlay</string>
			<key>relativePath</key>
			<string>./card_wave_overlay.imageset</string>
			<key>swiftSymbol</key>
			<string>cardWaveOverlay</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameDeleteIcon</string>
			<key>relativePath</key>
			<string>./delete_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>deleteIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameEditIcon</string>
			<key>relativePath</key>
			<string>./edit_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>editIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameFileIcon</string>
			<key>relativePath</key>
			<string>./file_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>fileIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameFilterIcon</string>
			<key>relativePath</key>
			<string>./filter_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>filterIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameFilterIconAlt</string>
			<key>relativePath</key>
			<string>./filter_icon_alt.imageset</string>
			<key>swiftSymbol</key>
			<string>filterIconAlt</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameFolderCopyIcon</string>
			<key>relativePath</key>
			<string>./folder_copy_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>folderCopyIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameHomeGraph</string>
			<key>relativePath</key>
			<string>./home.graph.imageset</string>
			<key>swiftSymbol</key>
			<string>homeGraph</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameHomeIcon</string>
			<key>relativePath</key>
			<string>./home_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>homeIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameInfluenceIcon</string>
			<key>relativePath</key>
			<string>./influence_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>influenceIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameLoadCircleIcon</string>
			<key>relativePath</key>
			<string>./load_circle_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>loadCircleIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameMarginIcon</string>
			<key>relativePath</key>
			<string>./margin_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>marginIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameMenuIcon</string>
			<key>relativePath</key>
			<string>./menu_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>menuIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNamePinIcon</string>
			<key>relativePath</key>
			<string>./pin_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>pinIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNamePosIcon</string>
			<key>relativePath</key>
			<string>./pos_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>posIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameProductImage</string>
			<key>relativePath</key>
			<string>./product_image.imageset</string>
			<key>swiftSymbol</key>
			<string>product</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameProductImage1</string>
			<key>relativePath</key>
			<string>./product_image_1.imageset</string>
			<key>swiftSymbol</key>
			<string>productImage1</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameSaleIcon</string>
			<key>relativePath</key>
			<string>./sale_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>saleIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameSearchIcon</string>
			<key>relativePath</key>
			<string>./search_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>searchIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameSortIcon</string>
			<key>relativePath</key>
			<string>./sort.icon.imageset</string>
			<key>swiftSymbol</key>
			<string>sortIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameTabCenter</string>
			<key>relativePath</key>
			<string>./tab_center.imageset</string>
			<key>swiftSymbol</key>
			<string>tabCenter</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameTabHome</string>
			<key>relativePath</key>
			<string>./tab_home.imageset</string>
			<key>swiftSymbol</key>
			<string>tabHome</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameTabIndicator</string>
			<key>relativePath</key>
			<string>./tab_indicator.imageset</string>
			<key>swiftSymbol</key>
			<string>tabIndicator</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameTabMedications</string>
			<key>relativePath</key>
			<string>./tab_medications.imageset</string>
			<key>swiftSymbol</key>
			<string>tabMedications</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameTabPrescriptions</string>
			<key>relativePath</key>
			<string>./tab_prescriptions.imageset</string>
			<key>swiftSymbol</key>
			<string>tabPrescriptions</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameTabSettings</string>
			<key>relativePath</key>
			<string>./tab_settings.imageset</string>
			<key>swiftSymbol</key>
			<string>tabSettings</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameTrashIcon</string>
			<key>relativePath</key>
			<string>./trash_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>trashIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameUserIcon</string>
			<key>relativePath</key>
			<string>./user_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>userIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameViewIcon</string>
			<key>relativePath</key>
			<string>./view_icon.imageset</string>
			<key>swiftSymbol</key>
			<string>viewIcon</string>
		</dict>
		<dict>
			<key>catalogPath</key>
			<string>/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Resources/Assets.xcassets</string>
			<key>objcSymbol</key>
			<string>ACImageNameWasfaLogo</string>
			<key>relativePath</key>
			<string>./wasfa_logo.imageset</string>
			<key>swiftSymbol</key>
			<string>wasfaLogo</string>
		</dict>
	</array>
	<key>symbols</key>
	<array/>
</dict>
</plist>
