import Foundation
#if canImport(AppKit)
import AppKit
#endif
#if canImport(UIKit)
import UIKit
#endif
#if canImport(SwiftUI)
import Swift<PERSON>
#endif
#if canImport(DeveloperToolsSupport)
import DeveloperToolsSupport
#endif

#if SWIFT_PACKAGE
private let resourceBundle = Foundation.Bundle.module
#else
private class ResourceBundleClass {}
private let resourceBundle = Foundation.Bundle(for: ResourceBundleClass.self)
#endif

// MARK: - Color Symbols -

@available(iOS 17.0, macOS 14.0, tvOS 17.0, watchOS 10.0, *)
extension DeveloperToolsSupport.ColorResource {

    /// The "AdminMainColor" asset catalog color resource.
    static let adminMain = DeveloperToolsSupport.ColorResource(name: "AdminMainColor", bundle: resourceBundle)

    /// The "Black900" asset catalog color resource.
    static let black900 = DeveloperToolsSupport.ColorResource(name: "Black900", bundle: resourceBundle)

    /// The "Black90001" asset catalog color resource.
    static let black90001 = DeveloperToolsSupport.ColorResource(name: "Black90001", bundle: resourceBundle)

    /// The "Black9000f" asset catalog color resource.
    static let black9000F = DeveloperToolsSupport.ColorResource(name: "Black9000f", bundle: resourceBundle)

    /// The "Black9001e" asset catalog color resource.
    static let black9001E = DeveloperToolsSupport.ColorResource(name: "Black9001e", bundle: resourceBundle)

    /// The "Black90021" asset catalog color resource.
    static let black90021 = DeveloperToolsSupport.ColorResource(name: "Black90021", bundle: resourceBundle)

    /// The "Black90023" asset catalog color resource.
    static let black90023 = DeveloperToolsSupport.ColorResource(name: "Black90023", bundle: resourceBundle)

    /// The "Black9002b" asset catalog color resource.
    static let black9002B = DeveloperToolsSupport.ColorResource(name: "Black9002b", bundle: resourceBundle)

    /// The "Black9003d" asset catalog color resource.
    static let black9003D = DeveloperToolsSupport.ColorResource(name: "Black9003d", bundle: resourceBundle)

    /// The "Black9003f" asset catalog color resource.
    static let black9003F = DeveloperToolsSupport.ColorResource(name: "Black9003f", bundle: resourceBundle)

    /// The "Black90042" asset catalog color resource.
    static let black90042 = DeveloperToolsSupport.ColorResource(name: "Black90042", bundle: resourceBundle)

    /// The "Black90054" asset catalog color resource.
    static let black90054 = DeveloperToolsSupport.ColorResource(name: "Black90054", bundle: resourceBundle)

    /// The "Black9005b" asset catalog color resource.
    static let black9005B = DeveloperToolsSupport.ColorResource(name: "Black9005b", bundle: resourceBundle)

    /// The "Black900Bf" asset catalog color resource.
    static let black900Bf = DeveloperToolsSupport.ColorResource(name: "Black900Bf", bundle: resourceBundle)

    /// The "Blue.light" asset catalog color resource.
    static let blueLight = DeveloperToolsSupport.ColorResource(name: "Blue.light", bundle: resourceBundle)

    /// The "Blue300" asset catalog color resource.
    static let blue300 = DeveloperToolsSupport.ColorResource(name: "Blue300", bundle: resourceBundle)

    /// The "Blue600" asset catalog color resource.
    static let blue600 = DeveloperToolsSupport.ColorResource(name: "Blue600", bundle: resourceBundle)

    /// The "Blue6002b" asset catalog color resource.
    static let blue6002B = DeveloperToolsSupport.ColorResource(name: "Blue6002b", bundle: resourceBundle)

    /// The "Blue60038" asset catalog color resource.
    static let blue60038 = DeveloperToolsSupport.ColorResource(name: "Blue60038", bundle: resourceBundle)

    /// The "Blue6003a" asset catalog color resource.
    static let blue6003A = DeveloperToolsSupport.ColorResource(name: "Blue6003a", bundle: resourceBundle)

    /// The "Blue60054" asset catalog color resource.
    static let blue60054 = DeveloperToolsSupport.ColorResource(name: "Blue60054", bundle: resourceBundle)

    /// The "Blue6009e" asset catalog color resource.
    static let blue6009E = DeveloperToolsSupport.ColorResource(name: "Blue6009e", bundle: resourceBundle)

    /// The "BlueGray100" asset catalog color resource.
    static let blueGray100 = DeveloperToolsSupport.ColorResource(name: "BlueGray100", bundle: resourceBundle)

    /// The "BlueGray10001" asset catalog color resource.
    static let blueGray10001 = DeveloperToolsSupport.ColorResource(name: "BlueGray10001", bundle: resourceBundle)

    /// The "BlueGray10002" asset catalog color resource.
    static let blueGray10002 = DeveloperToolsSupport.ColorResource(name: "BlueGray10002", bundle: resourceBundle)

    /// The "BlueGray900" asset catalog color resource.
    static let blueGray900 = DeveloperToolsSupport.ColorResource(name: "BlueGray900", bundle: resourceBundle)

    /// The "Cyan4000a" asset catalog color resource.
    static let cyan4000A = DeveloperToolsSupport.ColorResource(name: "Cyan4000a", bundle: resourceBundle)

    /// The "Cyan8003f" asset catalog color resource.
    static let cyan8003F = DeveloperToolsSupport.ColorResource(name: "Cyan8003f", bundle: resourceBundle)

    /// The "DescriptionText" asset catalog color resource.
    static let descriptionText = DeveloperToolsSupport.ColorResource(name: "DescriptionText", bundle: resourceBundle)

    /// The "DoctorMainColor" asset catalog color resource.
    static let doctorMain = DeveloperToolsSupport.ColorResource(name: "DoctorMainColor", bundle: resourceBundle)

    /// The "Gray20001" asset catalog color resource.
    static let gray20001 = DeveloperToolsSupport.ColorResource(name: "Gray20001", bundle: resourceBundle)

    /// The "Gray600" asset catalog color resource.
    static let gray600 = DeveloperToolsSupport.ColorResource(name: "Gray600", bundle: resourceBundle)

    /// The "Gray800" asset catalog color resource.
    static let gray800 = DeveloperToolsSupport.ColorResource(name: "Gray800", bundle: resourceBundle)

    /// The "Green900" asset catalog color resource.
    static let green900 = DeveloperToolsSupport.ColorResource(name: "Green900", bundle: resourceBundle)

    /// The "GreenA700" asset catalog color resource.
    static let greenA700 = DeveloperToolsSupport.ColorResource(name: "GreenA700", bundle: resourceBundle)

    /// The "LightGreen600" asset catalog color resource.
    static let lightGreen600 = DeveloperToolsSupport.ColorResource(name: "LightGreen600", bundle: resourceBundle)

    /// The "Pink300C1" asset catalog color resource.
    static let pink300C1 = DeveloperToolsSupport.ColorResource(name: "Pink300C1", bundle: resourceBundle)

    /// The "Red600" asset catalog color resource.
    static let red600 = DeveloperToolsSupport.ColorResource(name: "Red600", bundle: resourceBundle)

    /// The "RedA700" asset catalog color resource.
    static let redA700 = DeveloperToolsSupport.ColorResource(name: "RedA700", bundle: resourceBundle)

    /// The "Teal900" asset catalog color resource.
    static let teal900 = DeveloperToolsSupport.ColorResource(name: "Teal900", bundle: resourceBundle)

    /// The "WhiteA70093" asset catalog color resource.
    static let whiteA70093 = DeveloperToolsSupport.ColorResource(name: "WhiteA70093", bundle: resourceBundle)

}

// MARK: - Image Symbols -

@available(iOS 17.0, macOS 14.0, tvOS 17.0, watchOS 10.0, *)
extension DeveloperToolsSupport.ImageResource {

    /// The "add_icon" asset catalog image resource.
    static let addIcon = DeveloperToolsSupport.ImageResource(name: "add_icon", bundle: resourceBundle)

    /// The "add_icon_alt" asset catalog image resource.
    static let addIconAlt = DeveloperToolsSupport.ImageResource(name: "add_icon_alt", bundle: resourceBundle)

    /// The "admin_sale_icon_basket" asset catalog image resource.
    static let adminSaleIconBasket = DeveloperToolsSupport.ImageResource(name: "admin_sale_icon_basket", bundle: resourceBundle)

    /// The "admin_tab_account" asset catalog image resource.
    static let adminTabAccount = DeveloperToolsSupport.ImageResource(name: "admin_tab_account", bundle: resourceBundle)

    /// The "admin_tab_pos" asset catalog image resource.
    static let adminTabPos = DeveloperToolsSupport.ImageResource(name: "admin_tab_pos", bundle: resourceBundle)

    /// The "admin_tab_products" asset catalog image resource.
    static let adminTabProducts = DeveloperToolsSupport.ImageResource(name: "admin_tab_products", bundle: resourceBundle)

    /// The "admin_tab_sale" asset catalog image resource.
    static let adminTabSale = DeveloperToolsSupport.ImageResource(name: "admin_tab_sale", bundle: resourceBundle)

    /// The "arrow_left_icon" asset catalog image resource.
    static let arrowLeftIcon = DeveloperToolsSupport.ImageResource(name: "arrow_left_icon", bundle: resourceBundle)

    /// The "arrow_left_icon_alt" asset catalog image resource.
    static let arrowLeftIconAlt = DeveloperToolsSupport.ImageResource(name: "arrow_left_icon_alt", bundle: resourceBundle)

    /// The "arrow_right_icon" asset catalog image resource.
    static let arrowRightIcon = DeveloperToolsSupport.ImageResource(name: "arrow_right_icon", bundle: resourceBundle)

    /// The "bell_icon" asset catalog image resource.
    static let bellIcon = DeveloperToolsSupport.ImageResource(name: "bell_icon", bundle: resourceBundle)

    /// The "card_wave_overlay" asset catalog image resource.
    static let cardWaveOverlay = DeveloperToolsSupport.ImageResource(name: "card_wave_overlay", bundle: resourceBundle)

    /// The "delete_icon" asset catalog image resource.
    static let deleteIcon = DeveloperToolsSupport.ImageResource(name: "delete_icon", bundle: resourceBundle)

    /// The "edit_icon" asset catalog image resource.
    static let editIcon = DeveloperToolsSupport.ImageResource(name: "edit_icon", bundle: resourceBundle)

    /// The "file_icon" asset catalog image resource.
    static let fileIcon = DeveloperToolsSupport.ImageResource(name: "file_icon", bundle: resourceBundle)

    /// The "filter_icon" asset catalog image resource.
    static let filterIcon = DeveloperToolsSupport.ImageResource(name: "filter_icon", bundle: resourceBundle)

    /// The "filter_icon_alt" asset catalog image resource.
    static let filterIconAlt = DeveloperToolsSupport.ImageResource(name: "filter_icon_alt", bundle: resourceBundle)

    /// The "folder_copy_icon" asset catalog image resource.
    static let folderCopyIcon = DeveloperToolsSupport.ImageResource(name: "folder_copy_icon", bundle: resourceBundle)

    /// The "home.graph" asset catalog image resource.
    static let homeGraph = DeveloperToolsSupport.ImageResource(name: "home.graph", bundle: resourceBundle)

    /// The "home_icon" asset catalog image resource.
    static let homeIcon = DeveloperToolsSupport.ImageResource(name: "home_icon", bundle: resourceBundle)

    /// The "influence_icon" asset catalog image resource.
    static let influenceIcon = DeveloperToolsSupport.ImageResource(name: "influence_icon", bundle: resourceBundle)

    /// The "load_circle_icon" asset catalog image resource.
    static let loadCircleIcon = DeveloperToolsSupport.ImageResource(name: "load_circle_icon", bundle: resourceBundle)

    /// The "margin_icon" asset catalog image resource.
    static let marginIcon = DeveloperToolsSupport.ImageResource(name: "margin_icon", bundle: resourceBundle)

    /// The "menu_icon" asset catalog image resource.
    static let menuIcon = DeveloperToolsSupport.ImageResource(name: "menu_icon", bundle: resourceBundle)

    /// The "pin_icon" asset catalog image resource.
    static let pinIcon = DeveloperToolsSupport.ImageResource(name: "pin_icon", bundle: resourceBundle)

    /// The "pos_icon" asset catalog image resource.
    static let posIcon = DeveloperToolsSupport.ImageResource(name: "pos_icon", bundle: resourceBundle)

    /// The "product_image" asset catalog image resource.
    static let product = DeveloperToolsSupport.ImageResource(name: "product_image", bundle: resourceBundle)

    /// The "product_image_1" asset catalog image resource.
    static let productImage1 = DeveloperToolsSupport.ImageResource(name: "product_image_1", bundle: resourceBundle)

    /// The "sale_icon" asset catalog image resource.
    static let saleIcon = DeveloperToolsSupport.ImageResource(name: "sale_icon", bundle: resourceBundle)

    /// The "search_icon" asset catalog image resource.
    static let searchIcon = DeveloperToolsSupport.ImageResource(name: "search_icon", bundle: resourceBundle)

    /// The "sort.icon" asset catalog image resource.
    static let sortIcon = DeveloperToolsSupport.ImageResource(name: "sort.icon", bundle: resourceBundle)

    /// The "tab_center" asset catalog image resource.
    static let tabCenter = DeveloperToolsSupport.ImageResource(name: "tab_center", bundle: resourceBundle)

    /// The "tab_home" asset catalog image resource.
    static let tabHome = DeveloperToolsSupport.ImageResource(name: "tab_home", bundle: resourceBundle)

    /// The "tab_indicator" asset catalog image resource.
    static let tabIndicator = DeveloperToolsSupport.ImageResource(name: "tab_indicator", bundle: resourceBundle)

    /// The "tab_medications" asset catalog image resource.
    static let tabMedications = DeveloperToolsSupport.ImageResource(name: "tab_medications", bundle: resourceBundle)

    /// The "tab_prescriptions" asset catalog image resource.
    static let tabPrescriptions = DeveloperToolsSupport.ImageResource(name: "tab_prescriptions", bundle: resourceBundle)

    /// The "tab_settings" asset catalog image resource.
    static let tabSettings = DeveloperToolsSupport.ImageResource(name: "tab_settings", bundle: resourceBundle)

    /// The "trash_icon" asset catalog image resource.
    static let trashIcon = DeveloperToolsSupport.ImageResource(name: "trash_icon", bundle: resourceBundle)

    /// The "user_icon" asset catalog image resource.
    static let userIcon = DeveloperToolsSupport.ImageResource(name: "user_icon", bundle: resourceBundle)

    /// The "view_icon" asset catalog image resource.
    static let viewIcon = DeveloperToolsSupport.ImageResource(name: "view_icon", bundle: resourceBundle)

    /// The "wasfa_logo" asset catalog image resource.
    static let wasfaLogo = DeveloperToolsSupport.ImageResource(name: "wasfa_logo", bundle: resourceBundle)

}

// MARK: - Color Symbol Extensions -

#if canImport(AppKit)
@available(macOS 14.0, *)
@available(macCatalyst, unavailable)
extension AppKit.NSColor {

    /// The "AdminMainColor" asset catalog color.
    static var adminMain: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .adminMain)
#else
        .init()
#endif
    }

    /// The "Black900" asset catalog color.
    static var black900: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black900)
#else
        .init()
#endif
    }

    /// The "Black90001" asset catalog color.
    static var black90001: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black90001)
#else
        .init()
#endif
    }

    /// The "Black9000f" asset catalog color.
    static var black9000F: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black9000F)
#else
        .init()
#endif
    }

    /// The "Black9001e" asset catalog color.
    static var black9001E: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black9001E)
#else
        .init()
#endif
    }

    /// The "Black90021" asset catalog color.
    static var black90021: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black90021)
#else
        .init()
#endif
    }

    /// The "Black90023" asset catalog color.
    static var black90023: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black90023)
#else
        .init()
#endif
    }

    /// The "Black9002b" asset catalog color.
    static var black9002B: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black9002B)
#else
        .init()
#endif
    }

    /// The "Black9003d" asset catalog color.
    static var black9003D: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black9003D)
#else
        .init()
#endif
    }

    /// The "Black9003f" asset catalog color.
    static var black9003F: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black9003F)
#else
        .init()
#endif
    }

    /// The "Black90042" asset catalog color.
    static var black90042: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black90042)
#else
        .init()
#endif
    }

    /// The "Black90054" asset catalog color.
    static var black90054: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black90054)
#else
        .init()
#endif
    }

    /// The "Black9005b" asset catalog color.
    static var black9005B: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black9005B)
#else
        .init()
#endif
    }

    /// The "Black900Bf" asset catalog color.
    static var black900Bf: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .black900Bf)
#else
        .init()
#endif
    }

    /// The "Blue.light" asset catalog color.
    static var blueLight: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blueLight)
#else
        .init()
#endif
    }

    /// The "Blue300" asset catalog color.
    static var blue300: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blue300)
#else
        .init()
#endif
    }

    /// The "Blue600" asset catalog color.
    static var blue600: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blue600)
#else
        .init()
#endif
    }

    /// The "Blue6002b" asset catalog color.
    static var blue6002B: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blue6002B)
#else
        .init()
#endif
    }

    /// The "Blue60038" asset catalog color.
    static var blue60038: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blue60038)
#else
        .init()
#endif
    }

    /// The "Blue6003a" asset catalog color.
    static var blue6003A: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blue6003A)
#else
        .init()
#endif
    }

    /// The "Blue60054" asset catalog color.
    static var blue60054: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blue60054)
#else
        .init()
#endif
    }

    /// The "Blue6009e" asset catalog color.
    static var blue6009E: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blue6009E)
#else
        .init()
#endif
    }

    /// The "BlueGray100" asset catalog color.
    static var blueGray100: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blueGray100)
#else
        .init()
#endif
    }

    /// The "BlueGray10001" asset catalog color.
    static var blueGray10001: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blueGray10001)
#else
        .init()
#endif
    }

    /// The "BlueGray10002" asset catalog color.
    static var blueGray10002: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blueGray10002)
#else
        .init()
#endif
    }

    /// The "BlueGray900" asset catalog color.
    static var blueGray900: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .blueGray900)
#else
        .init()
#endif
    }

    /// The "Cyan4000a" asset catalog color.
    static var cyan4000A: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .cyan4000A)
#else
        .init()
#endif
    }

    /// The "Cyan8003f" asset catalog color.
    static var cyan8003F: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .cyan8003F)
#else
        .init()
#endif
    }

    /// The "DescriptionText" asset catalog color.
    static var descriptionText: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .descriptionText)
#else
        .init()
#endif
    }

    /// The "DoctorMainColor" asset catalog color.
    static var doctorMain: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .doctorMain)
#else
        .init()
#endif
    }

    /// The "Gray20001" asset catalog color.
    static var gray20001: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .gray20001)
#else
        .init()
#endif
    }

    /// The "Gray600" asset catalog color.
    static var gray600: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .gray600)
#else
        .init()
#endif
    }

    /// The "Gray800" asset catalog color.
    static var gray800: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .gray800)
#else
        .init()
#endif
    }

    /// The "Green900" asset catalog color.
    static var green900: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .green900)
#else
        .init()
#endif
    }

    /// The "GreenA700" asset catalog color.
    static var greenA700: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .greenA700)
#else
        .init()
#endif
    }

    /// The "LightGreen600" asset catalog color.
    static var lightGreen600: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .lightGreen600)
#else
        .init()
#endif
    }

    /// The "Pink300C1" asset catalog color.
    static var pink300C1: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .pink300C1)
#else
        .init()
#endif
    }

    /// The "Red600" asset catalog color.
    static var red600: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .red600)
#else
        .init()
#endif
    }

    /// The "RedA700" asset catalog color.
    static var redA700: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .redA700)
#else
        .init()
#endif
    }

    /// The "Teal900" asset catalog color.
    static var teal900: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .teal900)
#else
        .init()
#endif
    }

    /// The "WhiteA70093" asset catalog color.
    static var whiteA70093: AppKit.NSColor {
#if !targetEnvironment(macCatalyst)
        .init(resource: .whiteA70093)
#else
        .init()
#endif
    }

}
#endif

#if canImport(UIKit)
@available(iOS 17.0, tvOS 17.0, *)
@available(watchOS, unavailable)
extension UIKit.UIColor {

    /// The "AdminMainColor" asset catalog color.
    static var adminMain: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .adminMain)
#else
        .init()
#endif
    }

    /// The "Black900" asset catalog color.
    static var black900: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black900)
#else
        .init()
#endif
    }

    /// The "Black90001" asset catalog color.
    static var black90001: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black90001)
#else
        .init()
#endif
    }

    /// The "Black9000f" asset catalog color.
    static var black9000F: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black9000F)
#else
        .init()
#endif
    }

    /// The "Black9001e" asset catalog color.
    static var black9001E: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black9001E)
#else
        .init()
#endif
    }

    /// The "Black90021" asset catalog color.
    static var black90021: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black90021)
#else
        .init()
#endif
    }

    /// The "Black90023" asset catalog color.
    static var black90023: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black90023)
#else
        .init()
#endif
    }

    /// The "Black9002b" asset catalog color.
    static var black9002B: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black9002B)
#else
        .init()
#endif
    }

    /// The "Black9003d" asset catalog color.
    static var black9003D: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black9003D)
#else
        .init()
#endif
    }

    /// The "Black9003f" asset catalog color.
    static var black9003F: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black9003F)
#else
        .init()
#endif
    }

    /// The "Black90042" asset catalog color.
    static var black90042: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black90042)
#else
        .init()
#endif
    }

    /// The "Black90054" asset catalog color.
    static var black90054: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black90054)
#else
        .init()
#endif
    }

    /// The "Black9005b" asset catalog color.
    static var black9005B: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black9005B)
#else
        .init()
#endif
    }

    /// The "Black900Bf" asset catalog color.
    static var black900Bf: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .black900Bf)
#else
        .init()
#endif
    }

    /// The "Blue.light" asset catalog color.
    static var blueLight: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blueLight)
#else
        .init()
#endif
    }

    /// The "Blue300" asset catalog color.
    static var blue300: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blue300)
#else
        .init()
#endif
    }

    /// The "Blue600" asset catalog color.
    static var blue600: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blue600)
#else
        .init()
#endif
    }

    /// The "Blue6002b" asset catalog color.
    static var blue6002B: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blue6002B)
#else
        .init()
#endif
    }

    /// The "Blue60038" asset catalog color.
    static var blue60038: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blue60038)
#else
        .init()
#endif
    }

    /// The "Blue6003a" asset catalog color.
    static var blue6003A: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blue6003A)
#else
        .init()
#endif
    }

    /// The "Blue60054" asset catalog color.
    static var blue60054: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blue60054)
#else
        .init()
#endif
    }

    /// The "Blue6009e" asset catalog color.
    static var blue6009E: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blue6009E)
#else
        .init()
#endif
    }

    /// The "BlueGray100" asset catalog color.
    static var blueGray100: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blueGray100)
#else
        .init()
#endif
    }

    /// The "BlueGray10001" asset catalog color.
    static var blueGray10001: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blueGray10001)
#else
        .init()
#endif
    }

    /// The "BlueGray10002" asset catalog color.
    static var blueGray10002: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blueGray10002)
#else
        .init()
#endif
    }

    /// The "BlueGray900" asset catalog color.
    static var blueGray900: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .blueGray900)
#else
        .init()
#endif
    }

    /// The "Cyan4000a" asset catalog color.
    static var cyan4000A: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .cyan4000A)
#else
        .init()
#endif
    }

    /// The "Cyan8003f" asset catalog color.
    static var cyan8003F: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .cyan8003F)
#else
        .init()
#endif
    }

    /// The "DescriptionText" asset catalog color.
    static var descriptionText: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .descriptionText)
#else
        .init()
#endif
    }

    /// The "DoctorMainColor" asset catalog color.
    static var doctorMain: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .doctorMain)
#else
        .init()
#endif
    }

    /// The "Gray20001" asset catalog color.
    static var gray20001: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .gray20001)
#else
        .init()
#endif
    }

    /// The "Gray600" asset catalog color.
    static var gray600: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .gray600)
#else
        .init()
#endif
    }

    /// The "Gray800" asset catalog color.
    static var gray800: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .gray800)
#else
        .init()
#endif
    }

    /// The "Green900" asset catalog color.
    static var green900: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .green900)
#else
        .init()
#endif
    }

    /// The "GreenA700" asset catalog color.
    static var greenA700: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .greenA700)
#else
        .init()
#endif
    }

    /// The "LightGreen600" asset catalog color.
    static var lightGreen600: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .lightGreen600)
#else
        .init()
#endif
    }

    /// The "Pink300C1" asset catalog color.
    static var pink300C1: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .pink300C1)
#else
        .init()
#endif
    }

    /// The "Red600" asset catalog color.
    static var red600: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .red600)
#else
        .init()
#endif
    }

    /// The "RedA700" asset catalog color.
    static var redA700: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .redA700)
#else
        .init()
#endif
    }

    /// The "Teal900" asset catalog color.
    static var teal900: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .teal900)
#else
        .init()
#endif
    }

    /// The "WhiteA70093" asset catalog color.
    static var whiteA70093: UIKit.UIColor {
#if !os(watchOS)
        .init(resource: .whiteA70093)
#else
        .init()
#endif
    }

}
#endif

#if canImport(SwiftUI)
@available(iOS 17.0, macOS 14.0, tvOS 17.0, watchOS 10.0, *)
extension SwiftUI.Color {

    /// The "AdminMainColor" asset catalog color.
    static var adminMain: SwiftUI.Color { .init(.adminMain) }

    /// The "Black900" asset catalog color.
    static var black900: SwiftUI.Color { .init(.black900) }

    /// The "Black90001" asset catalog color.
    static var black90001: SwiftUI.Color { .init(.black90001) }

    /// The "Black9000f" asset catalog color.
    static var black9000F: SwiftUI.Color { .init(.black9000F) }

    /// The "Black9001e" asset catalog color.
    static var black9001E: SwiftUI.Color { .init(.black9001E) }

    /// The "Black90021" asset catalog color.
    static var black90021: SwiftUI.Color { .init(.black90021) }

    /// The "Black90023" asset catalog color.
    static var black90023: SwiftUI.Color { .init(.black90023) }

    /// The "Black9002b" asset catalog color.
    static var black9002B: SwiftUI.Color { .init(.black9002B) }

    /// The "Black9003d" asset catalog color.
    static var black9003D: SwiftUI.Color { .init(.black9003D) }

    /// The "Black9003f" asset catalog color.
    static var black9003F: SwiftUI.Color { .init(.black9003F) }

    /// The "Black90042" asset catalog color.
    static var black90042: SwiftUI.Color { .init(.black90042) }

    /// The "Black90054" asset catalog color.
    static var black90054: SwiftUI.Color { .init(.black90054) }

    /// The "Black9005b" asset catalog color.
    static var black9005B: SwiftUI.Color { .init(.black9005B) }

    /// The "Black900Bf" asset catalog color.
    static var black900Bf: SwiftUI.Color { .init(.black900Bf) }

    /// The "Blue.light" asset catalog color.
    static var blueLight: SwiftUI.Color { .init(.blueLight) }

    /// The "Blue300" asset catalog color.
    static var blue300: SwiftUI.Color { .init(.blue300) }

    /// The "Blue600" asset catalog color.
    static var blue600: SwiftUI.Color { .init(.blue600) }

    /// The "Blue6002b" asset catalog color.
    static var blue6002B: SwiftUI.Color { .init(.blue6002B) }

    /// The "Blue60038" asset catalog color.
    static var blue60038: SwiftUI.Color { .init(.blue60038) }

    /// The "Blue6003a" asset catalog color.
    static var blue6003A: SwiftUI.Color { .init(.blue6003A) }

    /// The "Blue60054" asset catalog color.
    static var blue60054: SwiftUI.Color { .init(.blue60054) }

    /// The "Blue6009e" asset catalog color.
    static var blue6009E: SwiftUI.Color { .init(.blue6009E) }

    /// The "BlueGray100" asset catalog color.
    static var blueGray100: SwiftUI.Color { .init(.blueGray100) }

    /// The "BlueGray10001" asset catalog color.
    static var blueGray10001: SwiftUI.Color { .init(.blueGray10001) }

    /// The "BlueGray10002" asset catalog color.
    static var blueGray10002: SwiftUI.Color { .init(.blueGray10002) }

    /// The "BlueGray900" asset catalog color.
    static var blueGray900: SwiftUI.Color { .init(.blueGray900) }

    /// The "Cyan4000a" asset catalog color.
    static var cyan4000A: SwiftUI.Color { .init(.cyan4000A) }

    /// The "Cyan8003f" asset catalog color.
    static var cyan8003F: SwiftUI.Color { .init(.cyan8003F) }

    /// The "DescriptionText" asset catalog color.
    static var descriptionText: SwiftUI.Color { .init(.descriptionText) }

    /// The "DoctorMainColor" asset catalog color.
    static var doctorMain: SwiftUI.Color { .init(.doctorMain) }

    /// The "Gray20001" asset catalog color.
    static var gray20001: SwiftUI.Color { .init(.gray20001) }

    /// The "Gray600" asset catalog color.
    static var gray600: SwiftUI.Color { .init(.gray600) }

    /// The "Gray800" asset catalog color.
    static var gray800: SwiftUI.Color { .init(.gray800) }

    /// The "Green900" asset catalog color.
    static var green900: SwiftUI.Color { .init(.green900) }

    /// The "GreenA700" asset catalog color.
    static var greenA700: SwiftUI.Color { .init(.greenA700) }

    /// The "LightGreen600" asset catalog color.
    static var lightGreen600: SwiftUI.Color { .init(.lightGreen600) }

    /// The "Pink300C1" asset catalog color.
    static var pink300C1: SwiftUI.Color { .init(.pink300C1) }

    /// The "Red600" asset catalog color.
    static var red600: SwiftUI.Color { .init(.red600) }

    /// The "RedA700" asset catalog color.
    static var redA700: SwiftUI.Color { .init(.redA700) }

    /// The "Teal900" asset catalog color.
    static var teal900: SwiftUI.Color { .init(.teal900) }

    /// The "WhiteA70093" asset catalog color.
    static var whiteA70093: SwiftUI.Color { .init(.whiteA70093) }

}

@available(iOS 17.0, macOS 14.0, tvOS 17.0, watchOS 10.0, *)
extension SwiftUI.ShapeStyle where Self == SwiftUI.Color {

    /// The "AdminMainColor" asset catalog color.
    static var adminMain: SwiftUI.Color { .init(.adminMain) }

    /// The "Black900" asset catalog color.
    static var black900: SwiftUI.Color { .init(.black900) }

    /// The "Black90001" asset catalog color.
    static var black90001: SwiftUI.Color { .init(.black90001) }

    /// The "Black9000f" asset catalog color.
    static var black9000F: SwiftUI.Color { .init(.black9000F) }

    /// The "Black9001e" asset catalog color.
    static var black9001E: SwiftUI.Color { .init(.black9001E) }

    /// The "Black90021" asset catalog color.
    static var black90021: SwiftUI.Color { .init(.black90021) }

    /// The "Black90023" asset catalog color.
    static var black90023: SwiftUI.Color { .init(.black90023) }

    /// The "Black9002b" asset catalog color.
    static var black9002B: SwiftUI.Color { .init(.black9002B) }

    /// The "Black9003d" asset catalog color.
    static var black9003D: SwiftUI.Color { .init(.black9003D) }

    /// The "Black9003f" asset catalog color.
    static var black9003F: SwiftUI.Color { .init(.black9003F) }

    /// The "Black90042" asset catalog color.
    static var black90042: SwiftUI.Color { .init(.black90042) }

    /// The "Black90054" asset catalog color.
    static var black90054: SwiftUI.Color { .init(.black90054) }

    /// The "Black9005b" asset catalog color.
    static var black9005B: SwiftUI.Color { .init(.black9005B) }

    /// The "Black900Bf" asset catalog color.
    static var black900Bf: SwiftUI.Color { .init(.black900Bf) }

    /// The "Blue.light" asset catalog color.
    static var blueLight: SwiftUI.Color { .init(.blueLight) }

    /// The "Blue300" asset catalog color.
    static var blue300: SwiftUI.Color { .init(.blue300) }

    /// The "Blue600" asset catalog color.
    static var blue600: SwiftUI.Color { .init(.blue600) }

    /// The "Blue6002b" asset catalog color.
    static var blue6002B: SwiftUI.Color { .init(.blue6002B) }

    /// The "Blue60038" asset catalog color.
    static var blue60038: SwiftUI.Color { .init(.blue60038) }

    /// The "Blue6003a" asset catalog color.
    static var blue6003A: SwiftUI.Color { .init(.blue6003A) }

    /// The "Blue60054" asset catalog color.
    static var blue60054: SwiftUI.Color { .init(.blue60054) }

    /// The "Blue6009e" asset catalog color.
    static var blue6009E: SwiftUI.Color { .init(.blue6009E) }

    /// The "BlueGray100" asset catalog color.
    static var blueGray100: SwiftUI.Color { .init(.blueGray100) }

    /// The "BlueGray10001" asset catalog color.
    static var blueGray10001: SwiftUI.Color { .init(.blueGray10001) }

    /// The "BlueGray10002" asset catalog color.
    static var blueGray10002: SwiftUI.Color { .init(.blueGray10002) }

    /// The "BlueGray900" asset catalog color.
    static var blueGray900: SwiftUI.Color { .init(.blueGray900) }

    /// The "Cyan4000a" asset catalog color.
    static var cyan4000A: SwiftUI.Color { .init(.cyan4000A) }

    /// The "Cyan8003f" asset catalog color.
    static var cyan8003F: SwiftUI.Color { .init(.cyan8003F) }

    /// The "DescriptionText" asset catalog color.
    static var descriptionText: SwiftUI.Color { .init(.descriptionText) }

    /// The "DoctorMainColor" asset catalog color.
    static var doctorMain: SwiftUI.Color { .init(.doctorMain) }

    /// The "Gray20001" asset catalog color.
    static var gray20001: SwiftUI.Color { .init(.gray20001) }

    /// The "Gray600" asset catalog color.
    static var gray600: SwiftUI.Color { .init(.gray600) }

    /// The "Gray800" asset catalog color.
    static var gray800: SwiftUI.Color { .init(.gray800) }

    /// The "Green900" asset catalog color.
    static var green900: SwiftUI.Color { .init(.green900) }

    /// The "GreenA700" asset catalog color.
    static var greenA700: SwiftUI.Color { .init(.greenA700) }

    /// The "LightGreen600" asset catalog color.
    static var lightGreen600: SwiftUI.Color { .init(.lightGreen600) }

    /// The "Pink300C1" asset catalog color.
    static var pink300C1: SwiftUI.Color { .init(.pink300C1) }

    /// The "Red600" asset catalog color.
    static var red600: SwiftUI.Color { .init(.red600) }

    /// The "RedA700" asset catalog color.
    static var redA700: SwiftUI.Color { .init(.redA700) }

    /// The "Teal900" asset catalog color.
    static var teal900: SwiftUI.Color { .init(.teal900) }

    /// The "WhiteA70093" asset catalog color.
    static var whiteA70093: SwiftUI.Color { .init(.whiteA70093) }

}
#endif

// MARK: - Image Symbol Extensions -

#if canImport(AppKit)
@available(macOS 14.0, *)
@available(macCatalyst, unavailable)
extension AppKit.NSImage {

    /// The "add_icon" asset catalog image.
    static var addIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .addIcon)
#else
        .init()
#endif
    }

    /// The "add_icon_alt" asset catalog image.
    static var addIconAlt: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .addIconAlt)
#else
        .init()
#endif
    }

    /// The "admin_sale_icon_basket" asset catalog image.
    static var adminSaleIconBasket: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .adminSaleIconBasket)
#else
        .init()
#endif
    }

    /// The "admin_tab_account" asset catalog image.
    static var adminTabAccount: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .adminTabAccount)
#else
        .init()
#endif
    }

    /// The "admin_tab_pos" asset catalog image.
    static var adminTabPos: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .adminTabPos)
#else
        .init()
#endif
    }

    /// The "admin_tab_products" asset catalog image.
    static var adminTabProducts: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .adminTabProducts)
#else
        .init()
#endif
    }

    /// The "admin_tab_sale" asset catalog image.
    static var adminTabSale: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .adminTabSale)
#else
        .init()
#endif
    }

    /// The "arrow_left_icon" asset catalog image.
    static var arrowLeftIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .arrowLeftIcon)
#else
        .init()
#endif
    }

    /// The "arrow_left_icon_alt" asset catalog image.
    static var arrowLeftIconAlt: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .arrowLeftIconAlt)
#else
        .init()
#endif
    }

    /// The "arrow_right_icon" asset catalog image.
    static var arrowRightIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .arrowRightIcon)
#else
        .init()
#endif
    }

    /// The "bell_icon" asset catalog image.
    static var bellIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .bellIcon)
#else
        .init()
#endif
    }

    /// The "card_wave_overlay" asset catalog image.
    static var cardWaveOverlay: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .cardWaveOverlay)
#else
        .init()
#endif
    }

    /// The "delete_icon" asset catalog image.
    static var deleteIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .deleteIcon)
#else
        .init()
#endif
    }

    /// The "edit_icon" asset catalog image.
    static var editIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .editIcon)
#else
        .init()
#endif
    }

    /// The "file_icon" asset catalog image.
    static var fileIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .fileIcon)
#else
        .init()
#endif
    }

    /// The "filter_icon" asset catalog image.
    static var filterIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .filterIcon)
#else
        .init()
#endif
    }

    /// The "filter_icon_alt" asset catalog image.
    static var filterIconAlt: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .filterIconAlt)
#else
        .init()
#endif
    }

    /// The "folder_copy_icon" asset catalog image.
    static var folderCopyIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .folderCopyIcon)
#else
        .init()
#endif
    }

    /// The "home.graph" asset catalog image.
    static var homeGraph: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .homeGraph)
#else
        .init()
#endif
    }

    /// The "home_icon" asset catalog image.
    static var homeIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .homeIcon)
#else
        .init()
#endif
    }

    /// The "influence_icon" asset catalog image.
    static var influenceIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .influenceIcon)
#else
        .init()
#endif
    }

    /// The "load_circle_icon" asset catalog image.
    static var loadCircleIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .loadCircleIcon)
#else
        .init()
#endif
    }

    /// The "margin_icon" asset catalog image.
    static var marginIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .marginIcon)
#else
        .init()
#endif
    }

    /// The "menu_icon" asset catalog image.
    static var menuIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .menuIcon)
#else
        .init()
#endif
    }

    /// The "pin_icon" asset catalog image.
    static var pinIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .pinIcon)
#else
        .init()
#endif
    }

    /// The "pos_icon" asset catalog image.
    static var posIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .posIcon)
#else
        .init()
#endif
    }

    /// The "product_image" asset catalog image.
    static var product: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .product)
#else
        .init()
#endif
    }

    /// The "product_image_1" asset catalog image.
    static var productImage1: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .productImage1)
#else
        .init()
#endif
    }

    /// The "sale_icon" asset catalog image.
    static var saleIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .saleIcon)
#else
        .init()
#endif
    }

    /// The "search_icon" asset catalog image.
    static var searchIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .searchIcon)
#else
        .init()
#endif
    }

    /// The "sort.icon" asset catalog image.
    static var sortIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .sortIcon)
#else
        .init()
#endif
    }

    /// The "tab_center" asset catalog image.
    static var tabCenter: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .tabCenter)
#else
        .init()
#endif
    }

    /// The "tab_home" asset catalog image.
    static var tabHome: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .tabHome)
#else
        .init()
#endif
    }

    /// The "tab_indicator" asset catalog image.
    static var tabIndicator: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .tabIndicator)
#else
        .init()
#endif
    }

    /// The "tab_medications" asset catalog image.
    static var tabMedications: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .tabMedications)
#else
        .init()
#endif
    }

    /// The "tab_prescriptions" asset catalog image.
    static var tabPrescriptions: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .tabPrescriptions)
#else
        .init()
#endif
    }

    /// The "tab_settings" asset catalog image.
    static var tabSettings: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .tabSettings)
#else
        .init()
#endif
    }

    /// The "trash_icon" asset catalog image.
    static var trashIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .trashIcon)
#else
        .init()
#endif
    }

    /// The "user_icon" asset catalog image.
    static var userIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .userIcon)
#else
        .init()
#endif
    }

    /// The "view_icon" asset catalog image.
    static var viewIcon: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .viewIcon)
#else
        .init()
#endif
    }

    /// The "wasfa_logo" asset catalog image.
    static var wasfaLogo: AppKit.NSImage {
#if !targetEnvironment(macCatalyst)
        .init(resource: .wasfaLogo)
#else
        .init()
#endif
    }

}
#endif

#if canImport(UIKit)
@available(iOS 17.0, tvOS 17.0, *)
@available(watchOS, unavailable)
extension UIKit.UIImage {

    /// The "add_icon" asset catalog image.
    static var addIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .addIcon)
#else
        .init()
#endif
    }

    /// The "add_icon_alt" asset catalog image.
    static var addIconAlt: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .addIconAlt)
#else
        .init()
#endif
    }

    /// The "admin_sale_icon_basket" asset catalog image.
    static var adminSaleIconBasket: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .adminSaleIconBasket)
#else
        .init()
#endif
    }

    /// The "admin_tab_account" asset catalog image.
    static var adminTabAccount: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .adminTabAccount)
#else
        .init()
#endif
    }

    /// The "admin_tab_pos" asset catalog image.
    static var adminTabPos: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .adminTabPos)
#else
        .init()
#endif
    }

    /// The "admin_tab_products" asset catalog image.
    static var adminTabProducts: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .adminTabProducts)
#else
        .init()
#endif
    }

    /// The "admin_tab_sale" asset catalog image.
    static var adminTabSale: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .adminTabSale)
#else
        .init()
#endif
    }

    /// The "arrow_left_icon" asset catalog image.
    static var arrowLeftIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .arrowLeftIcon)
#else
        .init()
#endif
    }

    /// The "arrow_left_icon_alt" asset catalog image.
    static var arrowLeftIconAlt: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .arrowLeftIconAlt)
#else
        .init()
#endif
    }

    /// The "arrow_right_icon" asset catalog image.
    static var arrowRightIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .arrowRightIcon)
#else
        .init()
#endif
    }

    /// The "bell_icon" asset catalog image.
    static var bellIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .bellIcon)
#else
        .init()
#endif
    }

    /// The "card_wave_overlay" asset catalog image.
    static var cardWaveOverlay: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .cardWaveOverlay)
#else
        .init()
#endif
    }

    /// The "delete_icon" asset catalog image.
    static var deleteIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .deleteIcon)
#else
        .init()
#endif
    }

    /// The "edit_icon" asset catalog image.
    static var editIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .editIcon)
#else
        .init()
#endif
    }

    /// The "file_icon" asset catalog image.
    static var fileIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .fileIcon)
#else
        .init()
#endif
    }

    /// The "filter_icon" asset catalog image.
    static var filterIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .filterIcon)
#else
        .init()
#endif
    }

    /// The "filter_icon_alt" asset catalog image.
    static var filterIconAlt: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .filterIconAlt)
#else
        .init()
#endif
    }

    /// The "folder_copy_icon" asset catalog image.
    static var folderCopyIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .folderCopyIcon)
#else
        .init()
#endif
    }

    /// The "home.graph" asset catalog image.
    static var homeGraph: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .homeGraph)
#else
        .init()
#endif
    }

    /// The "home_icon" asset catalog image.
    static var homeIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .homeIcon)
#else
        .init()
#endif
    }

    /// The "influence_icon" asset catalog image.
    static var influenceIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .influenceIcon)
#else
        .init()
#endif
    }

    /// The "load_circle_icon" asset catalog image.
    static var loadCircleIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .loadCircleIcon)
#else
        .init()
#endif
    }

    /// The "margin_icon" asset catalog image.
    static var marginIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .marginIcon)
#else
        .init()
#endif
    }

    /// The "menu_icon" asset catalog image.
    static var menuIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .menuIcon)
#else
        .init()
#endif
    }

    /// The "pin_icon" asset catalog image.
    static var pinIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .pinIcon)
#else
        .init()
#endif
    }

    /// The "pos_icon" asset catalog image.
    static var posIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .posIcon)
#else
        .init()
#endif
    }

    /// The "product_image" asset catalog image.
    static var product: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .product)
#else
        .init()
#endif
    }

    /// The "product_image_1" asset catalog image.
    static var productImage1: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .productImage1)
#else
        .init()
#endif
    }

    /// The "sale_icon" asset catalog image.
    static var saleIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .saleIcon)
#else
        .init()
#endif
    }

    /// The "search_icon" asset catalog image.
    static var searchIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .searchIcon)
#else
        .init()
#endif
    }

    /// The "sort.icon" asset catalog image.
    static var sortIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .sortIcon)
#else
        .init()
#endif
    }

    /// The "tab_center" asset catalog image.
    static var tabCenter: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .tabCenter)
#else
        .init()
#endif
    }

    /// The "tab_home" asset catalog image.
    static var tabHome: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .tabHome)
#else
        .init()
#endif
    }

    /// The "tab_indicator" asset catalog image.
    static var tabIndicator: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .tabIndicator)
#else
        .init()
#endif
    }

    /// The "tab_medications" asset catalog image.
    static var tabMedications: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .tabMedications)
#else
        .init()
#endif
    }

    /// The "tab_prescriptions" asset catalog image.
    static var tabPrescriptions: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .tabPrescriptions)
#else
        .init()
#endif
    }

    /// The "tab_settings" asset catalog image.
    static var tabSettings: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .tabSettings)
#else
        .init()
#endif
    }

    /// The "trash_icon" asset catalog image.
    static var trashIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .trashIcon)
#else
        .init()
#endif
    }

    /// The "user_icon" asset catalog image.
    static var userIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .userIcon)
#else
        .init()
#endif
    }

    /// The "view_icon" asset catalog image.
    static var viewIcon: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .viewIcon)
#else
        .init()
#endif
    }

    /// The "wasfa_logo" asset catalog image.
    static var wasfaLogo: UIKit.UIImage {
#if !os(watchOS)
        .init(resource: .wasfaLogo)
#else
        .init()
#endif
    }

}
#endif

// MARK: - Thinnable Asset Support -

@available(iOS 17.0, macOS 14.0, tvOS 17.0, watchOS 10.0, *)
@available(watchOS, unavailable)
extension DeveloperToolsSupport.ColorResource {

    private init?(thinnableName: Swift.String, bundle: Foundation.Bundle) {
#if canImport(AppKit) && os(macOS)
        if AppKit.NSColor(named: NSColor.Name(thinnableName), bundle: bundle) != nil {
            self.init(name: thinnableName, bundle: bundle)
        } else {
            return nil
        }
#elseif canImport(UIKit) && !os(watchOS)
        if UIKit.UIColor(named: thinnableName, in: bundle, compatibleWith: nil) != nil {
            self.init(name: thinnableName, bundle: bundle)
        } else {
            return nil
        }
#else
        return nil
#endif
    }

}

#if canImport(AppKit)
@available(macOS 14.0, *)
@available(macCatalyst, unavailable)
extension AppKit.NSColor {

    private convenience init?(thinnableResource: DeveloperToolsSupport.ColorResource?) {
#if !targetEnvironment(macCatalyst)
        if let resource = thinnableResource {
            self.init(resource: resource)
        } else {
            return nil
        }
#else
        return nil
#endif
    }

}
#endif

#if canImport(UIKit)
@available(iOS 17.0, tvOS 17.0, *)
@available(watchOS, unavailable)
extension UIKit.UIColor {

    private convenience init?(thinnableResource: DeveloperToolsSupport.ColorResource?) {
#if !os(watchOS)
        if let resource = thinnableResource {
            self.init(resource: resource)
        } else {
            return nil
        }
#else
        return nil
#endif
    }

}
#endif

#if canImport(SwiftUI)
@available(iOS 17.0, macOS 14.0, tvOS 17.0, watchOS 10.0, *)
extension SwiftUI.Color {

    private init?(thinnableResource: DeveloperToolsSupport.ColorResource?) {
        if let resource = thinnableResource {
            self.init(resource)
        } else {
            return nil
        }
    }

}

@available(iOS 17.0, macOS 14.0, tvOS 17.0, watchOS 10.0, *)
extension SwiftUI.ShapeStyle where Self == SwiftUI.Color {

    private init?(thinnableResource: DeveloperToolsSupport.ColorResource?) {
        if let resource = thinnableResource {
            self.init(resource)
        } else {
            return nil
        }
    }

}
#endif

@available(iOS 17.0, macOS 14.0, tvOS 17.0, watchOS 10.0, *)
@available(watchOS, unavailable)
extension DeveloperToolsSupport.ImageResource {

    private init?(thinnableName: Swift.String, bundle: Foundation.Bundle) {
#if canImport(AppKit) && os(macOS)
        if bundle.image(forResource: NSImage.Name(thinnableName)) != nil {
            self.init(name: thinnableName, bundle: bundle)
        } else {
            return nil
        }
#elseif canImport(UIKit) && !os(watchOS)
        if UIKit.UIImage(named: thinnableName, in: bundle, compatibleWith: nil) != nil {
            self.init(name: thinnableName, bundle: bundle)
        } else {
            return nil
        }
#else
        return nil
#endif
    }

}

#if canImport(AppKit)
@available(macOS 14.0, *)
@available(macCatalyst, unavailable)
extension AppKit.NSImage {

    private convenience init?(thinnableResource: DeveloperToolsSupport.ImageResource?) {
#if !targetEnvironment(macCatalyst)
        if let resource = thinnableResource {
            self.init(resource: resource)
        } else {
            return nil
        }
#else
        return nil
#endif
    }

}
#endif

#if canImport(UIKit)
@available(iOS 17.0, tvOS 17.0, *)
@available(watchOS, unavailable)
extension UIKit.UIImage {

    private convenience init?(thinnableResource: DeveloperToolsSupport.ImageResource?) {
#if !os(watchOS)
        if let resource = thinnableResource {
            self.init(resource: resource)
        } else {
            return nil
        }
#else
        return nil
#endif
    }

}
#endif

