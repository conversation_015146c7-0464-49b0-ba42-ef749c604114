{"": {"diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdmin-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdmin-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdmin-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdmin-master.swiftdeps"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Application/AppDelegate.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppDelegate.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppDelegate.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppDelegate.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppDelegate.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppDelegate.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppDelegate.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppDelegate.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppDelegate~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Application/WasfaAdminApp.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdminApp.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdminApp.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdminApp.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdminApp.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdminApp.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdminApp.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdminApp.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/WasfaAdminApp~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/BaseAPI/BaseAPI.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/BaseAPI.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/BaseAPI.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/BaseAPI.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/BaseAPI.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/BaseAPI.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/BaseAPI.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/BaseAPI.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/BaseAPI~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/BaseAPI/TargetType.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TargetType.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TargetType.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TargetType.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TargetType.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TargetType.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TargetType.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TargetType.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TargetType~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/Repositories/APIEndPoints.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/APIEndPoints.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/APIEndPoints.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/APIEndPoints.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/APIEndPoints.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/APIEndPoints.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/APIEndPoints.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/APIEndPoints.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/APIEndPoints~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/Repositories/RepositoriesAPI.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesAPI.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesAPI.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesAPI.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesAPI.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesAPI.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesAPI.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesAPI.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesAPI~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/Repositories/RepositoriesNetworking.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesNetworking.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesNetworking.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesNetworking.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesNetworking.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesNetworking.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesNetworking.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesNetworking.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RepositoriesNetworking~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/AppState/AppState+Extention.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState+Extention.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState+Extention.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState+Extention.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState+Extention.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState+Extention.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState+Extention.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState+Extention.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState+Extention~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/AppState/AppState.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/AppState/enums.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/enums.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/enums.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/enums.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/enums.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/enums.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/enums.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/enums.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/enums~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/Route/Route.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Route.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Route.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Route.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Route.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Route.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Route.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Route.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Route~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/Route/RouterManager.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManager.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManager.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManager.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManager.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManager.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/Route/RouterManagerPerformanceTest.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManagerPerformanceTest.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManagerPerformanceTest.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManagerPerformanceTest.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManagerPerformanceTest.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManagerPerformanceTest.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManagerPerformanceTest.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManagerPerformanceTest.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/RouterManagerPerformanceTest~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/ActivityLoader/ActivityLoaderView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoaderView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoaderView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoaderView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoaderView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoaderView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoaderView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoaderView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoaderView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/AlertView/AlertView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AlertView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AlertView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AlertView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AlertView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AlertView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AlertView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AlertView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AlertView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/CachedAsyncImage/CachedAsyncImageView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CachedAsyncImageView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CachedAsyncImageView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CachedAsyncImageView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CachedAsyncImageView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CachedAsyncImageView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CachedAsyncImageView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CachedAsyncImageView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CachedAsyncImageView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/CustomInputField/CustomInputField.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomInputField.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomInputField.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomInputField.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomInputField.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomInputField.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomInputField.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomInputField.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomInputField~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/ImagePicker/ImagePicker.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImagePicker.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImagePicker.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImagePicker.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImagePicker.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImagePicker.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImagePicker.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImagePicker.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImagePicker~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/ImagePicker/ImageSelectionSheet.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImageSelectionSheet.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImageSelectionSheet.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImageSelectionSheet.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImageSelectionSheet.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImageSelectionSheet.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImageSelectionSheet.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImageSelectionSheet.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ImageSelectionSheet~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/Toast/ToastModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/Toast/ToastModifier.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModifier.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModifier.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModifier.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModifier.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModifier.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModifier.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModifier.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastModifier~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/Toast/ToastView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ToastView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Constants/AppConstants.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppConstants.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppConstants.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppConstants.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppConstants.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppConstants.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppConstants.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppConstants.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppConstants~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Constants/ColorConstants.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ColorConstants.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ColorConstants.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ColorConstants.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ColorConstants.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ColorConstants.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ColorConstants.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ColorConstants.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ColorConstants~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Constants/FontScheme.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/FontScheme.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/FontScheme.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/FontScheme.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/FontScheme.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/FontScheme.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/FontScheme.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/FontScheme.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/FontScheme~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Color+Extension.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Color+Extension.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Color+Extension.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Color+Extension.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Color+Extension.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Color+Extension.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Color+Extension.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Color+Extension.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Color+Extension~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Data+Extension.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Data+Extension.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Data+Extension.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Data+Extension.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Data+Extension.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Data+Extension.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Data+Extension.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Data+Extension.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Data+Extension~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Date+Extension.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Date+Extension.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Date+Extension.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Date+Extension.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Date+Extension.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Date+Extension.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Date+Extension.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Date+Extension.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Date+Extension~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Double+Extension.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Double+Extension.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Double+Extension.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Double+Extension.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Double+Extension.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Double+Extension.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Double+Extension.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Double+Extension.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Double+Extension~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Font+Extensions.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Font+Extensions.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Font+Extensions.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Font+Extensions.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Font+Extensions.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Font+Extensions.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Font+Extensions.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Font+Extensions.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Font+Extensions~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/String+Extension.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/String+Extension.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/String+Extension.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/String+Extension.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/String+Extension.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/String+Extension.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/String+Extension.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/String+Extension.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/String+Extension~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/View+Extension.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/View+Extension.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/View+Extension.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/View+Extension.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/View+Extension.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/View+Extension.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/View+Extension.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/View+Extension.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/View+Extension~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/ActivityLoader/ActivityLoader.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoader.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoader.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoader.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoader.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoader.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoader.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoader.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ActivityLoader~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/Logger/Logger.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Logger.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Logger.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Logger.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Logger.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Logger.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Logger.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Logger.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Logger~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/Logger/NetworkLogger.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/NetworkLogger.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/NetworkLogger.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/NetworkLogger.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/NetworkLogger.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/NetworkLogger.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/NetworkLogger.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/NetworkLogger.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/NetworkLogger~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/PermissionManager/PermissionManager.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PermissionManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PermissionManager.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PermissionManager.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PermissionManager.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PermissionManager.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PermissionManager.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PermissionManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PermissionManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/TokenManager/TokenManager.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TokenManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TokenManager.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TokenManager.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TokenManager.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TokenManager.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TokenManager.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TokenManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TokenManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/UserDefaults/UserDefaults.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/UserDefaults.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/UserDefaults.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/UserDefaults.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/UserDefaults.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/UserDefaults.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/UserDefaults.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/UserDefaults.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/UserDefaults~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/Viewport/ViewportHelper.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ViewportHelper.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ViewportHelper.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ViewportHelper.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ViewportHelper.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ViewportHelper.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ViewportHelper.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ViewportHelper.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ViewportHelper~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Utilities/Utilities.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Utilities.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Utilities.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Utilities.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Utilities.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Utilities.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Utilities.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Utilities.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/Utilities~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/Model/SignInModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/View/HeaderView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HeaderView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HeaderView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HeaderView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HeaderView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HeaderView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HeaderView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HeaderView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HeaderView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/View/SignInView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/ViewModel/SignInViewModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInViewModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInViewModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInViewModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SignInViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Model/DashbordModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashbordModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashbordModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashbordModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashbordModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashbordModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashbordModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashbordModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashbordModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Center/Model/CenterModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Center/View/CenterView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Center/ViewModel/CenterViewModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterViewModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterViewModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterViewModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CenterViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/Model/HomeModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/View/HomeView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/View/PrescriptionCardView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionCardView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionCardView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionCardView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionCardView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionCardView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionCardView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionCardView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionCardView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/View/ScrollProgressIndicator.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ScrollProgressIndicator.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ScrollProgressIndicator.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ScrollProgressIndicator.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ScrollProgressIndicator.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ScrollProgressIndicator.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ScrollProgressIndicator.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ScrollProgressIndicator.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/ScrollProgressIndicator~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/ViewModel/HomeViewModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeViewModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeViewModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeViewModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/HomeViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Medications/Model/MedicationsModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Medications/View/MedicationsView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Medications/ViewModel/MedicationsViewModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsViewModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsViewModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsViewModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MedicationsViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Prescriptions/Model/PrescriptionsModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Prescriptions/View/PrescriptionsView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Prescriptions/ViewModel/PrescriptionsViewModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsViewModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsViewModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsViewModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/PrescriptionsViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Settings/Model/SettingsModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Settings/View/SettingsView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Settings/ViewModel/SettingsViewModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsViewModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsViewModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsViewModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SettingsViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/TabExtension.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TabExtension.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TabExtension.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TabExtension.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TabExtension.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TabExtension.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TabExtension.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TabExtension.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/TabExtension~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/View/CustomTabBar.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomTabBar.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomTabBar.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomTabBar.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomTabBar.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomTabBar.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomTabBar.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomTabBar.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/CustomTabBar~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/View/DashboardView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/View/LazyTabView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/LazyTabView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/LazyTabView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/LazyTabView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/LazyTabView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/LazyTabView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/LazyTabView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/LazyTabView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/LazyTabView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/ViewModel/DashboardViewModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardViewModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardViewModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardViewModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/DashboardViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Main/MainView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Main/MainViewModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainViewModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainViewModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainViewModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Splash/SplashView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SplashView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SplashView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SplashView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SplashView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SplashView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SplashView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SplashView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SplashView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/Model/SuperModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/View/MainScrollBody.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainScrollBody.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainScrollBody.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainScrollBody.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainScrollBody.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainScrollBody.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainScrollBody.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainScrollBody.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/MainScrollBody~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/View/SuperView.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperView.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperView.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperView.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperView.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperView.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperView~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/ViewModel/SuperViewModel.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperViewModel.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperViewModel.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperViewModel.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/SuperViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}}