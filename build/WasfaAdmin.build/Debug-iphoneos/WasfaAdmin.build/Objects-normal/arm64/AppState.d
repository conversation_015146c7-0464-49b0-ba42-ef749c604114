/Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/Objects-normal/arm64/AppState.o : /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/BaseAPI/BaseAPI.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/Repositories/RepositoriesAPI.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/CustomInputField/CustomInputField.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Constants/FontScheme.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/BaseAPI/TargetType.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Application/AppDelegate.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/AppState/AppState.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/Route/Route.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/Repositories/RepositoriesNetworking.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Model/DashbordModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/Model/HomeModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/Model/SignInModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/Model/SuperModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Center/Model/CenterModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Settings/Model/SettingsModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Medications/Model/MedicationsModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Prescriptions/Model/PrescriptionsModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/Toast/ToastModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/ViewModel/DashboardViewModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/ViewModel/HomeViewModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/ViewModel/SignInViewModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Main/MainViewModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/ViewModel/SuperViewModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Center/ViewModel/CenterViewModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Settings/ViewModel/SettingsViewModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Medications/ViewModel/MedicationsViewModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Prescriptions/ViewModel/PrescriptionsViewModel.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Data+Extension.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Double+Extension.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Date+Extension.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/String+Extension.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Color+Extension.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/View+Extension.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/TabExtension.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/AppState/AppState+Extention.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Application/WasfaAdminApp.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/View/CustomTabBar.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/ActivityLoader/ActivityLoader.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/TokenManager/TokenManager.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/PermissionManager/PermissionManager.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/Route/RouterManager.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/Logger/Logger.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/Logger/NetworkLogger.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/Toast/ToastModifier.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/ImagePicker/ImagePicker.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/Viewport/ViewportHelper.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/View/ScrollProgressIndicator.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Utilities/Utilities.swift /Users/<USER>/Desktop/WasfaAdminRX/build/WasfaAdmin.build/Debug-iphoneos/WasfaAdmin.build/DerivedSources/GeneratedAssetSymbols.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/AppState/enums.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Extentions/Font+Extensions.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Helper/UserDefaults/UserDefaults.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Constants/AppConstants.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Constants/ColorConstants.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/APIServices/Repositories/APIEndPoints.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/ImagePicker/ImageSelectionSheet.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Core/Route/RouterManagerPerformanceTest.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/View/LazyTabView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/View/PrescriptionCardView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/View/DashboardView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/CachedAsyncImage/CachedAsyncImageView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Home/View/HomeView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Splash/SplashView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/View/SignInView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Main/MainView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Auth/SignIn/View/HeaderView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/ActivityLoader/ActivityLoaderView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/View/SuperView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Center/View/CenterView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Settings/View/SettingsView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Medications/View/MedicationsView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/Dashboard/Tabs/Prescriptions/View/PrescriptionsView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/AlertView/AlertView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Global/Components/Toast/ToastView.swift /Users/<USER>/Desktop/WasfaAdminRX/WasfaAdmin/Modules/SuperBase/View/MainScrollBody.swift /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/XPC.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/ObjectiveC.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/CoreMIDI.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/PhotosUI.framework/Modules/PhotosUI.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/SwiftUI.framework/Modules/SwiftUI.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/_PhotosUI_SwiftUI.framework/Modules/_PhotosUI_SwiftUI.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/CoreMedia.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/simd.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/unistd.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/CoreImage.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/_time.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/sys_time.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/SwiftUICore.framework/Modules/SwiftUICore.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/QuartzCore.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/OSLog.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/Dispatch.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/_math.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/Spatial.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/_signal.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/Metal.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/System.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/CoreLocation.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/AVFoundation.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/CoreFoundation.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/Observation.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/DataDetection.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/_stdio.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/CoreAudio.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/_errno.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/FileProvider.framework/Modules/FileProvider.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/os.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/Photos.framework/Modules/Photos.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/_Builtin_float.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/UIKit.framework/Modules/UIKit.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/arm64-apple-ios.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/XPC.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/ObjectiveC.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/CoreMIDI.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/PhotosUI.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/SwiftUI.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/_PhotosUI_SwiftUI.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/CoreMedia.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/CoreData.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/simd.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/unistd.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/CoreImage.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/CoreTransferable.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/_time.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/sys_time.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/Combine.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/SwiftUICore.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/QuartzCore.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/_StringProcessing.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/OSLog.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/Dispatch.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/_math.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/Spatial.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/_signal.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/Metal.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/System.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/Darwin.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/CoreLocation.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/Foundation.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/AVFoundation.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/CoreFoundation.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/Observation.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/DataDetection.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/_stdio.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/CoreAudio.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/_errno.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/FileProvider.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/CoreGraphics.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/Symbols.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/os.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/Photos.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/_Builtin_float.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/Swift.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/UIKit.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/SwiftOnoneSupport.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/DeveloperToolsSupport.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/CoreText.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/_Concurrency.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/Accessibility.swiftmodule/arm64-apple-ios.swiftmodule /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/include/XPC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/include/ObjectiveC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/OpenGLES.framework/Headers/OpenGLES.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/CoreMedia.framework/Headers/CoreMedia.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/include/_time.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/include/Dispatch.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/CoreLocation.framework/Headers/CoreLocation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/AVFoundation.framework/Headers/AVFoundation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/AVFAudio.framework/Headers/AVFAudio.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/CoreAudioTypes.framework/Headers/CoreAudioTypes.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/UserNotifications.framework/Headers/UserNotifications.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/usr/include/os.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/Photos.framework/Headers/Photos.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/UIKit.framework/Headers/UIKit.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/MediaToolbox.framework/Headers/MediaToolbox.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/AudioToolbox.framework/Headers/AudioToolbox.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/Security.framework/Headers/Security.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins/libObservationMacros.dylib /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins/libPreviewsMacros.dylib
