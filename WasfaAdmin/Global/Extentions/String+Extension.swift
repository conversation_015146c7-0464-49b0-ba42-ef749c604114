//
//  StringExtenion.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

extension String {
    var localize:LocalizedStringKey { LocalizedStringKey(self) }
}


extension String {
    // MARK: - Check is valid Email

    func isValidEmail(isMandatory: Bool = false) -> Bool {
        if isEmpty() {
            return !isMandatory
        }
        
       
        let str = self.removeWhiteSpaces()
        let regExpression = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let predicate = NSPredicate(format: "SELF MATCHES %@", regExpression)
        return predicate.evaluate(with: str)
    }

    // MARK: - Removes whitespaces

    func removeWhiteSpaces() -> String {
        var str = self.trimmingCharacters(in: .whitespaces)
        str = str.replacingOccurrences(of: " ", with: "")
        return str
    }

    // MARK: - Check isEmpty

    func isEmpty() -> <PERSON>ol {
        if self.isEmpty || self == "" {
            return true
        }
        return false
    }
    
    var isEmptyInput: Bool {
        trimmed.isEmpty
    }
    
    var trimmed: String {
        trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

extension String {
    var toDouble: Double {
        return Double(self) ?? 0.0
    }
    
    /// Converts a string to a Date using the specified format.
       /// - Parameter format: A date format string (e.g. "yyyy-MM-dd")
       /// - Returns: A Date object if conversion succeeds, else nil.
       func toDate(format: String = "yyyy-MM-dd") -> Date? {
           let formatter = DateFormatter()
           formatter.dateFormat = format
           formatter.locale = Locale(identifier: "en_US_POSIX") // Ensures stable parsing
           formatter.timeZone = TimeZone(secondsFromGMT: 0)
           return formatter.date(from: self)
       }
}


extension String {
    func extractCountryCodeAndPhoneNumber(from knownCodes: [CountryCode]) -> (countryCode: CountryCode, phoneNumber: String) {
        // Try to find a matching dial code prefix
        for code in knownCodes.sorted(by: { $0.dialCode.count > $1.dialCode.count }) {
            if starts(with: code.dialCode) {
                let number = replacingOccurrences(of: code.dialCode, with: "")
                return (countryCode: code, phoneNumber: number)
            }
        }
        // Fallback if no match
        return (countryCode: .kuwait, phoneNumber: self)
    }
}



 extension String {
    
     func formatDateForChart() -> String {
        // Split the date string by "-" and extract month and day
        let components = self.components(separatedBy: "-")

        // Ensure we have at least 3 components (year, month, day)
        guard components.count >= 3 else {
            Logger.warning("⚠️ Invalid date format received: \(self), using as-is", tag: "HomeViewModel")
            return self
        }

        let month = components[1] // MM
        let day = components[2]   // dd

        return "\(month)-\(day)" // Return in MM-dd format
    }

}
