//
//  Font+Extensions.swift
//  WasfaAdminSetup
//
//  Created by Apple on 24/06/2025.
//

import SwiftUI

// MARK: - Custom Font Extensions
extension Font {
    // Helper function to create fonts with fallback to system fonts
    private static func customFont(name: String, size: CGFloat, relativeTo textStyle: Font.TextStyle = .body) -> Font {
        // Try to use custom font, fallback to system font if not available
        if UIFont(name: name, size: size) != nil {
            return Font.custom(name, size: size, relativeTo: textStyle)
        } else {
            // Fallback to system font with appropriate weight
            switch name {
            case let n where n.contains("Bold"):
                return Font.system(size: size, weight: .bold, design: .default)
            case let n where n.contains("Medium"):
                return Font.system(size: size, weight: .medium, design: .default)
            case let n where n.contains("Semibold"):
                return Font.system(size: size, weight: .semibold, design: .default)
            default:
                return Font.system(size: size, weight: .regular, design: .default)
            }
        }
    }
    
    // MARK: - Admin Theme Fonts
    public struct Admin {
        // Headings
        public static let heading1 = customFont(name: "Roboto-Bold", size: 20, relativeTo: .title)
        public static let heading2 = customFont(name: "Roboto-Bold", size: 18, relativeTo: .title2)
        public static let heading3 = customFont(name: "Roboto-Bold", size: 16, relativeTo: .title3)
        public static let heading4 = customFont(name: "Nunito-Bold", size: 18, relativeTo: .title2)

        // Subheadings
        public static let subheading1 = customFont(name: "Inter-Medium", size: 15, relativeTo: .headline)
        public static let subheading2 = customFont(name: "Inter-Medium", size: 15, relativeTo: .headline)
        
        // Body Text
        public static let body1 = customFont(name: "Roboto-Regular", size: 14, relativeTo: .body)
        public static let body1Medium = customFont(name: "Roboto-Medium", size: 14, relativeTo: .body)
        public static let body2 = customFont(name: "Roboto-Regular", size: 13, relativeTo: .callout)
        public static let body2Medium = customFont(name: "Roboto-Medium", size: 13, relativeTo: .callout)
        public static let body2Bold = customFont(name: "Roboto-Bold", size: 13, relativeTo: .callout)

        // Captions
        public static let caption1 = customFont(name: "Inter-Regular", size: 12, relativeTo: .caption)
        public static let caption1InterRegular = customFont(name: "Inter-Regular", size: 12, relativeTo: .caption)

        // Specific sizes for components
        public static let robotoRegular11 = customFont(name: "Roboto-Regular", size: 11, relativeTo: .caption2)
        public static let robotoBold14 = customFont(name: "Roboto-Bold", size: 14, relativeTo: .headline)
        public static let interMedium15 = customFont(name: "Inter-Medium", size: 15, relativeTo: .headline)
        public static let dmSansBold15 = customFont(name: "DM Sans-Bold", size: 15, relativeTo: .headline)
    }
}
