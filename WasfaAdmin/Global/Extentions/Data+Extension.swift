//
//  DataExtension.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import Foundation

extension Data {
    func printFormattedJSON() {
        if let json = try? JSONSerialization.jsonObject(with: self, options: .mutableContainers),
           let jsonData = try? JSONSerialization.data(withJSONObject: json, options: .prettyPrinted)
        {
            printJSONData(jsonData)
        } else {
            printPrettyJson()
//            assertionFailure("Malformed JSON")
        }
    }

    func printJSON() {
        printJSONData(self)
    }

    private func printJSONData(_ data: Data) {
        Logger.debug(String(decoding: data, as: UTF8.self), tag: "DataExtension")
    }

    private func printPrettyJson() {
        let str = String(decoding: self, as: UTF8.self)
        Logger.debug("raw http response: \(str)", tag: "DataExtension")
    }
}

extension Data {
    func toString() -> String? {
        return String(data: self, encoding: .utf8)
    }
}

extension Optional where Wrapped == Data {
    func printPrettyJson() {
        if let json = self {
            let str = String(decoding: json, as: UTF8.self)
            Logger.debug("raw http response: \(str)", tag: "DataExtension")
        }
    }
}
