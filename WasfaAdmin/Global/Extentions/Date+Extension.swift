//
//  DateExtension.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import Foundation

extension Date {
    static var currentTimeStamp: Int64 {
        return Int64(Date().timeIntervalSince1970 * 1000)
    }
}


extension DateFormatter {
    convenience init(dateFormat: String, calendar: Calendar) {
        self.init()
        self.dateFormat = dateFormat
        self.calendar = calendar
    }
}

extension Date {
    func get(_ components: Calendar.Component..., calendar: Calendar = Calendar.current) -> DateComponents {
        return calendar.dateComponents(Set(components), from: self)
    }

    func get(_ component: Calendar.Component, calendar: Calendar = Calendar.current) -> Int {
        return calendar.component(component, from: self)
    }
}



extension Date {
    func withAddedMinutes(minutes: Double) -> Date {
        addingTimeInterval(minutes * 60)
    }

    func withAddedHours(hours: Double) -> Date {
        withAddedMinutes(minutes: hours * 60)
    }
}

extension Date {
    func startOfMonth(using calendar: Calendar) -> Date {
        calendar.date(
            from: calendar.dateComponents([.year, .month], from: self)
        ) ?? self
    }
}

extension Date {
    var isPastDate: Bool {
        return self < Calendar.current.startOfDay(for: Date())
    }
    
    
}

extension Date {
    /// Checks if a date is not from the previous month, comparing only the year and month.
    /// - Returns: `true` if the date is from the current or future month, and `false` if it's from the previous month.
    func isNotFromPreviousMonth() -> Bool {
        let calendar = Calendar.current
        let today = Date()

        // Extract year and month for the current date (today)
        let currentYear = calendar.component(.year, from: today)
        let currentMonth = calendar.component(.month, from: today)

        // Extract year and month for the date to check (self)
        let dateYear = calendar.component(.year, from: self)
        let dateMonth = calendar.component(.month, from: self)

        // Compare year and month
        if dateYear > currentYear {
            return true // The date is from a future year
        } else if dateYear == currentYear && dateMonth >= currentMonth {
            return true // The date is from the current month or a future month in the current year
        } else {
            return false // The date is from a previous month
        }
    }
}

extension Date {
    func toString(outputFormate: String) -> String {
        // Create Date Formatter
        let dateFormatter = DateFormatter()

        // Set Date Format
        dateFormatter.dateFormat = outputFormate
        // Convert Date to String
        return dateFormatter.string(from: self)
    }
}





