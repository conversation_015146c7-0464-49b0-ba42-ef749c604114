//
//  ColorExtension.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

extension Color {
    static let Black9003d: Color = .init("Black9003d")
    static let Red600: Color = .init("Red600")
    static let Pink300C1: Color = .init("Pink300C1")
    static let Cyan4000a: Color = .init("Cyan4000a")
    static let Cyan8003f: Color = .init("Cyan8003f")
    static let Green900: Color = .init("Green900")
    static let Gray80001: Color = .init("Gray80001")
    static let Blue6009e: Color = .init("Blue6009e")
    static let Black9003f: Color = .init("Black9003f")
    static let WhiteA70093: Color = .init("WhiteA70093")
    static let Black90042: Color = .init("Black90042")
    static let GreenA700: Color = .init("GreenA700")
    static let Black90001: Color = .init("Black90001")
    static let LightGreen600: Color = .init("LightGreen600")
    static let Teal900: Color = .init("Teal900")
    static let Gray20001: Color = .init("Gray20001")
    static let BlueGray900: Color = .init("BlueGray900")
    static let Blue300: Color = .init("Blue300")
    static let DescriptionText: Color = .init("DescriptionText")
    static let BlueLight: Color = .init("Blue.light")
    static let RedA700: Color = .init("RedA700")
    static let Gray600: Color = .init("Gray600")
    static let Gray400: Color = .init("Gray400")
    static let BlueGray100: Color = .init("BlueGray100")
    static let Gray800: Color = .init("Gray800")
    static let Gray500C4: Color = .init("Gray500C4")
    static let Black9000f: Color = .init("Black9000f")
    static let Gray200: Color = .init("Gray200")
    static let Black90054: Color = .init("Black90054")
    static let Pink30054: Color = .init("Pink30054")
    static let Blue60054: Color = .init("Blue60054")
    static let Gray70002: Color = .init("Gray70002")
    static let WhiteA700: Color = .init("WhiteA700")
    static let ProductBG: Color = .init("ProductBG")
    static let Gray70001: Color = .init("Gray70001")
    static let Black9005b: Color = .init("Black9005b")
    static let BlueGray10001: Color = .init("BlueGray10001")
    static let BlueGray10002: Color = .init("BlueGray10002")
    static let Blue6003a: Color = .init("Blue6003a")
    static let LightGreen300: Color = .init("LightGreen300")
    static let Gray50: Color = .init("Gray50")
    static let Black9001e: Color = .init("Black9001e")
    static let Black90021: Color = .init("Black90021")
    static let Teal400: Color = .init("Teal400")
    static let Black90023: Color = .init("Black90023")
    static let Black900: Color = .init("Black900")
    static let Gray50001: Color = .init("Gray50001")
    static let Gray50003: Color = .init("Gray50003")
    static let Gray50002: Color = .init("Gray50002")
    static let Gray50004: Color = .init("Gray50004")
    static let Black900Bf: Color = .init("Black900Bf")
    static let Gray90002: Color = .init("Gray90002")
    static let Gray700: Color = .init("Gray700")
    static let Gray90003: Color = .init("Gray90003")
    static let Gray500: Color = .init("Gray500")
    static let Black9002b: Color = .init("Black9002b")
    static let Gray60001: Color = .init("Gray60001")
    static let Blue60038: Color = .init("Blue60038")
    static let Gray900: Color = .init("Gray900")
    static let Blue600: Color = .init("Blue600")
    static let Gray90001: Color = .init("Gray90001")
    static let Blue6002b: Color = .init("Blue6002b")
    static let Gray100: Color = .init("Gray100")
    static let Gray4009e: Color = .init("Gray4009e")
    static let Teal90026: Color = .init("Teal90026")
    static let Pink30038: Color = .init("Pink30038")

  
   
}

// MARK: - Color Hex Initializer

extension Color {
    /// Initialize a Color from a hex string
    /// - Parameter hex: Hex string (e.g., "#A61C5C" or "A61C5C")
    init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - Admin Theme Colors

public extension Color {
    enum Admin {
        // Primary Colors
        public static let primary = Color("AdminMainColor")
        public static let primaryBackground = Color(hex: "#F7EBF0")!
        public static let primaryFill = Color("AdminMainColor")

        // Secondary Colors
        public static let secondaryOrange = Color(hex: "#F1892C")!
        public static let secondaryGreen = Color(hex: "#5DB245")!
        public static let secondaryRed = Color(hex: "#E53935")!
        public static let secondaryRedDark = Color(hex: "#EC1448")!
        public static let secondaryBlue = Color(hex: "#1FABE2")!

        // Text Colors
        public static let textBlack = Color(hex: "#000000")!
        public static let textNearBlack = Color(hex: "#111111")!
        public static let textDarkGray = Color(hex: "#424546")!
        public static let textMediumGray = Color(hex: "#9A9898")!
        public static let textLightGray = Color(hex: "#B4ABAB")!
        public static let textWhite = Color(hex: "#FFFFFF")!
        public static let textError = Color(hex: "#E53935")!
        public static let textSecondary = Color(hex: "#9E9E9E")!

        // Background Colors
        public static let backgroundWhite = Color(hex: "#FFFFFF")!
        public static let backgroundOffWhite = Color(hex: "#FAFAFE")!
        public static let backgroundGray = Color("SecondaryCardBackground")
        public static let backgroundGrayMedium = Color(hex: "#F8F8F8")!
        public static let backgroundGrayDark = Color(hex: "#D9D9D9")!

        // Section Header Color
        public static let sectionHeader = Color(hex: "#111111")!
    }
}

// MARK: - Relative Sizing Extensions

public extension CGFloat {
    /// Relative width based on design width (typically 375pt for iPhone)
    
    @MainActor var relativeFontSize: CGFloat {
        return self * (UIScreen.main.bounds.height / 390.0)
    }
    
    @MainActor var relativeWidth: CGFloat {
        return self * (UIScreen.main.bounds.width / 390.0) * 0.97
    }

    /// Relative height based on design height (typically 812pt for iPhone)
    @MainActor var relativeHeight: CGFloat {
        return self * (UIScreen.main.bounds.height / 844.0)
    }
    
   
}

extension Double {
    
    var relativeWidth:Double {
        self * (CGFloat(UIScreen.main.bounds.width) / 390.0)
    }
    
    var relativeHeight:Double {
        (self * (CGFloat(UIScreen.main.bounds.height) / 844.0)) * 0.97
    }
    
    var relativeFontSize:Double {
        self * (CGFloat(UIScreen.main.bounds.width) / 390.0)
    }

}


// MARK: - Button Opacity Extension

public extension View {
    /// Disables the view and applies opacity based on the disabled state
    /// - Parameter disabled: Whether the view should be disabled
    /// - Returns: Modified view with disabled state and opacity
    func disableWithOpacity(_ disabled: Bool) -> some View {
        self
            .disabled(disabled)
            .opacity(disabled ? 0.6 : 1.0)
    }

    func disableWithGray(_ isDisabled: Bool) -> some View {
        self
            .grayscale(isDisabled ? 1.0 : 0.0) // Desaturate color
            .opacity(isDisabled ? 0.5 : 1.0) // Reduce visibility
            .allowsHitTesting(!isDisabled) // Disable interaction
            .animation(.linear(duration: 0.1), value: isDisabled)
    }
}
