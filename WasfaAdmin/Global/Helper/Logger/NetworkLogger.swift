//
//  NetworkLogger.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import Foundation

class NetworkLogger {
    
    static func log(request: URLRequest) {
       Logger.debug("\n - - - - - - - - - - OUTGOING - - - - - - - - - - \n", tag: "NetworkLogger")
       defer { Logger.debug("\n - - - - - - - - - -  END - - - - - - - - - - \n", tag: "NetworkLogger") }
       let urlAsString = request.url?.absoluteString ?? ""
       let urlComponents = URLComponents(string: urlAsString)
       let method = request.httpMethod != nil ? "\(request.httpMethod ?? "")" : ""
       let path = "\(urlComponents?.path ?? "")"
       let query = "\(urlComponents?.query ?? "")"
       let host = "\(urlComponents?.host ?? "")"
       var output = """
       \(urlAsString) \n\n
       \(method) \(path)?\(query) HTTP/1.1 \n
       HOST: \(host)\n
       """
       for (key,value) in request.allHTTPHeaderFields ?? [:] {
          output += "\(key): \(value) \n"
       }
       if let body = request.httpBody {
          output += "\n \(String(data: body, encoding: .utf8) ?? "")"
       }
       Logger.debug(output, tag: "NetworkLogger")
    }
    
    static func log(response: HTTPURLResponse?, data: Data?, error: Error?) {
       Logger.debug("\n - - - - - - - - - - INCOMMING - - - - - - - - - - \n", tag: "NetworkLogger")
       defer { Logger.debug("\n - - - - - - - - - -  END - - - - - - - - - - \n", tag: "NetworkLogger") }
       let urlString = response?.url?.absoluteString
       let components = NSURLComponents(string: urlString ?? "")
       let path = "\(components?.path ?? "")"
       let query = "\(components?.query ?? "")"
       var output = ""
       if let urlString = urlString {
          output += "\(urlString)"
          output += "\n\n"
       }
       if let statusCode =  response?.statusCode {
          output += "HTTP \(statusCode) \(path)?\(query)\n"
       }
       if let host = components?.host {
          output += "Host: \(host)\n"
       }
       for (key, value) in response?.allHeaderFields ?? [:] {
          output += "\(key): \(value)\n"
       }
       if let body = data {
//          output += "\n\(String(data: body, encoding: .utf8) ?? "")\n"
           body.printFormattedJSON()
       }
       if error != nil {
          output += "\nError: \(error!.localizedDescription)\n"
       }
       Logger.debug(output, tag: "NetworkLogger")
    }
}
