//
//  Logger.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import Foundation

enum LogLevel: String {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "⚠️ WARNING"
    case error = "❌ ERROR"
}

final class Logger {
    static let shared = Logger()

    // Set to true if you want logs also written to file (Documents/logs.txt)
    private let enableFileLogging = false

    private let dateFormatter: DateFormatter
    private let logQueue = DispatchQueue(label: "LoggerQueue", qos: .utility)

    private init() {
        dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
    }

    /// Core logging function
    private func log(_ level: LogLevel, tag: String?, items: [Any]) {
        #if DEBUG
        let timestamp = dateFormatter.string(from: Date())
        let tagString = tag != nil ? "[\(tag!)] " : ""
        let message = items.map { "\($0)" }.joined(separator: " ")
        let fullMessage = "\(timestamp) [\(level.rawValue)] \(tagString)\(message)"

        // Print to Xcode console
        Swift.print(fullMessage)

        // Optional file logging
        if enableFileLogging {
            writeToFile(fullMessage)
        }
        #endif
    }

    // MARK: - Public Log Methods

    static func debug(_ items: Any..., tag: String? = nil) {
        shared.log(.debug, tag: tag, items: items)
    }

    static func info(_ items: Any..., tag: String? = nil) {
        shared.log(.info, tag: tag, items: items)
    }

    static func warning(_ items: Any..., tag: String? = nil) {
        shared.log(.warning, tag: tag, items: items)
    }

    static func error(_ items: Any..., tag: String? = nil) {
        shared.log(.error, tag: tag, items: items)
    }

    // MARK: - File Logging

    private func writeToFile(_ message: String) {
        logQueue.async {
            guard let fileURL = self.getLogFileURL() else { return }
            let logLine = message + "\n"
            if FileManager.default.fileExists(atPath: fileURL.path) {
                // Append to file
                if let fileHandle = try? FileHandle(forWritingTo: fileURL) {
                    fileHandle.seekToEndOfFile()
                    if let data = logLine.data(using: .utf8) {
                        fileHandle.write(data)
                        fileHandle.closeFile()
                    }
                }
            } else {
                // Create file
                try? logLine.write(to: fileURL, atomically: true, encoding: .utf8)
            }
        }
    }

    private func getLogFileURL() -> URL? {
        guard let docs = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return nil
        }
        return docs.appendingPathComponent("logs.txt")
    }
}
