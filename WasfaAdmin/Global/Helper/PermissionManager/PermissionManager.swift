//
//  PermissionManager.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import Foundation
import AVFoundation
import Photos
import UIKit

/// Manager for handling camera and photo library permissions
class PermissionManager: ObservableObject {
    static let shared = PermissionManager()
    
    private init() {}
    
    // MARK: - Camera Permission
    
    /// Check if camera permission is granted
    var isCameraPermissionGranted: Bool {
        AVCaptureDevice.authorizationStatus(for: .video) == .authorized
    }
    
    /// Check if camera is available on device
    var isCameraAvailable: Bool {
        UIImagePickerController.isSourceTypeAvailable(.camera)
    }
    
    /// Request camera permission
    func requestCameraPermission() async -> Bool {
        Logger.info("📷 Requesting camera permission", tag: "PermissionManager")
        
        guard isCameraAvailable else {
            Logger.warning("📷 Camera not available on this device", tag: "PermissionManager")
            return false
        }
        
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        
        switch status {
        case .authorized:
            Logger.info("✅ Camera permission already granted", tag: "PermissionManager")
            return true
            
        case .notDetermined:
            Logger.info("🤔 Camera permission not determined, requesting...", tag: "PermissionManager")
            return await withCheckedContinuation { continuation in
                AVCaptureDevice.requestAccess(for: .video) { granted in
                    Logger.info("📷 Camera permission result: \(granted)", tag: "PermissionManager")
                    continuation.resume(returning: granted)
                }
            }
            
        case .denied, .restricted:
            Logger.warning("❌ Camera permission denied or restricted", tag: "PermissionManager")
            return false
            
        @unknown default:
            Logger.warning("❓ Unknown camera permission status", tag: "PermissionManager")
            return false
        }
    }
    
    // MARK: - Photo Library Permission
    
    /// Check if photo library permission is granted
    var isPhotoLibraryPermissionGranted: Bool {
        if #available(iOS 14, *) {
            return PHPhotoLibrary.authorizationStatus(for: .readWrite) == .authorized ||
                   PHPhotoLibrary.authorizationStatus(for: .readWrite) == .limited
        } else {
            return PHPhotoLibrary.authorizationStatus() == .authorized
        }
    }
    
    /// Request photo library permission
    func requestPhotoLibraryPermission() async -> Bool {
        Logger.info("📚 Requesting photo library permission", tag: "PermissionManager")
        
        if #available(iOS 14, *) {
            let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
            
            switch status {
            case .authorized, .limited:
                Logger.info("✅ Photo library permission already granted", tag: "PermissionManager")
                return true
                
            case .notDetermined:
                Logger.info("🤔 Photo library permission not determined, requesting...", tag: "PermissionManager")
                return await withCheckedContinuation { continuation in
                    PHPhotoLibrary.requestAuthorization(for: .readWrite) { newStatus in
                        let granted = newStatus == .authorized || newStatus == .limited
                        Logger.info("📚 Photo library permission result: \(granted)", tag: "PermissionManager")
                        continuation.resume(returning: granted)
                    }
                }
                
            case .denied, .restricted:
                Logger.warning("❌ Photo library permission denied or restricted", tag: "PermissionManager")
                return false
                
            @unknown default:
                Logger.warning("❓ Unknown photo library permission status", tag: "PermissionManager")
                return false
            }
        } else {
            // iOS 13 and earlier
            let status = PHPhotoLibrary.authorizationStatus()
            
            switch status {
            case .authorized:
                Logger.info("✅ Photo library permission already granted", tag: "PermissionManager")
                return true
                
            case .notDetermined:
                Logger.info("🤔 Photo library permission not determined, requesting...", tag: "PermissionManager")
                return await withCheckedContinuation { continuation in
                    PHPhotoLibrary.requestAuthorization { newStatus in
                        let granted = newStatus == .authorized
                        Logger.info("📚 Photo library permission result: \(granted)", tag: "PermissionManager")
                        continuation.resume(returning: granted)
                    }
                }
                
            case .denied, .restricted:
                Logger.warning("❌ Photo library permission denied or restricted", tag: "PermissionManager")
                return false
                
            @unknown default:
                Logger.warning("❓ Unknown photo library permission status", tag: "PermissionManager")
                return false
            }
        }
    }
    
    // MARK: - Settings Navigation
    
    /// Open app settings for permission management
    func openAppSettings() {
        Logger.info("⚙️ Opening app settings for permission management", tag: "PermissionManager")
        
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
            Logger.error("❌ Could not create settings URL", tag: "PermissionManager")
            return
        }
        
        if UIApplication.shared.canOpenURL(settingsUrl) {
            UIApplication.shared.open(settingsUrl) { success in
                Logger.info("⚙️ Settings opened: \(success)", tag: "PermissionManager")
            }
        } else {
            Logger.error("❌ Cannot open settings URL", tag: "PermissionManager")
        }
    }
    
    // MARK: - Permission Status Descriptions
    
    /// Get user-friendly description for camera permission status
    func getCameraPermissionStatusDescription() -> String {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        
        switch status {
        case .authorized:
            return "Camera access granted"
        case .denied:
            return "Camera access denied. Please enable in Settings."
        case .restricted:
            return "Camera access restricted by device policy"
        case .notDetermined:
            return "Camera permission not requested yet"
        @unknown default:
            return "Unknown camera permission status"
        }
    }
    
    /// Get user-friendly description for photo library permission status
    func getPhotoLibraryPermissionStatusDescription() -> String {
        if #available(iOS 14, *) {
            let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
            
            switch status {
            case .authorized:
                return "Full photo library access granted"
            case .limited:
                return "Limited photo library access granted"
            case .denied:
                return "Photo library access denied. Please enable in Settings."
            case .restricted:
                return "Photo library access restricted by device policy"
            case .notDetermined:
                return "Photo library permission not requested yet"
            @unknown default:
                return "Unknown photo library permission status"
            }
        } else {
            let status = PHPhotoLibrary.authorizationStatus()
            
            switch status {
            case .authorized:
                return "Photo library access granted"
            case .denied:
                return "Photo library access denied. Please enable in Settings."
            case .restricted:
                return "Photo library access restricted by device policy"
            case .notDetermined:
                return "Photo library permission not requested yet"
            @unknown default:
                return "Unknown photo library permission status"
            }
        }
    }
}
