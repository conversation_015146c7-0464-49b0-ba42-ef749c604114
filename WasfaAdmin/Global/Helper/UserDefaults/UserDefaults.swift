//
//  UserDefaults.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import SecureDefaults
import UIKit

class UserDefaultsSecure: NSObject {
    let defaults = SecureDefaults()
    static let sharedInstance = UserDefaultsSecure()

    override private init() {
        if !defaults.isKeyCreated {
            defaults.password = UUID().uuidString
        }
    }

    static var isLoggedIn: Bool {
        get {
            UserDefaultsSecure.sharedInstance.defaults.bool(forKey: .isLoggedInKey)
        }
        set {
            UserDefaultsSecure.sharedInstance.defaults.set(newValue, forKey: .isLoggedInKey)
        }
    }

    static var deviceID: String {
        guard let uuid = UIDevice.current.identifierForVendor?.uuidString else { return UUID().uuidString }
        return uuid
    }

    func setGeneratedTokenStringValue(value: String?) {
        defaults.set(value, forKey: .tokenKey)
    }

    func getGeneratedToken() -> String? {
        return defaults.string(forKey: .tokenKey)
    }

    // MARK: - Remember Me Token Management

    /// Sets token with Remember Me preference using TokenManager
    /// - Parameters:
    ///   - token: The authentication token to store
    ///   - rememberMe: Whether to store the token persistently
    func setTokenWithRememberMe(_ token: String?, rememberMe: Bool) {
        TokenManager.shared.setToken(token, rememberMe: rememberMe)
        Logger.debug("🔐 Token set via TokenManager with rememberMe: \(rememberMe)", tag: "UserDefaultsSecure")
    }

    /// Gets the current token using TokenManager (respects Remember Me preference)
    /// - Returns: The current authentication token if available
    func getTokenFromManager() -> String? {
        let token = TokenManager.shared.getToken()
        Logger.debug("🔍 Token retrieved via TokenManager: \(token != nil ? "exists" : "nil")", tag: "UserDefaultsSecure")
        return token
    }

    /// Checks if user has Remember Me enabled
    /// - Returns: True if Remember Me is enabled, false otherwise
    func isRememberMeEnabled() -> Bool {
        return TokenManager.shared.getRememberMePreference()
    }

//    func setGeneratedToken(value: Data) {
//        defaults.set(value, forKey: .tokenDataKey)
//    }
//
//    func getGeneratedToken() -> CreateBaseUrlGenerateTokenResponseData? {
//        if let data = defaults.data(forKey: .tokenDataKey) {
//            do {
//                return try JSONDecoder()
//                    .decode(CreateBaseUrlGenerateTokenResponseData.self, from: data)
//            } catch {
//                print("Unable to Decode")
//            }
//        }
//        return nil
//    }

    func setRegistrationData(value: Data) {
        Logger.debug("Setting registration data: \(value)", tag: "UserDefaults")
        defaults.set(value, forKey: .registrationDataKey)
    }

//    func getRegistrationData() -> UserRegister? {
//        if let data = defaults.data(forKey: .registrationDataKey) {
//            do {
//                let userRegister = try JSONDecoder().decode(UserRegister.self, from: data)
//                return userRegister
//            } catch {
//                print("Unable to Decode")
//            }
//        }
//        return nil
//    }

    // MARK: set and get user perform on local data

    func setUser(value: Data) {
        defaults.set(value, forKey: .userKey)
    }

//    func getUser() -> UserModel? {
//        if let data = defaults.data(forKey: .userKey) {
//            do {
//
//                let userModel = try JSONDecoder().decode(UserModel.self, from: data)
//                return userModel
//            } catch {
//                print("Unable to Decode")
//            }
//        }
//        return nil
//    }
}

extension String {
    // MARK: user keys

    static let userKey = "user_key"
    static let registrationDataKey = "registered_user_data_key"
    static let tokenDataKey = "generated_token_key"
    static let tokenKey = "generated_token_string_key"
    static let isLoggedInKey = "is_logged_in_key"
}


