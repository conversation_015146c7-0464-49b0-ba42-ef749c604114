//
//  TokenManager.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import Foundation

/// Centralized token management service that handles both persistent and temporary token storage
/// based on the user's "Remember Me" preference
class TokenManager: NSObject {
    static let shared = TokenManager()
    
    // MARK: - Private Properties
    
    /// In-memory token storage for temporary sessions (when Remember Me is disabled)
    private var temporaryToken: String?
    
    /// Reference to secure persistent storage
    private let secureStorage = UserDefaultsSecure.sharedInstance
    
    /// Key for storing Remember Me preference
    private let rememberMeKey = "remember_me_preference"
    
    // MARK: - Initialization
    
    override private init() {
        super.init()
        Logger.debug("✅ TokenManager initialized", tag: "TokenManager")

        // Safety check: Clear temporary tokens on startup if Remember Me is disabled
        // This handles cases where the app was force-closed and lifecycle methods weren't called
        validateTokenStateOnStartup()
    }

    /// Validates token state on app startup and clears temporary tokens if needed
    private func validateTokenStateOnStartup() {
        // Check if Remember Me preference has been explicitly set
        let hasRememberMePreference = UserDefaults.standard.object(forKey: rememberMeKey) != nil
        let rememberMe = getRememberMePreference()

        // Only clear temporary tokens if we have an explicit preference
        if hasRememberMePreference && !rememberMe && temporaryToken != nil {
            Logger.info("🧹 Startup cleanup: Clearing stale temporary token", tag: "TokenManager")
            temporaryToken = nil
        }

        // Only clear persistent tokens if Remember Me is explicitly disabled
        // Don't clear if no preference is set (could be first launch or migration pending)
        if hasRememberMePreference && !rememberMe && secureStorage.getGeneratedToken() != nil {
            Logger.info("🧹 Startup cleanup: Clearing stale persistent token (Remember Me explicitly disabled)", tag: "TokenManager")
            secureStorage.setGeneratedTokenStringValue(value: nil)
        } else if !hasRememberMePreference && secureStorage.getGeneratedToken() != nil {
            Logger.info("🔄 Startup: Found persistent token with no Remember Me preference - will be handled by migration", tag: "TokenManager")
        }
    }
    
    // MARK: - Public Interface
    
    /// Sets the authentication token with Remember Me preference
    /// - Parameters:
    ///   - token: The authentication token to store
    ///   - rememberMe: Whether to store the token persistently
    func setToken(_ token: String?, rememberMe: Bool) {
        Logger.debug("🔐 Setting token with rememberMe: \(rememberMe)", tag: "TokenManager")
        
        // Store Remember Me preference
        setRememberMePreference(rememberMe)
        
        if rememberMe {
            // Store token persistently and clear temporary storage
            secureStorage.setGeneratedTokenStringValue(value: token)
            temporaryToken = nil
            Logger.info("💾 Token stored persistently", tag: "TokenManager")
        } else {
            // Store token temporarily and clear persistent storage
            temporaryToken = token
            secureStorage.setGeneratedTokenStringValue(value: nil)
            Logger.info("🧠 Token stored in memory only", tag: "TokenManager")
        }
    }
    
    /// Retrieves the current authentication token
    /// - Returns: The current token if available, nil otherwise
    func getToken() -> String? {
        let rememberMe = getRememberMePreference()
        
        if rememberMe {
            // Get token from persistent storage
            let persistentToken = secureStorage.getGeneratedToken()
            Logger.debug("📱 Retrieved persistent token: \(persistentToken != nil ? "exists" : "nil")", tag: "TokenManager")
            return persistentToken
        } else {
            // Get token from temporary storage
            Logger.debug("🧠 Retrieved temporary token: \(temporaryToken != nil ? "exists" : "nil")", tag: "TokenManager")
            return temporaryToken
        }
    }
    
    /// Clears all stored tokens (both persistent and temporary)
    func clearAllTokens() {
        Logger.info("🧹 Clearing all tokens", tag: "TokenManager")
        temporaryToken = nil
        secureStorage.setGeneratedTokenStringValue(value: nil)
        clearRememberMePreference()
    }
    
    /// Clears only temporary tokens (called on app lifecycle events)
    func clearTemporaryTokens() {
        Logger.info("🧹 Clearing temporary tokens", tag: "TokenManager")
        temporaryToken = nil
    }
    
    /// Checks if a valid token exists
    /// - Returns: True if a token is available, false otherwise
    func hasValidToken() -> Bool {
        let token = getToken()
        let hasToken = token != nil && !token!.isEmpty
        Logger.debug("🔍 Token validation: \(hasToken ? "valid" : "invalid")", tag: "TokenManager")
        return hasToken
    }
    
    /// Gets the current Remember Me preference
    /// - Returns: True if Remember Me is enabled, false otherwise
    func getRememberMePreference() -> Bool {
        // Check if preference has been explicitly set
        if let preference = UserDefaults.standard.object(forKey: rememberMeKey) as? Bool {
            Logger.debug("📋 Remember Me preference (explicit): \(preference)", tag: "TokenManager")
            return preference
        } else {
            // No preference set yet - check if there's a persistent token (backward compatibility)
            let hasToken = secureStorage.getGeneratedToken() != nil
            Logger.debug("📋 Remember Me preference (inferred from token): \(hasToken)", tag: "TokenManager")
            return hasToken
        }
    }
    
    // MARK: - Private Methods
    
    /// Sets the Remember Me preference
    /// - Parameter rememberMe: The preference value to store
    private func setRememberMePreference(_ rememberMe: Bool) {
        UserDefaults.standard.set(rememberMe, forKey: rememberMeKey)
        Logger.debug("💾 Remember Me preference saved: \(rememberMe)", tag: "TokenManager")
    }
    
    /// Clears the Remember Me preference
    private func clearRememberMePreference() {
        UserDefaults.standard.removeObject(forKey: rememberMeKey)
        Logger.debug("🧹 Remember Me preference cleared", tag: "TokenManager")
    }
}

// MARK: - App Lifecycle Support

extension TokenManager {
    /// Called when app enters background - clears temporary tokens if Remember Me is disabled
    func handleAppDidEnterBackground() {
        let rememberMe = getRememberMePreference()
        if !rememberMe {
            Logger.info("📱 App entered background - clearing temporary token", tag: "TokenManager")
            clearTemporaryTokens()
        }
    }
    
    /// Called when app will terminate - clears temporary tokens if Remember Me is disabled
    func handleAppWillTerminate() {
        let rememberMe = getRememberMePreference()
        if !rememberMe {
            Logger.info("📱 App will terminate - clearing temporary token", tag: "TokenManager")
            clearTemporaryTokens()
        }
    }
    
    /// Called when app becomes active - validates token state
    func handleAppDidBecomeActive() {
        let hasToken = hasValidToken()
        let rememberMe = getRememberMePreference()
        Logger.info("📱 App became active - Token: \(hasToken ? "valid" : "invalid"), RememberMe: \(rememberMe)", tag: "TokenManager")
    }
}

// MARK: - Backward Compatibility

extension TokenManager {
    /// Migrates existing tokens to the new system
    /// This ensures existing users don't lose their authentication state
    func migrateExistingToken() {
        // Check if Remember Me preference has already been set
        let hasRememberMePreference = UserDefaults.standard.object(forKey: rememberMeKey) != nil

        // Only migrate if no preference is set yet
        if !hasRememberMePreference {
            // Check if there's an existing token in the old system
            if let existingToken = secureStorage.getGeneratedToken(), !existingToken.isEmpty {
                // Assume Remember Me was enabled for existing users (backward compatibility)
                Logger.info("🔄 Migrating existing token with Remember Me enabled", tag: "TokenManager")
                setToken(existingToken, rememberMe: true)
            } else {
                Logger.info("🔄 Migration: No existing token found", tag: "TokenManager")
            }
        } else {
            Logger.info("🔄 Migration: Remember Me preference already set, skipping migration", tag: "TokenManager")
        }
    }
}
