//
// ViewportHelper.swift
//

//import Foundation
//import UIKit
//
//func getRelativeWidth(_ size: CGFloat) -> CGFloat {
//    return size * (CGFloat(UIScreen.main.bounds.width) / 390.0)
//}
//
//func getRelativeHeight(_ size: CGFloat) -> CGFloat {
//    return (size * (CGFloat(UIScreen.main.bounds.height) / 844.0)) * 0.97
//}
//
//func getRelativeFontSize(_ size: CGFloat) -> CGFloat {
//    return size * (CGFloat(UIScreen.main.bounds.width) / 390.0)
//}
//
//
//extension Double {
//    
//    var relativeWidth:Double {
//        self * (CGFloat(UIScreen.main.bounds.width) / 390.0)
//    }
//    
//    var relativeHeight:Double {
//        (self * (CGFloat(UIScreen.main.bounds.height) / 844.0)) * 0.97
//    }
//    
//    var relativeFontSize:Double {
//        self * (CGFloat(UIScreen.main.bounds.width) / 390.0)
//    }
//
//}

