<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_85_1551)">
<rect width="32" height="32" rx="10" transform="matrix(-1 0 0 1 50 20)" fill="#F7F7F7"/>
<path d="M36.5 30L30.5 36L36.5 42" stroke="#A61C5C" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_d_85_1551" x="0" y="0" width="72" height="72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2"/>
<feGaussianBlur stdDeviation="10"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.65098 0 0 0 0 0.109804 0 0 0 0 0.360784 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_85_1551"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_85_1551" result="shape"/>
</filter>
</defs>
</svg>
