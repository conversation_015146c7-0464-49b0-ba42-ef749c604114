//
//  NetworkImageView.swift
//  Wasfa
//
//  Created by Apple on 28/10/2024.
//

import SDWebImageSwiftUI
import SwiftUI

typealias ImageSuccessHandler = (PlatformImage, Data?, SDImageCacheType) -> Void
typealias ImageFailureHandler = (Error) -> Void

struct NetworkImageView: View {
    let originalColor: Bool
    let path: String?
    let contentMode: ContentMode
    let onFailure: ImageFailureHandler?
    let onSuccess: ImageSuccessHandler?

    private static let defaultPlaceholder = Image(systemName: "photo")

    init(path: String?,
         contentMode: ContentMode = .fit,
         originalColor: Bool = true,
         onSuccess: ImageSuccessHandler? = nil,
         onFailure: ImageFailureHandler? = nil)
    {
        self.originalColor = originalColor
        self.path = path
        self.onSuccess = onSuccess
        self.onFailure = onFailure
        self.contentMode = contentMode
    }


    var body: some View {
        if let path = path, !path.isEmpty {
            let imageURL = path.starts(with: "http") ? URL(string: path) : nil

            if let url = imageURL {
                WebImage(url: url, options: [.lowPriority, .scaleDownLargeImages]) { image in
                    image
                        .resizable()
                } placeholder: {
                    placeholder
                }
                .onSuccess { image, data, cacheType in
                    onSuccess?(image, data, cacheType)
                }
                .onFailure { error in
                    print("Image load failed: \(error.localizedDescription)")
                    onFailure?(error)
                }
                .indicator(.activity)
                .transition(.fade(duration: 0.3))
                .aspectRatio(contentMode: contentMode)
            } else {
                Image(path)
                    .renderingMode(originalColor ? .original : .template)
                    .resizable()
                    .aspectRatio(contentMode: contentMode)
            }
        } else {
            placeholder
        }
    }

    private var placeholder: some View {
        Self.defaultPlaceholder
            .resizable()
            .scaledToFit()
            .foregroundStyle(.gray.opacity(0.6))
    }
}

// Prefetching extension
extension NetworkImageView {
    static func prefetch(urls: [String]) {
        let urls = urls.compactMap { URL(string: $0) }
        SDWebImagePrefetcher.shared.prefetchURLs(urls)
    }

    static func cancelPrefetching() {
        SDWebImagePrefetcher.shared.cancelPrefetching()
    }
}
