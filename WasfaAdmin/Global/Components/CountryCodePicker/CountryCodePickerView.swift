//
//  CountryCodePickerView.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI

// MARK: - Country Code Picker Component

struct CountryCodePickerView: View {
    var showBorder: Bool = false

    // MARK: - Properties

    @Binding var selectedCountryCode: CountryCode

    // MARK: - Body

    var body: some View {
        Menu {
            ForEach(CountryCode.allCases) { country in
                Button(action: {
                    selectedCountryCode = country
                }) {
                    HStack {
                        HStack(spacing: 4) {
                            Text("\(country.flag) (\(country.dialCode)) \(country.countryName)")
                        }
                        // add custom font named "Poppins" with 16px size and semibold
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(ColorConstants.Black90001)
                        .frame(maxWidth: .infinity, alignment: .leading)

                        if selectedCountryCode == country {
                            Image(systemName: "checkmark.circle")
                                .foregroundColor(.green)
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 4) {
                Text(selectedCountryCode.flag)
                Text(selectedCountryCode.dialCode)
            }
            .font(.custom("Roboto-Regular", size: 14))
            .frame(width: 65)
            .padding(.vertical, 8)
            .padding(.horizontal, 4)
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(.clear)
//                    .shadow(color: ColorConstants.Black9003f.opacity(0.1), radius: 2)
            )
        }
        .tint(ColorConstants.Black90001)
    }

    // MARK: - Binding Accessor

    func countryCodeBinding() -> Binding<CountryCode> {
        Binding(
            get: { selectedCountryCode },
            set: { selectedCountryCode = $0 }
        )
    }

    // MARK: - Helper Methods

    func dialCode() -> String {
        selectedCountryCode.dialCode
    }
}

// MARK: - Country Code Model

public enum CountryCode: String, CaseIterable, Identifiable {
    case kuwait
    case uae
    case bahrain
    case qatar
    case oman
    case saudiArabia

    public var id: String { rawValue }

    var flag: String {
        switch self {
        case .kuwait: return "🇰🇼"
        case .uae: return "🇦🇪"
        case .bahrain: return "🇧🇭"
        case .qatar: return "🇶🇦"
        case .oman: return "🇴🇲"
        case .saudiArabia: return "🇸🇦"
        }
    }

    var dialCode: String {
        switch self {
        case .kuwait: return "+965"
        case .uae: return "+971"
        case .bahrain: return "+973"
        case .qatar: return "+974"
        case .oman: return "+968"
        case .saudiArabia: return "+966"
        }
    }

    var countryName: String {
        switch self {
        case .kuwait: return "Kuwait"
        case .uae: return "UAE"
        case .bahrain: return "Bahrain"
        case .qatar: return "Qatar"
        case .oman: return "Oman"
        case .saudiArabia: return "Saudi Arabia"
        }
    }
}
