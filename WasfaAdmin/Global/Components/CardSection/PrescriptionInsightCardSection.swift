//
//  PrescriptionInsightCardSection.swift
//  WasfaAdmin
//
//  Created by Apple on 28/06/2025.
//

import SwiftUI

struct PrescriptionInsightCardSection: View {
    @ObservedObject var viewModel: HomeViewModel
    var body: some View {
        VStack(spacing: 16.relativeHeight) {
            ScrollView(.horizontal) {
                LazyHStack(spacing: 12.relativeWidth) {
                    ForEach(viewModel.prescriptionCardViewList) { item in
                        item
                            .id(item.id) // Ensure each item has a unique id
                    }
                }
                .scrollTargetLayout()
            }
            .scrollTargetBehavior(.viewAligned) // ⬅️ Enables snapping/paging
            .scrollPosition(id: $viewModel.selectedPrescriptionCardView)
            .scrollClipDisabled()

            ScrollProgressIndicator(
                totalSteps: 4,
                currentStep: $viewModel.selectedPrescriptionCardView
            )
        }
    }
}
