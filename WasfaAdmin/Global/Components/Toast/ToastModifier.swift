//
//  ToastModifier.swift
//  Alwasmmarine
//
//  Created by Apple on 22/12/23.
//

import SwiftUI
import SwipeActions

struct ToastModifier: ViewModifier {
    @Binding var toast: [Toast]
    @State private var workItem: DispatchWorkItem?

    func body(content: Content) -> some View {
        content
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .overlay(
                ZStack {
                    mainToastView()

                }.animation(.spring(), value: toast)
            )
            .onChange(of: toast, { _, _ in
                withAnimation(.bouncy) {
                    showToast()
                }
            })
    }

    @ViewBuilder func mainToastView() -> some View {
        if !toast.isEmpty {
            ForEach(Array(toast.enumerated()), id: \.element.id) { index, item in
                VStack {
                    Spacer()

                    SwipeView {
                        ToastView(
                            type: item.type,
                            title: item.title ?? "",
                            message: item.message) {
                                dismissToast(index: index)
                            }
                    } trailingActions: { _ in

                        SwipeAction {
                            dismissToast(index: index)
                        } label: { _ in
                            EmptyView()
                        } background: { _ in
                            EmptyView()
                        }.allowSwipeToTrigger()
                    }
                }
                .transition(.asymmetric(insertion: .move(edge: .bottom), removal: .move(edge: .leading)))
                .offset(y: CGFloat(-30 + (10 * index)))
            }
        }
    }

    private func showToast() {
        UIImpactFeedbackGenerator(style: .light).impactOccurred()

        toast.enumerated().forEach { index, toast in
            
            if toast.duration > 0 && toast.withTimer {
                workItem?.cancel()

                let task = DispatchWorkItem {
                    dismissToast(index: index)
                }

                workItem = task
                DispatchQueue.main.asyncAfter(deadline: .now() + toast.duration, execute: task)
            }
        }
    }

    private func dismissToast(index: Int) {
        if toast.indices.contains(index) {
         let _ =  withAnimation(.bouncy) {
                toast.remove(at: index)
            }
            workItem?.cancel()
            workItem = nil
        }
    }
}

extension View {
    func toastView(toast: Binding<[Toast]>) -> some View {
        modifier(ToastModifier(toast: toast))
    }
}
