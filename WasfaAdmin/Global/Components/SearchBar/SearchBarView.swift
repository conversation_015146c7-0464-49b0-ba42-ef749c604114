//
//  SearchBarView.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI

/// Reusable search bar component matching the Android design from fragment_meditation.xml
public struct SearchBarView: View {
    @Binding var searchText: String
    let placeholder: String
    let showFilterIndicator:Bool
    let onSearchChanged: ((String) -> Void)?
    let onFilterTapped: (() -> Void)?
    let showFilterButton: Bool
    let filterIcon: String

    public init(
        searchText: Binding<String>,
        placeholder: String = "Search... ",
        showFilterIndicator:Bool = false,
        onSearchChanged: ((String) -> Void)? = nil,
        onFilterTapped: (() -> Void)? = nil,
        showFilterButton: Bool = true,
        filterIcon: String = "line.3.horizontal.decrease.circle"
    ) {
        self._searchText = searchText
        self.placeholder = placeholder
        self.showFilterIndicator = showFilterIndicator
        self.onSearchChanged = onSearchChanged
        self.onFilterTapped = onFilterTapped
        self.showFilterButton = showFilterButton
        self.filterIcon = filterIcon
    }
    
    public var body: some View {
        
        HStack {
            Image(.searchIcon)
                .resizable()
                .frame(width: 35, height: 35)
                .padding(.leading, 10)

            TextField("Search... ", text: $searchText)
                .font(.custom("Roboto-Regular", size: 15))
                .foregroundColor(.black)
                .background(Color.clear)
                .onChange(of: searchText) { _, newValue in
                    onSearchChanged?(newValue)
                }

            Button { onFilterTapped?()} label: {
                Image(.prescriptionSearchFilter)
                    .resizable()
                    .frame(width: 35, height: 35)
                    .padding(.horizontal, 8)
                    .overlay(alignment:.topTrailing){
                        if showFilterIndicator {
                            Circle()
                                .fill(Color.red)
                                .frame(width: 10, height: 10)
                                .shadow(radius: 1)
                                .offset(x: -10, y: 0)
                        }
                    }
            }
          
        }
        .frame(height: 50)
        .background(Color.white)
        .clipShape(.capsule)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        // Default search bar
        SearchBarView(
            searchText: .constant(""),
            onSearchChanged: { text in
                print("Search changed: \(text)")
            },
            onFilterTapped: {
                print("Filter tapped")
            }
        )
        
        // Search bar without filter
        SearchBarView(
            searchText: .constant("Sample search"),
            placeholder: "Search medications...",
            showFilterIndicator: true,
            showFilterButton: false
        )
        
        // Custom filter icon
        SearchBarView(
            searchText: .constant(""),
            placeholder: "Search products...",
            filterIcon: "slider.horizontal.3"
        )
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
