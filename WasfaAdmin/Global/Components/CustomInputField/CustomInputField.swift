//
//  CustomInputField.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI
import UIKit

/// Custom input field component matching the Android settings design
public struct CustomInputField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    var isSecure: Bool = false
    var keyboardType: UIKeyboardType = .default
    var isDateField: Bool = false
    let cornerRadius: CGFloat
    let height: CGFloat
    var onDateFieldTapped: (() -> Void)?
    var onTextChanged: ((String) -> Void)?
    var errorMessage: String?

    @State private var isPasswordVisible: Bool = false

    public init(
        title: String,
        text: Binding<String>,
        placeholder: String,
        isSecure: Bool = false,
        keyboardType: UIKeyboardType = .default,
        isDateField: Bool = false,
        cornerRadius: CGFloat = 6,
        height: CGFloat = 44,
        onDateFieldTapped: (() -> Void)? = nil,
        onTextChanged: ((String) -> Void)? = nil,
        errorMessage: String? = nil
    ) {
        self.title = title
        self._text = text
        self.placeholder = placeholder
        self.isSecure = isSecure
        self.keyboardType = keyboardType
        self.isDateField = isDateField
        self.cornerRadius = cornerRadius
        self.height = height
        self.onDateFieldTapped = onDateFieldTapped
        self.onTextChanged = onTextChanged
        self.errorMessage = errorMessage
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            // Title Label - matching Android style
            Text(title)
                .font(.custom("Roboto-Bold", size: 16))
                .foregroundColor(.black)

            // Input Field Container - matching Android MaterialCardView
            if isDateField {
                // Date field as button (like Android TextView)
                Button(action: {
                    onDateFieldTapped?()
                }) {
                    HStack {
                        Text(text.isEmpty ? placeholder : text)
                            .font(.custom("Roboto-Regular", size: 14))
                            .foregroundColor(Color(hex: "#544C4C") ?? .gray)
                            .frame(maxWidth: .infinity, alignment: .leading)

                        Spacer()
                    }
                    .padding(.horizontal, 16)
                    .frame(height: 44)
                    .background(Color.white)
                    .cornerRadius(6)
                    .overlay(
                        RoundedRectangle(cornerRadius: 6)
                            .stroke(Color(hex: "#24544C4C") ?? Color.gray.opacity(0.14), lineWidth: 1)
                    )
                }
            } else {
                // Regular input field
                HStack {
                    Group {
                        if isSecure && !isPasswordVisible {
                            SecureField(placeholder, text: $text)
                        } else {
                            TextField(placeholder, text: $text)
                        }
                    }
                    .font(.custom("Roboto-Regular", size: 14))
                    .foregroundColor(Color(hex: "#544C4C") ?? .gray)
                    .autocapitalization(.none)
                    .keyboardType(keyboardType)
                    .textContentType(getTextContentType())
                    .onChange(of: text) { _, newValue in
                        onTextChanged?(newValue)
                    }

                    // Password visibility toggle
                    if isSecure {
                        Button(action: {
                            isPasswordVisible.toggle()
                        }) {
                            Image(systemName: !isPasswordVisible ? "eye.slash" : "eye")
                                .foregroundColor(Color(hex: "#544C4C") ?? .gray)
                                .font(.system(size: 16))
                        }
                    }
                }
                .padding(.horizontal, 16)
                .frame(height: height)
                .background(Color.white)
                .cornerRadius(cornerRadius)
                .overlay(
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(errorMessage != nil ? Color.red : (Color(hex: "#24544C4C") ?? Color.gray.opacity(0.14)), lineWidth: 1)
                )
            }

            // Error Message
            if let errorMessage = errorMessage, !errorMessage.isEmpty {
                Text(errorMessage)
                    .font(.custom("Roboto-Regular", size: 12))
                    .foregroundColor(.red)
                    .padding(.leading, 20)
            }
        }
    }

    private func getTextContentType() -> UITextContentType? {
        if title.lowercased().contains("email") {
            return .emailAddress
        } else if isSecure {
            return .password
        } else if title.lowercased().contains("phone") {
            return .telephoneNumber
        }
        return .none
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        CustomInputField(
            title: "Name",
            text: .constant(""),
            placeholder: "name"
        )

        CustomInputField(
            title: "Email",
            text: .constant(""),
            placeholder: "email",
            keyboardType: .emailAddress
        )

        CustomInputField(
            title: "Phone Number",
            text: .constant(""),
            placeholder: "phone number",
            keyboardType: .phonePad
        )

        CustomInputField(
            title: "Date of Birth",
            text: .constant(""),
            placeholder: "choose date",
            isDateField: true,
            onDateFieldTapped: {
                print("Date field tapped")
            }
        )
    }
    .padding()
    .background(Color.white)
}
