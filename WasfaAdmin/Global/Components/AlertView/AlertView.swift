//
//  AlertView.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//

import SwiftUI

struct AlertView: View {
    @Binding var pageState: PageState
    let config: AlertConfig
    var body: some View {
        switch config.alertType {
        case .alert:
            ZStack {
                VStack(alignment: .leading) {
                    Text(config.title)
                        .font(Font.custom("Montserrat", size: 18).weight(.medium))
                        .foregroundColor(.black)

                    Text(config.text)
                        .font(Font.custom("Montserrat", size: 13).weight(.medium))
                        .foregroundColor(Color(red: 0.47, green: 0.47, blue: 0.47))
                        .padding(.vertical, 8)

                    HStack {
                        Button {
                            pageState = .stable
                            config.onOk?()
                        } label: {
                            VStack {
                                Text("Ok")
                                    .font(Font.custom("Montserrat", size: 15).weight(.bold))
                                    .foregroundColor(.white)
                            }
                            .foregroundColor(.clear)
                            .frame(height: 36)
                            .frame(maxWidth: .infinity)
                            .background(.doctorMain)
                            
                            .cornerRadius(5)
                            .overlay(
                                RoundedRectangle(cornerRadius: 5)
                                    .inset(by: 0.50)
                                    .stroke(.doctorMain, lineWidth: 0.50)
                            )
                        }
                    }
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(.white)
                .cornerRadius(6)
                .padding()
                
            }

        case .choiceAlert:
            ZStack {
                VStack(alignment: .leading) {
                    Text(config.title)
                        .font(Font.custom("Montserrat", size: 18).weight(.medium))
                        .foregroundColor(.black)

                    Text(config.text)
                        .font(Font.custom("Montserrat", size: 13).weight(.medium))
                        .foregroundColor(Color(red: 0.47, green: 0.47, blue: 0.47))
                        .padding(.vertical, 8)

                    HStack {
                        Button {
                            pageState = .stable
                            config.onCancel?()

                        } label: {
                            VStack {
                                Text(config.cancelButtonText)
                                    .font(Font.custom("Montserrat", size: 13).weight(.medium))
                                    .foregroundColor(.white)
                            }
                            .foregroundColor(.clear)
                            .frame(height: 36)
                            .frame(maxWidth: .infinity)
                            .background(.doctorMain.opacity(0.3))
                            .cornerRadius(5)
                            .overlay(
                                RoundedRectangle(cornerRadius: 5)
                                    .inset(by: 0.50)
                                    .stroke(.doctorMain.opacity(0.3), lineWidth: 0.50)
                            )
                        }

                        Button {
                            pageState = .stable
                            config.onOk?()
                        } label: {
                            VStack {
                                Text(config.okButtonText)
                                    .font(Font.custom("Montserrat", size: 13).weight(.medium))
                                    .foregroundColor(.white)
                            }
                            .foregroundColor(.clear)
                            .frame(height: 36)
                            .frame(maxWidth: .infinity)
                            .background(.doctorMain)
                            .cornerRadius(5)
                            .overlay(
                                RoundedRectangle(cornerRadius: 5)
                                    .inset(by: 0.50)
                                    .stroke(Color(red: 0.4, green: 0.7, blue: 0.27), lineWidth: 0.50)
                            )
                        }
                    }
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(.white)
                .cornerRadius(6)
                .padding()
            }
        }
    }
}

#Preview {
    VStack {
        
        AlertView(pageState: .constant(.message(config: AlertConfig(title: "Hello", text: "Hi",onCancel: {}, onOk: {}))), config: AlertConfig(title: "Hello", text: "Hi",alertType: .choiceAlert, onCancel: {},onOk: {}))
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(.gray)
}
