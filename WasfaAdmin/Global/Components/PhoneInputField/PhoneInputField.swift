//
//  PhoneInputField.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI

/// Custom phone input field component with Kuwait country code
public struct PhoneInputField: View {
    let title: String
    @Binding var text: String
    @Binding var countryCode: CountryCode
    let placeholder: String
    let cornerRadius: CGFloat
    let height: CGFloat
    var errorMessage: String?
    var onTextChanged: ((String) -> Void)?

    public init(
        title: String,
        text: Binding<String>,
        countryCode: Binding<CountryCode>,
        placeholder: String,
        cornerRadius: CGFloat = 15,
        height: CGFloat = 53,
        errorMessage: String? = nil,
        onTextChanged: ((String) -> Void)? = nil
    ) {
        self.title = title
        self._text = text
        self._countryCode = countryCode
        self.placeholder = placeholder
        self.cornerRadius = cornerRadius
        self.height = height
        self.errorMessage = errorMessage
        self.onTextChanged = onTextChanged
    }
    
    public var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            // Title Label
            Text(title)
                .font(.custom("Roboto-Medium", size: 14))
                .foregroundColor(.black)
            
            // Phone Input Field with Kuwait Flag
            HStack(spacing: 0) {
                // Kuwait Flag and Country Code
                CountryCodePickerView(showBorder: true, selectedCountryCode: $countryCode)
//                    .frame(maxWidth: 45, maxHeight: 26)
                    .padding(.leading, 10)
                    .onChange(of: countryCode) { oldValue, newValue in
                        if oldValue != newValue { onTextChanged?(text)}
                    }
                // Separator Line
                Rectangle()
                    .fill(Color.black.opacity(0.21))
                    .frame(width: 1, height: 31)
                
                // Phone Number Input
                TextField(placeholder, text: $text)
                    .font(.custom("Roboto-Regular", size: 14))
                    .foregroundColor(.black)
                    .keyboardType(.phonePad)
                    .padding(.leading, 12)
                    .padding(.trailing, 10)
                    .onChange(of: text) { _, newValue in
                        onTextChanged?(newValue)
                    }
            }
            .frame(height: height)
            .background(Color.white)
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(Color.black.opacity(0.17), lineWidth: 1)
            )
            
            // Error Message
            if let errorMessage = errorMessage, !errorMessage.isEmpty {
                Text(errorMessage)
                    .font(.custom("Roboto-Regular", size: 12))
                    .foregroundColor(.red)
                    .padding(.leading, 20)
            }
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        PhoneInputField(
            title: "Phone Number",
            text: .constant(""),
            countryCode: .constant(.kuwait),
            placeholder: "phone number"
        )
        
        PhoneInputField(
            title: "Phone Number",
            text: .constant("12345678"),
            countryCode: .constant(.kuwait),
            placeholder: "phone number",
            errorMessage: "Please enter a valid phone number"
        )
    }
    .padding()
}
