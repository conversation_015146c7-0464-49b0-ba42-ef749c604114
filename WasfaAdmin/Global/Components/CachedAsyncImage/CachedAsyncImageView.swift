//
//  CachedAsyncImageView.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI

//struct CachedAsyncImageView: View {
//    let imageUrl: String?
//    let placeholder: Image
//    let errorImage: Image
//
//    var body: some View {
//        if let imageUrl = imageUrl, let url = URL(string: imageUrl) {
//            AsyncImage(url: url, transaction: Transaction(animation: .easeInOut)) { phase in
//                switch phase {
//                case .empty:
//                    ZStack {
//                        placeholder
//                            .resizable()
//                            .scaledToFit()
//                            .opacity(0.4)
//                            .padding()
//
//                        ProgressView() // ✅ Loading indicator
//                    }
//
//                case .success(let image):
//                    image
//                        .resizable()
//                        .aspectRatio(contentMode: .fill)
//
//                case .failure:
//                    errorImage
//                        .resizable()
//                        .scaledToFit()
//                        .opacity(0.5)
//                        .padding()
//
//                @unknown default:
//                    EmptyView()
//                }
//            }
//        } else {
//            errorImage
//                .resizable()
//                .scaledToFit()
//                .opacity(0.5)
//                .padding()
//        }
//    }
//}
