//
//  Untitled.swift
//  Wasfa
//
//  Created by Apple on 09/02/2025.
//

import SwiftUI
import RichText

struct CustomRichText: View {
    let html, fontFamily, fontSrc, sizeAdjust: String
    let fontColor:Color
    var lineHeight:CGFloat = 100
    var body: some View {
        RichText(html: html)
            .lineHeight(lineHeight)
            .colorScheme(.auto)
            .imageRadius(12)
            .fontType(.customName(fontFamily))
            .foregroundColor(light: fontColor, dark: fontColor)
            .linkColor(light:Color(.systemBlue) , dark: Color(.systemBlue))
            .colorPreference(forceColor: .onlyLinks)
            .linkOpenType(.SFSafariView())
            .customCSS("""
             @font-face {
                    font-family: '\(fontFamily)';
                    src: url("\(fontSrc)") format('truetype');
                    size-adjust: \(sizeAdjust)%;
                    }
            """)
            .placeholder {
                ProgressView()
                    
            }
            .transition(.easeOut)
    }
}
