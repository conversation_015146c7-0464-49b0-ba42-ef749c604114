//
//  DatePickerField.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI

/// Custom date picker field component matching Android design
public struct DatePickerField: View {
    let title: String
    @Binding var selectedDate: Date?
    let placeholder: String
    let cornerRadius: CGFloat
    let height: CGFloat
    var errorMessage: String?
    @State private var showDatePicker = false



    public init(
        title: String,
        selectedDate: Binding<Date?>,
        placeholder: String,
        cornerRadius: CGFloat = 15,
        height: CGFloat = 53,
        errorMessage: String? = nil
    ) {
        self.title = title
        self._selectedDate = selectedDate
        self.placeholder = placeholder
        self.cornerRadius = cornerRadius
        self.height = height
        self.errorMessage = errorMessage
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            // Title Label
            Text(title)
                .font(.custom("Roboto-Medium", size: 14))
                .foregroundColor(.black)

            // Date Picker Field
            Button(action: {
                showDatePicker = true
            }) {
                HStack {
                    Text((selectedDate != nil ? selectedDate?.toString(outputFormate: "yyyy-MM-dd") : placeholder) ?? "")
                        .font(.custom("Roboto-Regular", size: 12))
                        .foregroundColor(selectedDate != nil ? .black : (Color(hex: "#9C9C9C") ?? .gray))
                        .frame(maxWidth: .infinity, alignment: .leading)

                    // Calendar Icon
                    Image(systemName: "calendar")
                        .foregroundColor(Color(hex: "#9C9C9C") ?? .gray)
                        .frame(width: 20, height: 20)
                }
                .padding(.horizontal, 20)
                .frame(height: height)
                .background(Color.white)
                .overlay(
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(Color.black.opacity(0.17), lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())

            // Error Message
            if let errorMessage = errorMessage, !errorMessage.isEmpty {
                Text(errorMessage)
                    .font(.custom("Roboto-Regular", size: 12))
                    .foregroundColor(.red)
                    .padding(.leading, 20)
            }
        }
        .sheet(isPresented: $showDatePicker) {
            DatePickerSheet(selectedDate: $selectedDate, showDatePicker: $showDatePicker)
                .presentationDetents([.medium])
        }
    }
}

// MARK: - Date Picker Sheet

private struct DatePickerSheet: View {
    @Binding var selectedDate: Date?
    @Binding var showDatePicker: Bool
    @State private var tempDate = Date()

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                Button("Cancel") {
                    showDatePicker = false
                }
                .padding()
                .foregroundColor(.red)

                Spacer()

                Button("Submit") {
                    selectedDate = tempDate
                    showDatePicker = false
                }
                .padding()
                .foregroundColor(.accentColor)
            }

            Divider()

            DatePicker("Select Date", selection: $tempDate,
                       displayedComponents: .date)
                .datePickerStyle(.graphical)
                .labelsHidden()
                .padding(.horizontal)

            Spacer()
        }
        .presentationDetents([.height(420.relativeHeight)])
        .onAppear {
            tempDate = selectedDate ?? Date()
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        DatePickerField(
            title: "Date",
            selectedDate: .constant(nil),
            placeholder: "choose date"
        )

        DatePickerField(
            title: "Date",
            selectedDate: .constant(Date()),
            placeholder: "choose date",
            errorMessage: "Please select a date"
        )
    }
    .padding()
}
