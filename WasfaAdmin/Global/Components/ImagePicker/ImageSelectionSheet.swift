//
//  ImageSelectionSheet.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI

/// Action sheet for selecting image source (camera or photo library)
struct ImageSelectionSheet: View {
    @Binding var isPresented: Bool
    let onCameraSelected: () -> Void
    let onPhotoLibrarySelected: () -> Void
    let onRemovePhoto: (() -> Void)?
    
    init(
        isPresented: Binding<Bool>,
        onCameraSelected: @escaping () -> Void,
        onPhotoLibrarySelected: @escaping () -> Void,
        onRemovePhoto: (() -> Void)? = nil
    ) {
        self._isPresented = isPresented
        self.onCameraSelected = onCameraSelected
        self.onPhotoLibrarySelected = onPhotoLibrarySelected
        self.onRemovePhoto = onRemovePhoto
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            VStack(spacing: 8) {
               
                Text("Select Profile Photo")
                    .font(.custom("Roboto-Medium", size: 18))
                    .foregroundColor(.black)
                    .padding(.top, 16)
                    .padding(.bottom, 8)
            }
            
            Divider()
                .padding(.horizontal, 16)
            
            // Options
            VStack(spacing: 0) {
                // Camera Option
                Button(action: {
                    Logger.info("📷 Camera option selected", tag: "ImageSelectionSheet")
                    isPresented = false
                    onCameraSelected()
                }) {
                    HStack(spacing: 16) {
                        Image(systemName: "camera.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.black)
                            .frame(width: 24, height: 24)
                        
                        Text("Take Photo")
                            .font(.custom("Roboto-Regular", size: 16))
                            .foregroundColor(.black)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
                
                Divider()
                    .padding(.horizontal, 16)
                
                // Photo Library Option
                Button(action: {
                    Logger.info("📚 Photo library option selected", tag: "ImageSelectionSheet")
                    isPresented = false
                    onPhotoLibrarySelected()
                }) {
                    HStack(spacing: 16) {
                        Image(systemName: "photo.on.rectangle")
                            .font(.system(size: 20))
                            .foregroundColor(.black)
                            .frame(width: 24, height: 24)
                        
                        Text("Choose from Library")
                            .font(.custom("Roboto-Regular", size: 16))
                            .foregroundColor(.black)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
                
                // Remove Photo Option (if available)
                if let removeAction = onRemovePhoto {
                    Divider()
                        .padding(.horizontal, 16)
                    
                    Button(action: {
                        Logger.info("🗑️ Remove photo option selected", tag: "ImageSelectionSheet")
                        isPresented = false
                        removeAction()
                    }) {
                        HStack(spacing: 16) {
                            Image(systemName: "trash")
                                .font(.system(size: 20))
                                .foregroundColor(.red)
                                .frame(width: 24, height: 24)
                            
                            Text("Remove Photo")
                                .font(.custom("Roboto-Regular", size: 16))
                                .foregroundColor(.red)
                            
                            Spacer()
                        }
                        .padding(.horizontal, 24)
                        .padding(.vertical, 16)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            
            // Cancel Button
            VStack(spacing: 0) {
                Divider()
                    .padding(.horizontal, 16)
                
                Button(action: {
                    Logger.info("🚫 Image selection cancelled", tag: "ImageSelectionSheet")
                    isPresented = false
                }) {
                    Text("Cancel")
                        .font(.custom("Roboto-Medium", size: 16))
                        .foregroundColor(Color(hex: "#A42161") ?? .blue)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // Bottom safe area
            Color.clear
                .frame(height: 20)
        }
        .background(Color.white)
       
    }
}

// MARK: - Corner Radius Extension

extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// MARK: - Preview

#Preview {
    VStack {
        Spacer()
        
        ImageSelectionSheet(
            isPresented: .constant(true),
            onCameraSelected: {
                print("Camera selected")
            },
            onPhotoLibrarySelected: {
                print("Photo library selected")
            },
            onRemovePhoto: {
                print("Remove photo selected")
            }
        )
    }
    .background(Color.black.opacity(0.3))
}
