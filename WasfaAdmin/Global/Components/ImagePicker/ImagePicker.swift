//
//  ImagePicker.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI
import PhotosUI
import UIKit

/// SwiftUI wrapper for image selection with camera and photo library support
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    let sourceType: UIImagePickerController.SourceType
    let onImageSelected: ((UIImage?) -> Void)?
    let onError: ((String) -> Void)?
    
    init(
        selectedImage: Binding<UIImage?>,
        sourceType: UIImagePickerController.SourceType,
        onImageSelected: ((UIImage?) -> Void)? = nil,
        onError: ((String) -> Void)? = nil
    ) {
        self._selectedImage = selectedImage
        self.sourceType = sourceType
        self.onImageSelected = onImageSelected
        self.onError = onError
    }
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = sourceType
        picker.allowsEditing = true // Enable basic cropping
        
        // Configure for profile images
        if sourceType == .camera {
            picker.cameraDevice = .front // Default to front camera for selfies
        }
        
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {
        // No updates needed
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            Logger.info("📸 Image selected from picker", tag: "ImagePicker")
            
            // Prefer edited image (cropped) over original
            let selectedImage = (info[.editedImage] as? UIImage) ?? (info[.originalImage] as? UIImage)
            
            if let image = selectedImage {
                // Resize image for profile picture (max 1024x1024)
                let resizedImage = resizeImage(image: image, targetSize: CGSize(width: 1024, height: 1024))
                
                DispatchQueue.main.async {
                    self.parent.selectedImage = resizedImage
                    self.parent.onImageSelected?(resizedImage)
                    Logger.info("✅ Image processed and set", tag: "ImagePicker")
                }
            } else {
                Logger.warning("⚠️ No image found in picker result", tag: "ImagePicker")
                DispatchQueue.main.async {
                    self.parent.onError?("No image selected")
                }
            }
            
            parent.presentationMode.wrappedValue.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            Logger.info("🚫 Image picker cancelled", tag: "ImagePicker")
            parent.presentationMode.wrappedValue.dismiss()
        }
        
        /// Resize image while maintaining aspect ratio
        private func resizeImage(image: UIImage, targetSize: CGSize) -> UIImage {
            let size = image.size
            
            let widthRatio  = targetSize.width  / size.width
            let heightRatio = targetSize.height / size.height
            
            // Use the smaller ratio to maintain aspect ratio
            let ratio = min(widthRatio, heightRatio)
            
            let newSize = CGSize(width: size.width * ratio, height: size.height * ratio)
            
            let rect = CGRect(origin: .zero, size: newSize)
            
            UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
            image.draw(in: rect)
            let newImage = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            
            return newImage ?? image
        }
    }
}

/// Modern PHPickerViewController wrapper for iOS 14+
@available(iOS 14.0, *)
struct PhotoPicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    let onImageSelected: ((UIImage?) -> Void)?
    let onError: ((String) -> Void)?
    
    init(
        selectedImage: Binding<UIImage?>,
        onImageSelected: ((UIImage?) -> Void)? = nil,
        onError: ((String) -> Void)? = nil
    ) {
        self._selectedImage = selectedImage
        self.onImageSelected = onImageSelected
        self.onError = onError
    }
    
    func makeUIViewController(context: Context) -> PHPickerViewController {
        var configuration = PHPickerConfiguration()
        configuration.filter = .images
        configuration.selectionLimit = 1
        
        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {
        // No updates needed
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: PhotoPicker
        
        init(_ parent: PhotoPicker) {
            self.parent = parent
        }
        
        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            parent.presentationMode.wrappedValue.dismiss()
            
            guard let provider = results.first?.itemProvider else {
                Logger.info("🚫 Photo picker cancelled or no selection", tag: "PhotoPicker")
                return
            }
            
            if provider.canLoadObject(ofClass: UIImage.self) {
                provider.loadObject(ofClass: UIImage.self) { image, error in
                    if let error = error {
                        Logger.error("❌ Error loading image: \(error)", tag: "PhotoPicker")
                        DispatchQueue.main.async {
                            self.parent.onError?("Failed to load image: \(error.localizedDescription)")
                        }
                        return
                    }
                    
                    if let uiImage = image as? UIImage {
                        Logger.info("📸 Image loaded from photo picker", tag: "PhotoPicker")
                        
                        // Resize image for profile picture
                        let resizedImage = self.resizeImage(image: uiImage, targetSize: CGSize(width: 1024, height: 1024))
                        
                        DispatchQueue.main.async {
                            self.parent.selectedImage = resizedImage
                            self.parent.onImageSelected?(resizedImage)
                            Logger.info("✅ Image processed and set", tag: "PhotoPicker")
                        }
                    }
                }
            }
        }
        
        /// Resize image while maintaining aspect ratio
        private func resizeImage(image: UIImage, targetSize: CGSize) -> UIImage {
            let size = image.size
            
            let widthRatio  = targetSize.width  / size.width
            let heightRatio = targetSize.height / size.height
            
            let ratio = min(widthRatio, heightRatio)
            let newSize = CGSize(width: size.width * ratio, height: size.height * ratio)
            let rect = CGRect(origin: .zero, size: newSize)
            
            UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
            image.draw(in: rect)
            let newImage = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            
            return newImage ?? image
        }
    }
}
