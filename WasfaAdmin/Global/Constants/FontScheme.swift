import Foundation
import SwiftUI

class FontScheme: NSObject {
    static func kInterRegular(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kInterRegular, size: size)
    }

    static func kInterMedium(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kInterMedium, size: size)
    }

    static func kNunitoLight(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoLight, size: size)
    }

    static func kNunitoMedium(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoMedium, size: size)
    }

    static func kNunitoExtraBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoExtraBold, size: size)
    }

    static func kNunitoBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoBold, size: size)
    }

    static func kNunitoSemiBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoSemiBold, size: size)
    }

    static func kNunitoRegular(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kNunitoRegular, size: size)
    }

    static func kPlusJakartaSansRomanBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kPlusJakartaSansRomanBold, size: size)
    }

    static func kRobotoRomanRegular(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kRobotoRomanRegular, size: size)
    }
    
    static func kPoppins(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kPoppinsRegular, size: size)
    }

    static func kRobotoRomanMedium(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kRobotoRomanMedium, size: size)
    }

    static func kRobotoRomanLight(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kRobotoRomanLight, size: size)
    }

    static func kProximaNovaMedium(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kProximaNovaMedium, size: size)
    }

    static func kRobotoRomanBold(size: CGFloat) -> Font {
        return Font.custom(FontConstant.kRobotoRomanBold, size: size)
    }

    static func fontFromConstant(fontName: String, size: CGFloat) -> Font {
        var result = Font.system(size: size)

        switch fontName {
        case "kInterRegular":
            result = self.kInterRegular(size: size)
        case "kInterMedium":
            result = self.kInterMedium(size: size)
        case "kNunitoLight":
            result = self.kNunitoLight(size: size)
        case "kNunitoMedium":
            result = self.kNunitoMedium(size: size)
        case "kNunitoExtraBold":
            result = self.kNunitoExtraBold(size: size)
        case "kNunitoBold":
            result = self.kNunitoBold(size: size)
        case "kNunitoSemiBold":
            result = self.kNunitoSemiBold(size: size)
        case "kNunitoRegular":
            result = self.kNunitoRegular(size: size)
        case "kPlusJakartaSansRomanBold":
            result = self.kPlusJakartaSansRomanBold(size: size)
        case "kRobotoRomanRegular":
            result = self.kRobotoRomanRegular(size: size)
        case "kRobotoRomanMedium":
            result = self.kRobotoRomanMedium(size: size)
        case "kRobotoRomanLight":
            result = self.kRobotoRomanLight(size: size)
        case "kProximaNovaMedium":
            result = self.kProximaNovaMedium(size: size)
        case "kRobotoRomanBold":
            result = self.kRobotoRomanBold(size: size)
        default:
            result = self.kInterRegular(size: size)
        }
        return result
    }

    enum FontConstant {
        // Inter Font Family
        static let kInterRegular: String = "Inter"
        static let kInterMedium: String = "Inter"

        // Nunito Font Family
        static let kNunitoLight: String = "Nunito"
        static let kNunitoRegular: String = "Nunito"
        static let kNunitoMedium: String = "Nunito"
        static let kNunitoSemiBold: String = "Nunito"
        static let kNunitoBold: String = "Nunito"
        static let kNunitoExtraBold: String = "Nunito"

        // Plus Jakarta Sans Font Family
        static let kPlusJakartaSansRomanBold: String = "PlusJakartaSans"

        // Roboto Font Family
        static let kRobotoRomanLight: String = "Roboto"
        static let kRobotoRomanRegular: String = "Roboto"
        static let kRobotoRomanMedium: String = "Roboto"
        static let kRobotoRomanBold: String = "Roboto"

        // Poppins Font Family
        static let kPoppinsRegular: String = "Poppins"

        // Proxima Nova (Commercial font - needs to be purchased separately)
        static let kProximaNovaMedium: String = "ProximaNova"
    }
}
