//
//  AppDelegate.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import UIKit

class AppDelegate: NSObject, UIApplicationDelegate {
    // MARK: - App Lifecycle

    func application(_ application: UIApplication,
                     didFinishLaunchingWithOptions launchOptions: [UIApplication
                         .LaunchOptionsKey: Any]? = nil) -> <PERSON><PERSON>
    {
        // Configure app services

        // Migrate existing tokens to new TokenManager system
        TokenManager.shared.migrateExistingToken()

        return true
    }

    // MARK: - TokenManager Lifecycle Integration

    func applicationDidEnterBackground(_ application: UIApplication) {
        // Handle Remember Me token management when app enters background
        TokenManager.shared.handleAppDidEnterBackground()
    }

    func applicationWillTerminate(_ application: UIApplication) {
        // Handle Remember Me token management when app will terminate
        TokenManager.shared.handleAppWillTerminate()
    }

    func applicationDidBecomeActive(_ application: UIApplication) {
        // Handle Remember Me token management when app becomes active
        TokenManager.shared.handleAppDidBecomeActive()
    }

    func applicationWillResignActive(_ application: UIApplication) {
        // Additional safety: clear temporary tokens when app becomes inactive
        // This covers cases where applicationWillTerminate might not be called
        TokenManager.shared.handleAppDidEnterBackground()
    }
}
