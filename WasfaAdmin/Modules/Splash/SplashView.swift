//
//  ContentView.swift
//  WasfaApixrx
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

struct SplashView: View {
    var body: some View {
        ZStack {
            // Clean white background
            Color.white
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                Spacer()
                
                // Wasfa logo centered on screen
                logoView
                
                Spacer()
            }
        }
    }
    
    var logoView: some View {
        // Use the simplified SwiftUI Image extension with custom fallback
        Image(.wasfaLogo)
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(maxWidth: 200, maxHeight: 200)
    }
}

#Preview {
    SplashView()
}
