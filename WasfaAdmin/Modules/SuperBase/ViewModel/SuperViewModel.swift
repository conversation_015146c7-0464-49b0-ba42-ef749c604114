//
//  SuperViewModel.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//

import Alamofire
import Foundation
import SwiftUI
import Combine

@MainActor
class SuperViewModel: ObservableObject {
    // MARK: - Memory Management Optimizations

    /// Cancellables for proper Combine cleanup (nonisolated for deinit access)
    private nonisolated(unsafe) var cancellables: Set<AnyCancellable> = []

    /// Track all async tasks for proper cancellation (nonisolated for deinit access)
    private nonisolated(unsafe) var activeTasks: Set<Task<Void, Never>> = []

    /// API service reference (using singleton pattern with memory monitoring)
    private nonisolated(unsafe) var _api: RepositoriesAPIProtocol?

    /// API service with lazy initialization and memory-safe access
    var api: RepositoriesAPIProtocol {
        if let existingAPI = _api {
            return existingAPI
        }

        let apiService = RepositoriesAPI.shared
        _api = apiService
        return apiService
    }

    @Published var pageState: PageState = .stable

    /// Weak references to prevent retain cycles with environment objects (nonisolated for deinit access)
    private nonisolated(unsafe) weak var _appState: AppState?
    private nonisolated(unsafe) weak var _routerManager: RouterManager?

    var appState: AppState? {
        get { _appState }
        set { _appState = newValue }
    }

    var routerManager: RouterManager? {
        get { _routerManager }
        set { _routerManager = newValue }
    }

    init() {
        Logger.debug("SuperViewModel class is initialized", tag: "SuperViewModel")

        if let token = UserDefaultsSecure.sharedInstance.getGeneratedToken() {
            Logger.debug("token: \(token)", tag: "SuperViewModel")
        }

        // Setup memory monitoring
        setupMemoryManagement()
    }

    deinit {
        Logger.debug("SuperViewModel class is deinitialized", tag: "SuperViewModel")

        // Perform synchronous cleanup directly - NO Task creation in deinit
        performSynchronousCleanup()
    }

    // MARK: - Memory Management Methods

    /// Setup memory management and monitoring
    private func setupMemoryManagement() {
        // Monitor memory warnings
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                self?.handleMemoryWarning()
            }
            .store(in: &cancellables)
    }

    /// Handle memory warnings by cleaning up resources
    private func handleMemoryWarning() {
        Logger.warning("⚠️ Memory warning received in SuperViewModel", tag: "SuperViewModel")

        // Clear weak references if they're no longer needed
        if _appState == nil {
            _appState = nil
        }
        if _routerManager == nil {
            _routerManager = nil
        }
        if _api == nil {
            _api = nil
        }
    }

    /// Comprehensive cleanup method (async version)
    private func performCleanup() {
        performSynchronousCleanup()
    }

    /// Synchronous cleanup for deinit
    private nonisolated func performSynchronousCleanup() {
        // Cancel all Combine subscriptions
        cancellables.forEach { $0.cancel() }
        cancellables.removeAll()

        // Cancel any ongoing async tasks
        cancelAllTasks()

        // Clear references
        _appState = nil
        _routerManager = nil
        _api = nil

        // Reset page state to prevent any lingering references (skip for nonisolated context)
        // pageState = .stable // Cannot modify @Published property from nonisolated context

        Logger.debug("🧹 SuperViewModel cleanup completed", tag: "SuperViewModel")
    }

    /// Cancel all ongoing tasks when view model is deallocated
    private nonisolated func cancelAllTasks() {
        // Cancel all tracked async tasks
        let taskCount = activeTasks.count
        activeTasks.forEach { $0.cancel() }
        activeTasks.removeAll()
        Logger.debug("🚫 Cancelled \(taskCount) ongoing tasks in SuperViewModel", tag: "SuperViewModel")
    }
    
    func initEnvironment(appState: AppState? = nil, routerManager: RouterManager? = nil) {
        self.appState = appState
        self.routerManager = routerManager
    }
    
    func isUnAuthenticateErrorOccurred(_ error: String) -> Bool {
        error.contains(AppConstants.unAuthenticateErrorString) ||
            error.contains(AppConstants.unAuthenticateErrorStringSecond)
    }
    
    func updatePageState(_ state: PageState) { pageState = state }

    func handleApiCallFailure(_ error: String) { updatePageState(.failure(error: error)) }

    // MARK: - Async/Await API Methods

    /// Memory-safe API call with proper lifecycle management
    func onApiCall<T>(
        _ execution: () async throws -> CommonApiResponse<T>,
        dismissKeyboard: Bool = true,
        withStateChange: Bool = true,
        withLoadingIndicator: Bool = true,
        customLoadingBinding: Binding<Bool>? = nil,
        onSuccess: @escaping (CommonApiResponse<T>) -> Void,
        onFailure: TypeCallback<String>? = nil
    ) async {
        // Check if view model is still alive
        guard !Task.isCancelled else {
            Logger.warning("⚠️ API call cancelled - SuperViewModel deallocated", tag: "SuperViewModel")
            return
        }

        if withStateChange {
            updatePageState(.loading(withStateChange && withLoadingIndicator))
        }

        if let customLoadingBinding = customLoadingBinding {
            customLoadingBinding.wrappedValue = true
        }

        if dismissKeyboard {
            Utilities.dismissKeyboard()
        }

        do {
            let result = try await execution()

            // Ensure we're still alive before processing response
            guard !Task.isCancelled else {
                Logger.warning("⚠️ API response ignored - SuperViewModel deallocated", tag: "SuperViewModel")
                return
            }

            if result.isError {
                await MainActor.run {
                    onFailure?(result.message)
                    if withStateChange {
                        onApiCallFailure(error: result.message, customLoadingBinding: customLoadingBinding)
                    }
                }
                return
            }

            await MainActor.run {
                if withStateChange {
                    onApiCallSuccess(response: result, customLoadingBinding: customLoadingBinding)
                }
                onSuccess(result)
            }

        } catch {
            // Ensure we're still alive before handling error
            guard !Task.isCancelled else {
                Logger.warning("⚠️ API error ignored - SuperViewModel deallocated", tag: "SuperViewModel")
                return
            }

            let errorMessage = error.localizedDescription
            await MainActor.run {
                onFailure?(errorMessage)
                if withStateChange {
                    onApiCallFailure(error: errorMessage, customLoadingBinding: customLoadingBinding)
                }
            }
        }
    }

    // MARK: - Legacy Closure-Based API Methods (Preserved for Compatibility)
    
    func onApiCallFailure(error: String, customLoadingBinding: Binding<Bool>?) {
        handleApiCallFailure(error)
        if let customLoadingBinding = customLoadingBinding { customLoadingBinding.wrappedValue = false }
        
        if error == AppConstants.unAuthenticateErrorString {
            NotificationCenter.default.post(name: .userDidBecomeUnauthenticated, object: nil)
        }
        
    }
    
    func onApiCallSuccess<T>(response: ApiBaseModel<T>, customLoadingBinding: Binding<Bool>?) {
//        if response.data is [Any] {
//            let dataArray = response.data as! [Any]
//
//            if dataArray.isEmpty {
//                updatePageState(.noData)
//            }else{
//                updatePageState(.stable)
//            }
//
//        } else {
//            updatePageState(.stable)
//        }
        
        updatePageState(.stable)
        if let customLoadingBinding = customLoadingBinding { customLoadingBinding.wrappedValue = false }
    }
    
    func onApiCall<T, R>(_ execution: (_ request: R, _ completionHandler: @escaping (Result<CommonApiResponse<T>, AFError>) -> Void) -> Void, parameters: R, dismissKeyboard: Bool = true, withStateChange: Bool = true, withLoadingIndicator: Bool = true, customLoadingBinding: Binding<Bool>? = nil, onSuccess: @escaping (CommonApiResponse<T>) -> Void, onFailure: TypeCallback<String>? = nil) {
        updatePageState(.loading(withStateChange && withLoadingIndicator))
        
        if let customLoadingBinding = customLoadingBinding { customLoadingBinding.wrappedValue = true }
        
        if dismissKeyboard { Utilities.dismissKeyboard() }
        execution(parameters) { result in
            switch result {
            case let .success(result):
                if result.isError {
                    onFailure?(result.message)
                    if withStateChange {
                        self.onApiCallFailure(error: result.message, customLoadingBinding: customLoadingBinding)
                    }
                    return
                }
                if withStateChange {
                    self.onApiCallSuccess(response: result, customLoadingBinding: customLoadingBinding)
                }
                onSuccess(result)
            case let .failure(error):
                onFailure?(error.localizedDescription)
                if withStateChange {
                    self.onApiCallFailure(error: error.localizedDescription, customLoadingBinding: customLoadingBinding)
                }
            }
        }
    }
}

extension SuperViewModel {
    func handleError(_ error: Error) {
        DispatchQueue.main.async {
            self.pageState = .failure(error: error.localizedDescription)
        }
    }
}
