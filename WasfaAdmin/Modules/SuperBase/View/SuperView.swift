//
//  SuperView.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//

import SwiftUI

struct SuperView<Content: View, T: SuperViewModel>: View {
    let content: Content
    @ObservedObject private var viewModel: T

    init(viewModel: T, @ViewBuilder content: () -> Content) {
        self.content = content()
        self.viewModel = viewModel
    }

    var body: some View {
        GeometryReader { geometry in
            content
                .injectEnvironmentValues(viewModel)
                .overlay(overlayContent(geometry: geometry))
                .hideNavigationBar(shouldHideNavigationBar)
                .animation(.bouncy, value: viewModel.pageState)
        }
    }

    // MARK: - Optimized Overlay System

    /// Consolidated overlay content with single conditional view
    @ViewBuilder
    private func overlayContent(geometry: GeometryProxy) -> some View {
        switch viewModel.pageState {
        case .loading(let isLoading):
            if isLoading {
                LoadingOverlay(geometry: geometry)
            }
        case .message(let config):
            AlertOverlay(
                config: config,
                pageState: $viewModel.pageState,
                geometry: geometry
            )
        case .failure(let error):
            ErrorOverlay(
                error: error,
                pageState: $viewModel.pageState,
                geometry: geometry
            )
        case .success(let message, let onDone):
            SuccessOverlay(
                message: message,
                onDone: onDone,
                pageState: $viewModel.pageState,
                geometry: geometry
            )
        case .custom(let view):
            CustomOverlay(
                view: view,
                geometry: geometry
            )
        case .stable:
            EmptyView()
        }
    }

    /// Computed property for navigation bar visibility
    private var shouldHideNavigationBar: Bool {
        if case .custom = viewModel.pageState {
            return true
        }
        return false
    }
}

// MARK: - Optimized Overlay Components

/// Loading overlay with responsive design
private struct LoadingOverlay: View {
    let geometry: GeometryProxy

    var body: some View {
        Rectangle()
            .fill(.ultraThinMaterial)
//            .frame(width: geometry.size.width, height: geometry.size.height)
            .frame(height: UIScreen.main.bounds.height)
            .overlay(
                ActivityLoaderView()
                    .transition(.scale)
            )
            .ignoresSafeArea(.all)
    }
}

/// Alert overlay with responsive design
private struct AlertOverlay: View {
    let config: AlertConfig
    @Binding var pageState: PageState
    let geometry: GeometryProxy

    var body: some View {
        Rectangle()
            .fill(.ultraThinMaterial)
//            .frame(width: geometry.size.width, height: geometry.size.height)
            .frame(height: UIScreen.main.bounds.height)
            .overlay(
                AlertView(pageState: $pageState, config: config)
                    .padding(.horizontal)
                    .transition(.slide)
            )
            .ignoresSafeArea(.all)
    }
}

/// Error overlay with responsive design
private struct ErrorOverlay: View {
    let error: String
    @Binding var pageState: PageState
    let geometry: GeometryProxy

    var body: some View {
        Rectangle()
            .fill(.ultraThinMaterial)
//            .frame(width: geometry.size.width, height: geometry.size.height)
            .frame(height: UIScreen.main.bounds.height)
            .overlay(
                AlertView(
                    pageState: $pageState,
                    config: AlertConfig(title: "Error!", text: error)
                )
                .padding(.horizontal)
                .transition(.slide)
            )
            .ignoresSafeArea(.all)
    }
}

/// Success overlay with responsive design
private struct SuccessOverlay: View {
    let message: String
    let onDone: (() -> Void)?
    @Binding var pageState: PageState
    let geometry: GeometryProxy

    var body: some View {
        Rectangle()
            .fill(.ultraThinMaterial)
//            .frame(width: geometry.size.width, height: geometry.size.height)
            .frame(height: UIScreen.main.bounds.height)
            .overlay(
                AlertView(
                    pageState: $pageState,
                    config: AlertConfig(title: "Success!", text: message)
                )
                .padding(.horizontal)
                .transition(.slide)
            )
            .ignoresSafeArea(.all)
            .onDisappear {
                onDone?()
            }
    }
}

/// Custom overlay with responsive design
private struct CustomOverlay: View {
    let view: any View
    let geometry: GeometryProxy

    var body: some View {
        AnyView(view)
//            .frame(width: geometry.size.width, height: geometry.size.height)
            .frame(height: UIScreen.main.bounds.height)
            .transition(.move(edge: .leading))
    }
}
