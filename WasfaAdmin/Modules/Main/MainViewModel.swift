//
//  MainViewModel.swift
//  Wasfa
//
//  Created by Apple on 27/10/2024.
//

import Foundation

class MainViewModel: SuperViewModel {
    @Published var isDisplaying = false
    override init() {
        super.init()
    }

    func onAppear() {
        isDisplaying.toggle()

        // Use a more reliable approach for updating the initial screen
        Utilities.enQueue(after: .now() + 3) {
            // Ensure we have access to appState before updating
            guard let appState = self.appState else {
                Logger.warning("⚠️ AppState not available in MainViewModel.onAppear", tag: "MainViewModel")
                return
            }
            
            let hasValidToken: Bool = TokenManager.shared.hasValidToken()
           
            if hasValidToken {
                appState.updateInitialScreen(.dashboard)
            } else {
                appState.updateInitialScreen(.authentication)
            }
        }
    }
    
    func createGenerateToken() async {
        guard UserDefaultsSecure.sharedInstance.getGeneratedToken() == nil else { return }

        let parameters: [String: Any] = [
            "deviceID": AppState.fcmToken.isEmpty ? AppState.deviceID : AppState.fcmToken
        ]

        // Note: This would require a generateToken endpoint to be added to RepositoriesAPI
        // For now, this is a placeholder showing the async/await pattern
        /*
         await onApiCall(
             { try await self.api.generateToken(parameters: parameters) },
             onSuccess: { response in
                 if response.success, let token = response.data?.token {
                     UserDefaultsSecure.sharedInstance.setGeneratedTokenStringValue(value: token)
                 }
             }
         )
         */
    }
    func userDidBecomeUnauthenticated(_ notification: NotificationCenter.Publisher.Output) {
        Task(operation: performLogout)
    }
    /// Performs the actual logout operation
    private func performLogout() async {
        updatePageState(.loading(true))
        await MainActor.run {
            
            Logger.info("🚪 Starting logout process", tag: "SettingsViewModel")
        }

        // Clear all authentication tokens and preferences
        TokenManager.shared.clearAllTokens()
        Logger.info("🧹 All tokens and preferences cleared", tag: "SettingsViewModel")

        // Clear any additional user data
        AppState.isLoggedIn = false
        AppState.user = nil
        Logger.info("🗑️ User data cleared from AppState", tag: "SettingsViewModel")

        // Add a small delay to show the logout loading state
       
        updatePageState(.stable)
        await MainActor.run {
           

            // Navigate back to authentication screen
            guard let appState = self.appState else {
                Logger.error("❌ AppState not available during logout", tag: "SettingsViewModel")
                return
            }

            appState.updateInitialScreen(.authentication)
            self.appState?.showToast(.init(type: .error, message: "User Token Expired. Please login again."))
            Logger.info("✅ Logout completed - navigated to authentication screen", tag: "SettingsViewModel")
        }
    }
    
    func refreshUserToken() {}
}
