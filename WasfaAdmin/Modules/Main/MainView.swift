//
//  MainView.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

struct MainView: View {
    @StateObject private var viewModel: MainViewModel = .init()
    @EnvironmentObject private var appState: AppState

    var body: some View {
        Group {
            // Fix: Use appState directly instead of viewModel.appState
            switch appState.initialScreen {
            case .splash:
                SplashView()
                    .opacity(viewModel.isDisplaying ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 0.5), value: viewModel.isDisplaying)
                    .onAppear(perform: viewModel.onAppear)
            case .dashboard:
                DashboardView()
                    .transition(.backslide)
            case .authentication:
                SignInView()
                    .transition(.backslide)
            }
        }
        
        .tint(.doctorMain)
        .toastView(toast: $appState.toast)
        .animation(.easeInOut, value: appState.initialScreen) // Fix: Use appState directly
        .injectEnvironmentValues(viewModel)
        .onAppear {
            // Ensure environment injection happens immediately
            viewModel.initEnvironment(appState: appState, routerManager: nil)
        }
        .onReceive(NotificationCenter.default.publisher(for: .userDidBecomeUnauthenticated), perform: viewModel.userDidBecomeUnauthenticated)
        
    }
}


#Preview {
    NavigationStack {
        MainView().attachAllEnvironmentObjects()
    }
}


extension AnyTransition {
    static var backslide: AnyTransition {
        AnyTransition.asymmetric(
            insertion: .move(edge: .trailing),
            removal: .move(edge: .leading))
    }
}
