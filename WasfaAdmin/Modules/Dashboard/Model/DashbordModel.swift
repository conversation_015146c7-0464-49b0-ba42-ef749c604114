//
//  DashbordModel.swift
//  Wasfa
//
//  Created by Apple on 20/09/2024.
//

import SwiftUI

enum Tab: Identifiable, CaseIterable, Hashable {
    case home, medications, center, prescriptions, settings

    var id: Self { self }

    var image: ImageResource {
        switch self {
        case .home: .tabHome
        case .medications: .tabMedications
        case .center: .tabCenter
        case .prescriptions: .tabPrescriptions
        case .settings: .tabSettings
            
        }
    }

    var title: String {
        let title: String = {
            switch self {
            case .home: return "Home"
            case .medications: return "Medications"
            case .center: return "New Prescriptions"
            case .prescriptions: return "Report"
            case .settings: return "Settings"
            }
        }()

        return title
    }

    var index: Int {
        return Tab.allCases.firstIndex(of: self) ?? 0
    }
}
