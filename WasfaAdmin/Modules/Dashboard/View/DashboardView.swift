//
//  TabView.swift
//  Wasfa
//
//  Created by Apple on 16/09/2024.
//

import SwiftUI

struct DashboardView: View {
    @Environment(\.routerManager) var routerManager: RouterManager
    @EnvironmentObject var appState: AppState

    var body: some View {
        // Use the optimized LazyTabView for better performance
        LazyTabView()
           
    }

    // MARK: - Legacy Methods (moved to LazyTabView)

    // Badge configuration and other methods are now handled by LazyTabView
}

#Preview {
    DashboardView().attachAllEnvironmentObjects()
}

struct DemoView: View {
    let content: String
    var body: some View {
        NavigationStack {
            GeometryReader(content: { geometry in
                let size = geometry.size
                ScrollView {
                    ForEach(1 ... 50, id: \.self) { index in

                        Text("\(content) \(index)")
                    }
                }
                .scrollIndicators(.hidden)
                .navigationTitle(content)
                .navigationBarTitleDisplayMode(.large)
                .frame(width: size.width, height: size.height)
                //            .background(.orange)

            })
        }
    }
}
