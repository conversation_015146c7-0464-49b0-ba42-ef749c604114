//
//  LazyTabView.swift
//  WasfaAdmin
//
//  Created by Performance Optimization Project on 26/06/2025.
//

import SwiftUI

/// Lazy loading TabView that only loads tabs when they are accessed
/// This provides significant performance improvements for app startup time
struct LazyTabView: View {
    @Environment(\.routerManager) var routerManager: RouterManager
    @EnvironmentObject var appState: AppState
    
    @Namespace private var animation
    
    // MARK: - Lazy Loading State Management
    
    /// Track which tabs have been loaded to avoid reloading
    @State private var loadedTabs: Set<Tab> = []
    
    /// Track the previously selected tab for smooth transitions
    @State private var previousTab: Tab?
    
    /// Performance monitoring
    @State private var tabLoadTimes: [Tab: TimeInterval] = [:]
    
    init() {
        UITabBar.appearance().layer.zPosition = -1
        UITabBar.appearance().isHidden = true
    }
    
    var body: some View {
        ZStack(alignment: .bottom) {
            VStack(spacing: 0) {
                TabView(selection: selectionBinding) {
                    ForEach(Tab.allCases, id: \.self) { tab in
                        Group {
                            if shouldLoadTab(tab) {
                                loadedTabContent(for: tab)
                            } else {
                                placeholderView(for: tab)
                            }
                        }
                        .tabItem {
                            Image(tab.image)
                                .resizable()
                                .renderingMode(.template)
                                .frame(width: 24, height: 24)
                            if tab != .center {
                                Text(tab.title)
                            }
                        }
                        .tag(tab)
                        .onAppear {
                            handleTabAppear(tab)
                        }
                    }
                }
                CustomTabBar()
                
            }
        }
        .ignoresSafeArea(.keyboard)
        .onLoad {
            configureTabBarBadgeOffset()
            preloadCriticalTabs()
        }
        .onChange(of: appState.selectedTab) { oldValue, newValue in
            handleTabChange(from: oldValue, to: newValue)
        }
    }
    
    /// Custom Tab Bar
    @ViewBuilder
    func CustomTabBar() -> some View {
        TabShape(midPoint: UIScreen.main.bounds.width * 0.5)
            .fill(.ultraThinMaterial)
//            .fill(.white)
            .edgesIgnoringSafeArea(.bottom)
            .overlay {
                HStack(alignment: .top, spacing: 0) {
                    ForEach(Tab.allCases) { tab in
                        TabItem(tab: tab, activeTab: selectionBinding)
                            .padding(.top)
                            .if(tab == .center) {
                                $0

                                    .background {
                                        Rectangle()
                                           
                                            .foregroundColor(.clear)
                                            .frame(width: 58, height: 58)
                                            .background(.doctorMain)
                                            .clipShape(.circle)
                                            .shadow(color: Color(red: 0.65, green: 0.11, blue: 0.36, opacity: 0.60), radius: 4, y: 4
                                            )
                                    }
                                    .offset(x: 0, y: -10)
                            }
                            .overlay(alignment: .top) {
                                if selectionBinding.wrappedValue == tab {
                                    Image(.tabIndicator)
                                        .shadow(color: Color(red: 0.65, green: 0.11, blue: 0.36, opacity: 0.60), radius: 4, y: 2
                                        )
                                        .opacity(tab == .center ? 0.001 : 1.0)
                                        .padding(.top, 6)
                                        .matchedGeometryEffect(id: "tab.indicator", in: animation)
                                }
                            }
                            .animation(.bouncy, value: selectionBinding.wrappedValue)
                    }
                }.padding(.horizontal, 8.0.relativeWidth)
            }
            .frame(width: UIScreen.main.bounds.width, height: 45.0.relativeHeight,
                   alignment: .leading)
            .padding(.top, -11)
//            .shadow(color: Color.black.opacity(0.10), radius: 2, x: 0, y: 0)
     
    }

    // MARK: - Lazy Loading Logic
    
    /// Determines if a tab should be loaded based on lazy loading strategy
    private func shouldLoadTab(_ tab: Tab) -> Bool {
        // Always load the currently selected tab
        if tab == appState.selectedTab {
            return true
        }
        
        // Load tabs that have been previously accessed
        if loadedTabs.contains(tab) {
            return true
        }
        
        // Load critical tabs immediately (home tab for better UX)
        if tab == .home {
            return true
        }
        
        return false
    }
    
    /// Creates the actual tab content for loaded tabs
    @ViewBuilder
    private func loadedTabContent(for tab: Tab) -> some View {
        NavigationStack(path: getRouterManagerPath(tab)) {
            tab.view
                .navigationDestination(for: Route.self) { $0 }
                .id(appState.rootViewId)
        }
    }
    
    /// Creates placeholder view for unloaded tabs
    @ViewBuilder
    private func placeholderView(for tab: Tab) -> some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Loading \(tab.title)...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
        .onAppear {
            // Trigger loading when placeholder appears
            loadTab(tab)
        }
    }
    
    // MARK: - Tab Loading Management
    
    /// Handles tab appearance and triggers loading
    private func handleTabAppear(_ tab: Tab) {
        guard !loadedTabs.contains(tab) else { return }
        
        // Mark tab as loaded and track performance
        let startTime = CFAbsoluteTimeGetCurrent()
        loadedTabs.insert(tab)
        
        let loadTime = CFAbsoluteTimeGetCurrent() - startTime
        tabLoadTimes[tab] = loadTime

        Logger.debug("📱 Lazy loaded tab: \(tab.title) in \(String(format: "%.3f", loadTime))s", tag: "LazyTabView")
    }
    
    /// Loads a specific tab and marks it as loaded
    private func loadTab(_ tab: Tab) {
        guard !loadedTabs.contains(tab) else { return }
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // Add to loaded tabs set
        loadedTabs.insert(tab)
        
        let loadTime = CFAbsoluteTimeGetCurrent() - startTime
        tabLoadTimes[tab] = loadTime

        Logger.info("🚀 Tab loaded: \(tab.title) in \(String(format: "%.3f", loadTime))s", tag: "LazyTabView")
    }
    
    /// Handles tab change events for performance tracking
    private func handleTabChange(from oldTab: Tab?, to newTab: Tab) {
        previousTab = oldTab
        
        // Ensure the new tab is loaded
        if !loadedTabs.contains(newTab) {
            loadTab(newTab)
        }
        
        // Log tab switch performance
        if let oldTab = oldTab {
            Logger.info("🔄 Tab switch: \(oldTab.title) → \(newTab.title)", tag: "LazyTabView")
        }
    }
    
    /// Preloads critical tabs for better user experience
    private func preloadCriticalTabs() {
        // Preload home tab immediately for better UX
        if !loadedTabs.contains(.home) {
            loadTab(.home)
        }
        
        // Optionally preload the most commonly used tab after a delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if !loadedTabs.contains(.medications) {
                loadTab(.medications)
            }
        }
    }
    
    // MARK: - Performance Monitoring
    
    /// Gets performance statistics for loaded tabs
    func getPerformanceStats() -> [Tab: TimeInterval] {
        return tabLoadTimes
    }
    
    /// Logs performance summary
    func logPerformanceSummary() {
        Logger.info("\n📊 LazyTabView Performance Summary:", tag: "LazyTabView")
        Logger.info("=" + String(repeating: "=", count: 40), tag: "LazyTabView")

        for tab in Tab.allCases {
            let status = loadedTabs.contains(tab) ? "✅ Loaded" : "⏳ Pending"
            let loadTime = tabLoadTimes[tab].map { String(format: "%.3f", $0) + "s" } ?? "N/A"
            Logger.info("  \(tab.title): \(status) (\(loadTime))", tag: "LazyTabView")
        }

        let totalLoadedTabs = loadedTabs.count
        let totalTabs = Tab.allCases.count
        let loadPercentage = (Double(totalLoadedTabs) / Double(totalTabs)) * 100

        Logger.info("\nLoaded: \(totalLoadedTabs)/\(totalTabs) tabs (\(String(format: "%.1f", loadPercentage))%)", tag: "LazyTabView")
        Logger.info("=" + String(repeating: "=", count: 40), tag: "LazyTabView")
    }
}

// MARK: - Extensions

extension LazyTabView {
    /// Selection binding for tab management
    var selectionBinding: Binding<Tab> {
        Binding(
            get: {
                self.appState.selectedTab
            },
            set: { newTab in
                if newTab != self.appState.selectedTab {
                    self.appState.updateSelectedTab(newTab)
                } else {
                    // Handle tab re-selection (pop to root)
                    routerManager.popToRoot(where: routerManager.mapTabTypeToRoutesType(from: self.appState.selectedTab))
                }
            }
        )
    }
    
    /// Gets router manager path for a specific tab
    func getRouterManagerPath(_ type: Tab) -> Binding<[Route]> {
        routerManager.getRouteStackBinding(for: type.routeType)
    }
    
    /// Configure tab bar badge offset (preserved from original implementation)
    func configureTabBarBadgeOffset() {
        let appearance = UITabBarAppearance()
        
        let tabBarItemAppearance = UITabBarItemAppearance()
        tabBarItemAppearance.normal.badgePositionAdjustment = UIOffset(horizontal: 2, vertical: 2)
        
        appearance.stackedLayoutAppearance = tabBarItemAppearance
        appearance.inlineLayoutAppearance = tabBarItemAppearance
        appearance.compactInlineLayoutAppearance = tabBarItemAppearance
        
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

#Preview {
    LazyTabView()
        .environmentObject(AppState())
        .environment(\.routerManager, RouterManager())
}
