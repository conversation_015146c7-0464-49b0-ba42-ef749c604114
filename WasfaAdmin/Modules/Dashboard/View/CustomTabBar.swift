//
//  CustomTabBar.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

/// Tab Bar Item
struct TabItem: View {
    var tab: Tab
    @Binding var activeTab: Tab
    @Namespace private var animation

    var currentlyActiveTab: Bool { tab == activeTab }

    var body: some View {
        let selectedTab = activeTab == tab

        VStack(spacing: 4) {
            Image(tab.image)
                .renderingMode(.template)
                .resizable()
                .scaledToFit()
                .padding(4)
                .frame(width: 28.0.relativeFontSize, height: 28.0.relativeFontSize)
                .if(tab == .center) { $0.foregroundStyle(.white) }

            if tab == .center {
                Text("")

            } else {
                Text(LocalizedStringKey(tab.title))
                    .font(Font.custom("Urbanist", size: 11.relativeFontSize).weight(.bold))
                    .foregroundStyle(selectedTab ? .doctorMain : .gray)
            }
        }
        .foregroundStyle(activeTab == tab ? .doctorMain : .gray)
        .frame(maxWidth: .infinity)
        .contentShape(.rect)
        .onTapGesture { self.updateActiveTab(tab)}
        
    }
    
    
    func updateActiveTab(_ tab: Tab) {
        self.activeTab = tab
    }
}

struct TabShape: Shape {
    var midPoint: CGFloat

    func path(in rect: CGRect) -> Path {
        return Path { path in
            path.addPath(Rectangle().path(in: rect))

            path.move(to: .init(x: midPoint - 60, y: 0))

            let to = CGPoint(x: midPoint, y: -25)
            let control1 = CGPoint(x: midPoint - 28, y: 0)
            let control2 = CGPoint(x: midPoint - 28, y: -25)

            path.addCurve(to: to, control1: control1, control2: control2)

            let to1 = CGPoint(x: midPoint + 60, y: 0)
            let control3 = CGPoint(x: midPoint + 28, y: -25)
            let control4 = CGPoint(x: midPoint + 28, y: 0)

            path.addCurve(to: to1, control1: control3, control2: control4)
        }
    }
}
