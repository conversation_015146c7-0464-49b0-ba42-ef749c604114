//
//  DashboardViewModel.swift
//  Wasfa
//
//  Created by Apple on 20/09/2024.
//

import SwiftUI

extension DashboardView {
    var selectionBinding: Binding<Tab> { Binding(
        get: {
            self.appState.selectedTab
        },
        set: {
            if $0 != self.appState.selectedTab {
                self.appState.updateSelectedTab($0)
            } else {
//                if $0 == .shop() {
//                    appState.resetShopTabID()
//                }
//                appState.restRootViewID()
                routerManager.popToRoot(where: routerManager.mapTabTypeToRoutesType(from: self.appState.selectedTab))
            }
        }
    ) }

    func getRouterManagerPath(_ type: Tab) -> Binding<[Route]> {
        // Use the optimized utility method for O(1) access
        routerManager.getRouteStackBinding(for: type.routeType)
    }
}

extension DashboardView {
    final class ViewModel {
        func getRouterManagerPath(_ type: Tab, routerManager: Binding<RouterManager>) -> Binding<[Route]> {
            // Use the optimized utility method for O(1) access
            routerManager.wrappedValue.getRouteStackBinding(for: type.routeType)
        }
    }
}
