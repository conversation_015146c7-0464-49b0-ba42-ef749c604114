//
//  SettingsViewModel.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

class SettingsViewModel: SuperViewModel {
    @Published var homeData: HomeModel?
    @Published var isLoading: Bool = false
    @Published var isLoggingOut: Bool = false
    @Published var showLogoutConfirmation: Bool = false
    @Published var userProfileModel: Profile?

    // Form field states
    @Published var name: String = ""
    @Published var email: String = ""
    @Published var phoneNumber: String = ""
    @Published var alternativeNumber: String = ""
    @Published var dateOfBirth: String = ""
    @Published var civilId: String = ""
    @Published var showDatePicker: Bool = false
    @Published var selectedDate: Date = .init()

    // Image handling states
    @Published var selectedProfileImage: UIImage?
    @Published var showImageSelectionSheet: Bool = false
    @Published var showImagePicker: Bool = false
    @Published var showPhotoPicker: Bool = false
    @Published var imagePickerSourceType: UIImagePickerController.SourceType = .photoLibrary
    @Published var showPermissionAlert: Bool = false
    @Published var permissionAlertMessage: String = ""

    override init() {
        super.init()

        loadProfile()
    }

    // MARK: - Helper Methods

    func saveProfile() {
        Task {
            await performSaveProfile()
        }
    }

    func onDateFieldTapped() {
        if let date = dateOfBirth.toDate(format: "yyyy-MM-dd") {
            selectedDate = date
        }

        showDatePicker = true
    }

    func onDatePickerDisappear() {
        dateOfBirth = selectedDate.toString(outputFormate: "yyyy-MM-dd")
    }

    private func performSaveProfile() async {
        Logger.info("💾 Starting profile save", tag: "SettingsViewModel")

        // Prepare parameters
        var parameters: [String: Any] = [
            "name": name,
            "email": email,
            "phone": phoneNumber,
            "alternateNumber": alternativeNumber,
            "dob": dateOfBirth,
            "civilId": civilId
        ]

        // Add image data if available
        if let image = selectedProfileImage {
            let imageData = image.jpegData(compressionQuality: 0.8)
            parameters["profilePic"] = imageData
            Logger.info("📸 Profile image prepared for upload", tag: "SettingsViewModel")
        }

        await onApiCall(
            { try await self.api.updateProfile(parameters: parameters) },
            onSuccess: { response in
                Logger.info("✅ Profile updated successfully", tag: "SettingsViewModel")
                // Update local profile data
                if let updatedProfile = response.data?.profile {
                    self.userProfileModel = updatedProfile
                    self.assignValues(updatedProfile)
                }
                // Show success message
                self.showToast("Profile updated successfully")
                self.appState?.showToast(.init(type: .success, message: response.message))
            },
            onFailure: { error in
                Logger.error("❌ Failed to update profile: \(error)", tag: "SettingsViewModel")
                self.showToast("Failed to update profile: \(error)")
            }
        )
    }

    // MARK: - Image Handling Methods

    /// Handles camera/photo selection button tap
    func handleProfileImageTap() {
        Logger.info("📸 Profile image tap detected", tag: "SettingsViewModel")
        showImageSelectionSheet = true
    }

    /// Handles camera selection from action sheet
    func selectCamera() {
        Logger.info("📷 Camera selection requested", tag: "SettingsViewModel")

        Task {
            let hasPermission = await PermissionManager.shared.requestCameraPermission()

            await MainActor.run {
                if hasPermission {
                    self.imagePickerSourceType = .camera
                    self.showImagePicker = true
                } else {
                    self.showPermissionDeniedAlert(for: "camera")
                }
            }
        }
    }

    /// Handles photo library selection from action sheet
    func selectPhotoLibrary() {
        Logger.info("📚 Photo library selection requested", tag: "SettingsViewModel")

        Task {
            let hasPermission = await PermissionManager.shared.requestPhotoLibraryPermission()

            await MainActor.run {
                if hasPermission {
                    if #available(iOS 14.0, *) {
                        self.showPhotoPicker = true
                    } else {
                        self.imagePickerSourceType = .photoLibrary
                        self.showImagePicker = true
                    }
                } else {
                    self.showPermissionDeniedAlert(for: "photo library")
                }
            }
        }
    }

    /// Handles removing the current profile photo
    func removeProfilePhoto() {
        Logger.info("🗑️ Profile photo removal requested", tag: "SettingsViewModel")
        selectedProfileImage = nil
        // Note: You might want to also update the server to remove the profile photo
    }

    /// Handles image selection from picker
    func handleImageSelected(_ image: UIImage?) {
        Logger.info("📸 Image selected from picker", tag: "SettingsViewModel")
        selectedProfileImage = image
    }

    /// Shows permission denied alert
    private func showPermissionDeniedAlert(for feature: String) {
        permissionAlertMessage = "\(feature.capitalized) access is required to select profile photos. Please enable it in Settings."
        showPermissionAlert = true
    }

    /// Opens app settings for permission management
    func openAppSettings() {
        PermissionManager.shared.openAppSettings()
    }

    /// Shows toast message (placeholder - implement based on your toast system)
    private func showToast(_ message: String) {
        // Implement toast showing logic here
        Logger.info("🍞 Toast: \(message)", tag: "SettingsViewModel")
    }

    func assignValues(_ value: Profile?) {
        guard let value = value else { return }
        name = value.name
        email = value.email
        phoneNumber = value.phone
        alternativeNumber = value.alternateNumber ?? ""
        dateOfBirth = value.dob ?? ""
        civilId = value.civilID ?? ""

        if let date = value.dob?.toDate(format: "yyyy-MM-dd") { selectedDate = date }
    }

    func resetForm() {
        assignValues(userProfileModel)
    }

    func loadProfile() {
        Task {
            await fetchProfile()
        }
    }

    private func fetchProfile() async {
        let parameters: [String: Any] = [:]

        await onApiCall(
            { try await self.api.profile(parameters: parameters) },
            onSuccess: { response in
                // Handle successful profile data
                let data = response.data?.profile
                self.userProfileModel = data
                self.assignValues(data)
                Logger.info("Profile response: \(response)", tag: "HomeViewModel")
            },
            onFailure: { error in
                // Handle profile loading failure
                Logger.error("Failed to load profile: \(error)", tag: "HomeViewModel")
            }
        )
    }

    func loadProductsList() {
        Task {
            await fetchProductsList()
        }
    }

    private func fetchProductsList() async {
        let parameters: [String: Any] = [
            "page": 1,
            "limit": 20
        ]

        await onApiCall(
            { try await self.api.productsList(parameters: parameters) },
            onSuccess: { response in
                // Handle successful products list
                Logger.info("Products list response: \(response)", tag: "HomeViewModel")
            },
            onFailure: { error in
                // Handle products list loading failure
                Logger.error("Failed to load products list: \(error)", tag: "HomeViewModel")
            }
        )
    }

    // MARK: - Logout Functionality

    /// Shows logout confirmation alert
    func requestLogout() {
        Logger.info("🚪 Logout confirmation requested", tag: "SettingsViewModel")
        showLogoutConfirmation = true
    }

    /// Performs logout by clearing all tokens and navigating to sign-in screen
    func confirmLogout() {
        Logger.info("🚪 Logout confirmed by user", tag: "SettingsViewModel")
        showLogoutConfirmation = false
        Task {
            await performLogout()
        }
    }

    /// Cancels the logout process
    func cancelLogout() {
        Logger.info("🚫 Logout cancelled by user", tag: "SettingsViewModel")
        showLogoutConfirmation = false
    }

    /// Performs the actual logout operation
    private func performLogout() async {
        updatePageState(.loading(true))
        await MainActor.run {
            isLoggingOut = true
            Logger.info("🚪 Starting logout process", tag: "SettingsViewModel")
        }

        // Clear all authentication tokens and preferences
        TokenManager.shared.clearAllTokens()
        Logger.info("🧹 All tokens and preferences cleared", tag: "SettingsViewModel")

        // Clear any additional user data
        AppState.isLoggedIn = false
        AppState.user = nil
        Logger.info("🗑️ User data cleared from AppState", tag: "SettingsViewModel")

        // Add a small delay to show the logout loading state
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        updatePageState(.stable)
        await MainActor.run {
            isLoggingOut = false

            // Navigate back to authentication screen
            guard let appState = self.appState else {
                Logger.error("❌ AppState not available during logout", tag: "SettingsViewModel")
                return
            }

            appState.updateInitialScreen(.authentication)
            Logger.info("✅ Logout completed - navigated to authentication screen", tag: "SettingsViewModel")
        }
    }
}
