//
//  HomeModel.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import Foundation

// MARK: - ProfileModel

struct ProfileModel: Codable {
    private let data: [Profile]

    var profile: Profile? {
        data.first
    }
}

// MARK: - Datum

struct Profile: Codable {
    let phone: String
    let civilID, alternateNumber: String?
    let id: Int
    let dob: String?
    let profilePic: String
    let email, type, name: String

    enum CodingKeys: String, CodingKey {
        case phone
        case civilID = "civilId"
        case alternateNumber, id, dob, profilePic, email, type, name
    }
}
