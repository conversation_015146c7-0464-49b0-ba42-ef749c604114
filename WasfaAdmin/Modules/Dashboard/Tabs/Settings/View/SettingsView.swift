//
//  SettingsView.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

struct SettingsView: View {
    @StateObject private var viewModel = SettingsViewModel()
   

    var body: some View {
        SuperView(viewModel: viewModel) {
            // Scrollable Content
            ScrollView {
                VStack(spacing: 0) {
                    // Profile Image Section
                    profileImageSection

                    // Form Fields Section
                    formFieldsSection

                    // Action Buttons Section
                    actionButtonsSection

                    // Logout Section
                    logoutSection

                    // Loading State
                    if viewModel.isLoading {
                        ProgressView()
                            .padding()
                    }

                    // Bottom spacing
                    Spacer(minLength: 100)
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)
            .scrollIndicators(.hidden)
            .padding(.horizontal, 16)
            .background(Color.white)
        }
//        .onLoad(perform: viewModel.loadHomePage)
        .alert("Logout Confirmation", isPresented: $viewModel.showLogoutConfirmation) {
            But<PERSON>("Cancel", role: .cancel) {
                viewModel.cancelLogout()
            }
            .accessibilityLabel("Cancel logout")

            <PERSON><PERSON>("Logout", role: .destructive) {
                viewModel.confirmLogout()
            }
            .accessibilityLabel("Confirm logout")
        } message: {
            Text("Are you sure you want to logout?")
                .accessibilityLabel("Logout confirmation message")
        }
        .tint(nil)
        .sheet(isPresented: $viewModel.showDatePicker) {
            VStack(spacing: 0) {
                HStack {
                    Button("Cancel") {
                        viewModel.showDatePicker = false
                    }
                    .padding()
                    .foregroundColor(.red)

                    Spacer()

                    Button("Submit") {
                        viewModel.onDatePickerDisappear()
                        viewModel.showDatePicker = false
                    }
                    .padding()
                    .foregroundColor(.accentColor)
                }

                Divider()

                DatePicker("Select Date", selection: $viewModel.selectedDate, displayedComponents: .date)
                    .datePickerStyle(.graphical)
                    .labelsHidden()
                    .padding(.horizontal)

                Spacer()
            }
            .presentationDetents([.height(420.relativeHeight)])
        }
    }

    // MARK: - Profile Image Section

    private var profileImageSection: some View {
        VStack {
            ZStack {
                // Profile Image Container - matching Android 160dp circular
                Circle()
                    .fill(Color(hex: "#edeeee") ?? Color.gray.opacity(0.1))
                    .frame(width: 160, height: 160)
                    .overlay {
                        Group {
                            if let selectedImage = viewModel.selectedProfileImage {
                                // Show selected image from picker
                                Image(uiImage: selectedImage)
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: 160, height: 160)
                                    .clipShape(Circle())
                            } else if let imageUrlString = viewModel.userProfileModel?.profilePic, !imageUrlString.isEmpty {
                                // Show profile image from server
                                NetworkImageView(path: imageUrlString, contentMode: .fill)
                                    .frame(width: 160, height: 160)
                                    .clipShape(Circle())
                                    // add some border
                                    .overlay(
                                        Circle()
                                            .stroke(style: StrokeStyle(lineWidth: 2))
                                            .foregroundColor(Color(hex: "#edeeee") ?? Color.gray.opacity(0.1))
                                    )
                            } else {
                                // Show placeholder
                                Image(systemName: "person.fill")
                                    .font(.system(size: 60))
                                    .foregroundColor(.gray)
                            }
                        }
                    }

                // Camera Icon - positioned at bottom-right like Android
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Button(action: {
                            Logger.info("📸 Profile image edit button tapped", tag: "SettingsView")
                            viewModel.handleProfileImageTap()
                        }) {
                            Image(systemName: "camera.fill")
                                .font(.system(size: 14))
                                .foregroundColor(.black)
                                .frame(width: 26, height: 24)
                                .background(Color.white)
                                .clipShape(Circle())
                                .shadow(radius: 2)
                        }
                        .offset(x: -15, y: -7)
                    }
                }
            }
            .frame(width: 165, height: 160) // Matching Android RelativeLayout size
            .onTapGesture {
                Logger.info("📸 Profile image tapped", tag: "SettingsView")
                viewModel.handleProfileImageTap()
            }
        }
        .padding(.top, 50) // Matching Android 50dp top margin
        // Image Selection Sheet
        .sheet(isPresented: $viewModel.showImageSelectionSheet) {
            ImageSelectionSheet(
                isPresented: $viewModel.showImageSelectionSheet,
                onCameraSelected: {
                    viewModel.selectCamera()
                },
                onPhotoLibrarySelected: {
                    viewModel.selectPhotoLibrary()
                },
                onRemovePhoto: viewModel.selectedProfileImage != nil ? {
                    viewModel.removeProfilePhoto()
                } : nil
            )
            .presentationDetents([.height(280)])
            .presentationDragIndicator(.visible)
        }
        // Legacy Image Picker (iOS 13 and camera)
        .sheet(isPresented: $viewModel.showImagePicker) {
            ImagePicker(
                selectedImage: $viewModel.selectedProfileImage,
                sourceType: viewModel.imagePickerSourceType,
                onImageSelected: { image in
                    viewModel.handleImageSelected(image)
                },
                onError: { error in
                    Logger.error("❌ Image picker error: \(error)", tag: "SettingsView")
                }
            )
        }
        // Modern Photo Picker (iOS 14+)
        .sheet(isPresented: $viewModel.showPhotoPicker) {
            PhotoPicker(
                selectedImage: $viewModel.selectedProfileImage,
                onImageSelected: { image in
                    viewModel.handleImageSelected(image)
                },
                onError: { error in
                    Logger.error("❌ Photo picker error: \(error)", tag: "SettingsView")
                }
            )
        }
        // Permission Alert
        .alert("Permission Required", isPresented: $viewModel.showPermissionAlert) {
            Button("Settings") {
                viewModel.openAppSettings()
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text(viewModel.permissionAlertMessage)
        }
    }

    // MARK: - Form Fields Section

    private var formFieldsSection: some View {
        VStack(spacing: 0) {
            // Name Field
            CustomInputField(
                title: "Name",
                text: $viewModel.name,
                placeholder: "name"
            )
            .padding(.top, 20) // Matching Android 20dp top margin for first field

            // Email Field
            CustomInputField(
                title: "Email",
                text: $viewModel.email,
                placeholder: "email",
                keyboardType: .emailAddress
            )
            .padding(.top, 20) // Matching Android 20dp top margin between fields

            // Phone Number Field
            CustomInputField(
                title: "Phone Number",
                text: $viewModel.phoneNumber,
                placeholder: "phone number",
                keyboardType: .phonePad
            )
            .padding(.top, 20)

            // Alternative Number Field
            CustomInputField(
                title: "Alternative Number",
                text: $viewModel.alternativeNumber,
                placeholder: "alternative number",
                keyboardType: .phonePad
            )
            .padding(.top, 20)

            // Date of Birth Field
            CustomInputField(
                title: "Date of Birth",
                text: $viewModel.dateOfBirth,
                placeholder: "choose date",
                isDateField: true,
                onDateFieldTapped: viewModel.onDateFieldTapped
            )
            .padding(.top, 20)

            // Civil ID Field
            CustomInputField(
                title: "Civil ID",
                text: $viewModel.civilId,
                placeholder: "civil id",
                keyboardType: .numberPad
            )
            .padding(.top, 20)
        }
    }

    // MARK: - Action Buttons Section

    private var actionButtonsSection: some View {
        HStack(spacing: 0) {
            // Save Button - matching Android 190x46dp with doctor_main_color
            Button(action: viewModel.saveProfile) {
                Text("Save")
                    .font(.custom("Roboto-Medium", size: 18))
                    .foregroundColor(.white)
                    .frame(width: 190, height: 46)
                    .background(Color(hex: "#A42161") ?? ColorConstants.Blue6009e) // doctor_main_color
                    .cornerRadius(12)
            }

            // Reset Button - matching Android 100x46dp, text-only with 30dp margin
            Button(action: viewModel.resetForm) {
                Text("Reset")
                    .font(.custom("Roboto-Medium", size: 18))
                    .foregroundColor(Color(hex: "#A42161") ?? ColorConstants.Blue6009e) // doctor_main_color
                    .frame(width: 100, height: 46)
            }
            .padding(.leading, 30) // Matching Android 30dp left margin

            Spacer()
        }
        .padding(.top, 36) // Matching Android 80dp top margin
    }

    // MARK: - Logout Section

    private var logoutSection: some View {
        HStack(spacing: 14) { // Matching Android 14dp margin between icon and text
            // Logout Icon - matching Android 24x24dp with red tint
            Image(systemName: "rectangle.portrait.and.arrow.right")
                .font(.system(size: 20))
                .foregroundColor(Color.red)
                .frame(width: 24, height: 24)

            // Logout Text
            Text("Log Out")
                .font(.custom("Roboto-Bold", size: 14))
                .foregroundColor(Color.red)

            Spacer()
        }
        .padding(.leading, 10) // Matching Android 10dp start margin
        .padding(.top, 50) // Matching Android 50dp top margin
        .onTapGesture {
            viewModel.requestLogout()
        }
    }
}

#Preview {
    NavigationStack {
        SettingsView()
            .attachAllEnvironmentObjects()
    }
}
