//
//  PrescriptionsView.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI
import UIKit

struct PrescriptionsView: View {
    @StateObject private var viewModel = PrescriptionsViewModel()

    var body: some View {
        SuperView(viewModel: viewModel) {
            // MARK: - Content

            ScrollView {
                LazyVStack(spacing: 20) {
                    // MARK: - Search Bar

                    searchBarSection

                    // MARK: - New Prescription Button

                    newPrescriptionButton

                    // MARK: - Prescriptions List

                    prescriptionsListSection

                    // MARK: - Load More Button

                    if viewModel.hasMorePages && !viewModel.prescriptions.isEmpty {
                        loadMoreButton
                    }

                    Spacer(minLength: 20)
                }
                .padding(.horizontal, 16)
                .padding(.top, 20)
            }
            .navigationTitle("Prescriptions")
            .navigationBarTitleDisplayMode(.large)
            .scrollIndicators(.hidden)
            .background(.gray.opacity(0.05))
            .refreshable {
                viewModel.refreshPrescriptions()
            }
//            .background(.white)
        }
        .onLoad(perform: viewModel.loadPrescriptions)
    }

    // MARK: - Search Bar Section

    private var searchBarSection: some View {
        HStack {
            Image("search_icon")
                .resizable()
                .frame(width: 35, height: 35)
                .padding(.leading, 10)

            TextField("Search... ", text: $viewModel.searchText)
                .font(.custom("Roboto-Regular", size: 15))
                .foregroundColor(.black)
                .background(Color.clear)
                .onChange(of: viewModel.searchText, viewModel.searchPrescriptions)

//            Button {} label: {
//                Image(.prescriptionSearchFilter)
//                    .resizable()
//                    .frame(width: 35, height: 35)
//                    .padding(.horizontal, 8)
//            }
        }
        .frame(height: 50)
        .background(Color.white)
        .clipShape(.capsule)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }

    // MARK: - New Prescription Button

    private var newPrescriptionButton: some View {
        Button(action: {
            // Navigate to add new prescription
            viewModel.navigateToNewPrescription()
        }) {
            HStack(spacing: 10) {
                Image("icon_brown_plus")
                    .resizable()
                    .frame(width: 18, height: 18)

                Text("New Prescription")
                    .font(Font.custom("Poppins", size: 16).weight(.bold))
                    .fontWeight(.bold)
                    .foregroundColor(Color("DoctorMainColor"))
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.09).shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1))
            .cornerRadius(12)
        }
    }

    // MARK: - Prescriptions List Section

    private var prescriptionsListSection: some View {
        VStack(spacing: 16) {
            if viewModel.isLoading {
                ProgressView("Loading...")
                    .frame(maxWidth: .infinity, minHeight: 200)
            } else if viewModel.prescriptions.isEmpty {
                VStack(spacing: 16) {
                    Text("No Data Found")
                        .font(.custom("Roboto-Bold", size: 14))
                        .foregroundColor(.black)
                        .padding(.top, 50)
                }
                .frame(maxWidth: .infinity, minHeight: 200)
            } else {
                LazyVStack(spacing: 10) {
                    ForEach(viewModel.prescriptions) { prescription in
                        prescriptionCard(prescription: prescription)
                    }
                }
            }
        }
    }

    // MARK: - Load More Button

    private var loadMoreButton: some View {
        Button(action: {
            viewModel.loadMorePrescriptions()
        }) {
            HStack {
                if viewModel.isLoadingMore {
                    ProgressView()
                        .scaleEffect(0.8)
                        .foregroundColor(Color("DoctorMainColor"))
                    Text("Loading...")
                        .font(.custom("Roboto-Medium", size: 14))
                        .foregroundColor(Color("DoctorMainColor"))
                } else {
                    Text("Load More")
                        .font(.custom("Roboto-Medium", size: 14))
                        .foregroundColor(Color("DoctorMainColor"))
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 44)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
        .onAppear(perform: {
            viewModel.loadMorePrescriptions()
        })
        .disabled(viewModel.isLoadingMore)
    }

    // MARK: - Prescription Card

    private func prescriptionCard(prescription: PrescriptionModel.Prescription) -> some View {
        VStack(spacing: 0) {
            // Patient Info Section
            HStack(alignment: .center, spacing: 10) {
                NetworkImageView(path: prescription.patientInfo.first?.profilePic, contentMode: .fill)
                    .frame(width: 62, height: 62)
                    .clipShape(Circle())
                    // add some border
                    .overlay(
                        Circle()
                            .stroke(style: StrokeStyle(lineWidth: 2))
                            .foregroundColor(.gray.opacity(0.1))
                    )

                VStack(alignment: .leading, spacing: 5) {
                    Text(prescription.name)
                        .font(.custom("Roboto-Bold", size: 18))
                        .foregroundColor(.black)
                        .multilineTextAlignment(.leading)

                    Text(prescription.patientInfo.first?.phone ?? "")
                        .font(.custom("Roboto-Bold", size: 14))
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.leading)
                }

                Spacer()
            }
            .padding(.horizontal, 15)
            .padding(.top, 15)

            // Medications Section
            HStack(alignment: .top, spacing: 10) {
                Image(.tabMedications)
                    .renderingMode(.template)
                    .resizable()
                    .foregroundStyle(.adminMain)
                    .frame(width: 17.relativeFontSize, height: 17.relativeFontSize)
                    .frame(width: 32.relativeFontSize, height: 32.relativeFontSize)
                    .background(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.09))
                    .cornerRadius(10.relativeFontSize)

                VStack(alignment: .leading, spacing: 2) {
                    Text("Medications Prescribed")
                        .font(.custom("Roboto-Medium", size: 12))
                        .foregroundColor(.gray)

                    Text("\(prescription.medicationsPrescribed)")
                        .font(.custom("Roboto-Bold", size: 12))
                        .foregroundColor(Color("DoctorMainColor"))
                }

                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)

            Button {
                viewModel.handlePrescriptionTap(prescriptionId: prescription.prescriptionID)
            } label: {
                // View Details Button
                HStack {
                    Text("View Details")
                        .font(.custom("Roboto-Regular", size: 16))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 38)
                .background(Color("DoctorMainColor"))
                .cornerRadius(12)
                .padding(.horizontal, 10)
                .padding(.top, 20)
                .padding(.bottom, 10)
            }
        }
        .frame(height: 212)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    NavigationStack {
        PrescriptionsView().attachAllEnvironmentObjects()
    }
}
