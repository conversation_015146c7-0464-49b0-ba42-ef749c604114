//
//  PrescriptionDetailsModel.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import Foundation

struct PrescriptionDetailsRequest: Codable {
    let id: String
}

// MARK: - PrescriptionDetailsModel

// MARK: - PrescriptionDetailsModel

struct PrescriptionDetailsModel: Codable {
    let id: String
    let patientInfo: PatientInfo
    let description: String?
    let medications: [Medication]

    // MARK: - Medication

    struct Medication: Codable, Identifiable {
        let productID: Int
        let description, courseDay, doseTime, courseDuration, doseday, dose: String?
        let  actualPrice: String
        let productName, variation, dosage: String
        let price : String
        let productThumbnailImage: String?
        let duration, unitPrice: String
        let quanity: Int

        enum CodingKeys: String, CodingKey {
            case productID = "productId"
            case description, dose, actualPrice
            case courseDay = "course_day"
            case productName
            case doseTime = "dose_time"
            case variation, dosage, price
            case courseDuration = "course_duration"
            case doseday, productThumbnailImage, duration, unitPrice, quanity
        }

        var id: Int { productID }
    }

    // MARK: - PatientInfo

    struct PatientInfo: Codable {
        let phone: String
        let address: [Address]
        let id: Int
        let dob, email, altPhone: String?
        let name: String
        let civilID: String?

        enum CodingKeys: String, CodingKey {
            case phone, address, id, dob, email
            case altPhone = "alt_phone"
            case name
            case civilID = "civil_id"
        }
    }

    // MARK: - Address

    struct Address: Codable {
        let id: Int
        let appartment: String?
        let lastName, firstName, governorateName: String
        let areaID: Int
        let phone, building, street, addressTitle: String
        let governorateID: Int
        let alternatePhone: String?
        let areaName, block: String
        let setDefault: Int
        let floor, email: String?

        enum CodingKeys: String, CodingKey {
            case id, appartment, lastName, firstName, governorateName
            case areaID = "areaId"
            case phone, building, street, addressTitle
            case governorateID = "governorateId"
            case alternatePhone, areaName, block, setDefault, floor, email
        }
    }
}
