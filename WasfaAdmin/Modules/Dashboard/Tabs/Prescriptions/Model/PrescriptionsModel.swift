//
//  PrescriptionsModel.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import Foundation

// MARK: - Prescription API Request Models

struct PrescriptionListRequest: Codable {
    let perPage: String
    let pageNo: String
    let keyword: String

    enum CodingKeys: String, CodingKey {
        case perPage = "per_page"
        case pageNo = "page_no"
        case keyword
    }
}



// MARK: - PrescriptionModel
struct PrescriptionModel: Codable {
    let totalProductsCount: Int
    let prescriptions: [Prescription]
    let totalPages: Int
    
    
    // MARK: - Prescription
    struct Prescription: Codable, Identifiable {
        var id: UUID = UUID()
        let prescriptionID: String
        let medicationsPrescribed: Int
        let patientInfo: [PatientInfo]
        let name: String
        let description: String?
        let medications: [Medication]
        
        
        enum CodingKeys: String, CodingKey {
            case prescriptionID = "id"
            case medicationsPrescribed
            case patientInfo
            case name
            case description
            case medications
        }
    }

    // MARK: - Medication
    struct Medication: Codable {
        let doseTime: String?
        let dosage, productName: String
        let description: String?
        let duration: String

        enum CodingKeys: String, CodingKey {
            case doseTime = "dose_time"
            case dosage
            case productName = "product_name"
            case description, duration
        }
    }

    // MARK: - PatientInfo
    struct PatientInfo: Codable {
        let phone: String
        let civilID: String?
        let alternateNumber: String?
        let id: Int
        let dob: String?
        let profilePic: String
        let email: String?
        let type, name: String

        enum CodingKeys: String, CodingKey {
            case phone
            case civilID = "civilId"
            case alternateNumber, id, dob, profilePic, email, type, name
        }
    }

}


