//
//  PrescriptionsViewModel.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

class PrescriptionsViewModel: SuperViewModel {
    @Published var prescriptions: [PrescriptionModel.Prescription] = []
    @Published var isLoading: Bool = false
    @Published var searchKeyword: String = ""
    @Published var currentPage: Int = 1
    @Published var totalPages: Int = 1
    @Published var totalPrescriptionsCount: Int = 0
    @Published var hasMorePages: Bool = false
    @Published var isLoadingMore: Bool = false
    
    
    @Published var searchText: String = ""

    // Constants for pagination
    private let itemsPerPage = "5"

    private var isInitialLoad: Bool = false

    override init() {
        super.init()
    }

    func loadPrescriptions() {
        currentPage = 1
        prescriptions = []
        Task {
            await fetchPrescriptions()
        }
    }

    func searchPrescriptions(oldValue: String,keyword: String) {
        searchKeyword = keyword
        currentPage = 1
        prescriptions = []
        Task {
            await fetchPrescriptions()
        }
    }

    func loadMorePrescriptions() {
        guard hasMorePages && !isLoadingMore else { return }
        currentPage += 1
        Task {
            await fetchPrescriptions(isLoadingMore: true)
        }
    }

    private func fetchPrescriptions(isLoadingMore: Bool = false) async {
        if isLoadingMore {
            self.isLoadingMore = true
        } else {
            isLoading = true
        }

        let parameters: [String: Any] = [
            "per_page": itemsPerPage,
            "page_no": String(currentPage),
            "keyword": searchKeyword
        ]

        await onApiCall(
            { try await self.api.prescriptionsList(parameters: parameters) },
            withLoadingIndicator: !isInitialLoad,
            onSuccess: { response in

                if !self.isInitialLoad { self.isInitialLoad = true }

                if let data = response.data {
                    if isLoadingMore {
                        // Append new prescriptions to existing list
                        self.prescriptions.append(contentsOf: data.prescriptions)
                    } else {
                        // Replace prescriptions list
                        self.prescriptions = data.prescriptions
                    }

                    self.totalPrescriptionsCount = data.totalProductsCount
                    self.totalPages = Int(data.totalPages)
                    self.hasMorePages = self.currentPage < self.totalPages

                    Logger.info("✅ Prescriptions loaded successfully: \(data.prescriptions.count) items", tag: "PrescriptionsViewModel")
                } else {
                    Logger.warning("⚠️ No prescription data received", tag: "PrescriptionsViewModel")
                }

                if isLoadingMore {
                    self.isLoadingMore = false
                } else {
                    self.isLoading = false
                }
            },
            onFailure: { error in
                Logger.error("❌ Failed to load prescriptions: \(error)", tag: "PrescriptionsViewModel")

                if isLoadingMore {
                    self.isLoadingMore = false
                    // Revert page increment on failure
                    self.currentPage -= 1
                } else {
                    self.isLoading = false
                }
            }
        )
    }

    func navigateToNewPrescription() {
        // TODO: Implement navigation to new prescription screen
        Logger.info("🆕 Navigate to new prescription", tag: "PrescriptionsViewModel")
        
        self.appState?.updateSelectedTab(.center)
    }

    func handlePrescriptionTap(prescriptionId: String) {
        Logger.info("Prescription tapped: \(prescriptionId)", tag: "RecentPrescriptionsSectionView")

        // Navigate to prescription detail view using RouterManager
        routerManager?.push(to: .prescriptionDetails(prescriptionId: prescriptionId), where: .prescriptionsRoute)
    }

    private func fetchPrescriptionDetails(prescriptionId: String) async {
        let parameters: [String: Any] = [
            "id": prescriptionId
        ]

        await onApiCall(
            { try await self.api.prescriptionDetails(parameters: parameters) },
            onSuccess: { response in
                if let data = response.data, !data.isEmpty {
                    Logger.info("✅ Prescription details loaded successfully", tag: "PrescriptionsViewModel")
                    // TODO: Navigate to prescription details screen with data
                    // For now, just log the details
                    Logger.debug("📋 Prescription details: \(data)", tag: "PrescriptionsViewModel")
                } else {
                    Logger.warning("⚠️ No prescription details received", tag: "PrescriptionsViewModel")
                }
            },
            onFailure: { error in
                Logger.error("❌ Failed to load prescription details: \(error)", tag: "PrescriptionsViewModel")
            }
        )
    }

    // MARK: - Utility Methods

    func refreshPrescriptions() {
//        searchKeyword = ""
        loadPrescriptions()
    }
}
