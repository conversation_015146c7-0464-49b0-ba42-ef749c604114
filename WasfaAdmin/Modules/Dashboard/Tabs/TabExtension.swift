//
//  TabExtension.swift
//  Wasfa
//
//  Created by Apple on 20/09/2024.
//

import SwiftUI

extension Tab {
    @ViewBuilder
    var view: some View {
        switch self {
        // PRESERVE: Keep existing view mappings unchanged
        case .home:
           HomeView()
        case .medications:
           MedicationsView()
        case .center:
           CenterView()
        case .prescriptions:
           ReportView()
        case .settings:
            SettingsView()

        // EXTEND: Add new views for admin tabs (placeholder for now)
        case .pos:
            POSView()
        case .products:
            ProductsView()
        case .sales:
            SalesView()
        case .customers:
            CustomersView()
        }
    }
}
