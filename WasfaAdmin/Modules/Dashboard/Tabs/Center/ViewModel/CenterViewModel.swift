//
//  HomeViewModel.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

class CenterViewModel: SuperViewModel {
    // MARK: - Published Properties

    @Published var isLoading: Bool = false
    @Published var patientFormData = PatientFormData()
    @Published var formErrors = FormValidationErrors()
    @Published var cartItems: [CartRXModel.CartItem] = []
    @Published var selectedPatientId: Int?
    @Published var isCreatingPatient: Bool = false
    @Published var isSubmittingPrescription: Bool = false

    @Published var isSearchingPatient: Bool = false

    @Published var selectedDate: Date?

    override init() {
        super.init()
    }

    func onBackButtonTapped() {
        routerManager?.goBack()
    }
    
    
    

    // MARK: - Form Validation

    func validateForm() -> Bool {
        formErrors.clearAll()
        var isValid = true

        // Validate patient name
        if patientFormData.name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            formErrors.nameError = "Please enter full name"
            isValid = false
        }

        // Validate phone number
        if patientFormData.phone.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            formErrors.phoneError = "Please enter contact number"
            isValid = false
        } else if !isValidPhone(patientFormData.phone) {
            formErrors.phoneError = "Please enter a valid phone number"
            isValid = false
        }

        // Validate civil ID
        if patientFormData.civilId.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            formErrors.civilIdError = "Please enter civil ID"
            isValid = false
        }

        // Validate email
        if patientFormData.email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            formErrors.emailError = "Please enter email"
            isValid = false
        } else if !isValidEmail(patientFormData.email) {
            formErrors.emailError = "Please enter a valid email"
            isValid = false
        }

        // Validate date of birth
        if patientFormData.dateOfBirth.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            formErrors.dobError = "Please choose date of birth"
            isValid = false
        }

        return isValid
    }

    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }

    private func isValidPhone(_ phone: String) -> Bool {
        return phone.count >= 8 && phone.allSatisfy { $0.isNumber }
    }

    // MARK: - Patient Management

    func onPhoneNumberChanged(_ phoneNumber: String) {
        // Clear previous error when user starts typing
        formErrors.phoneError = nil

        // Trigger patient search when phone number reaches 8 digits
        if phoneNumber.count == 8 && phoneNumber.allSatisfy({ $0.isNumber }) {
            Task {
                await searchPatientByPhone(phoneNumber)
            }
        } else if phoneNumber.count > 8 {
            // Clear patient data if phone number is invalid
            clearPatientData()
        }
    }

    // MARK: - Real-time Form Validation

    func onNameChanged(_ name: String) {
        if !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            formErrors.nameError = nil
        }
    }

    func onCivilIdChanged(_ civilId: String) {
        if !civilId.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            formErrors.civilIdError = nil
        }
    }

    func onEmailChanged(_ email: String) {
        if !email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            if isValidEmail(email) {
                formErrors.emailError = nil
            } else {
                formErrors.emailError = "Please enter a valid email"
            }
        } else {
            formErrors.emailError = nil
        }
    }

    func onDateChanged(_ oldValue: Date?,_ newValue: Date?) {
        if newValue != nil {
            formErrors.dobError = nil
        }

        if let date = newValue {
            patientFormData.dateOfBirth = date.toString(outputFormate: "yyyy-MM-dd")
        }
    }

    private func searchPatientByPhone(_ phoneNumber: String) async {
        isSearchingPatient = true

        let parameters: [String: Any] = [
            "keyword": "\(patientFormData.countryCode.dialCode)\(phoneNumber)"
        ]

        await onApiCall(
            { try await self.api.customerList(parameters: parameters) },
            withLoadingIndicator: false,
            onSuccess: { response in
                self.isSearchingPatient = false

                let fullPhoneNumber = "\(self.patientFormData.countryCode.dialCode)\(phoneNumber)"

                let patient = response.data?.first(where: { $0.phone == fullPhoneNumber })

                if let patient = patient {
                    self.populatePatientData(patient)
                    Logger.info("✅ Patient found and populated: \(patient.name)", tag: "CenterViewModel")
                } else {
                    // No patient found, clear any existing data
                    self.clearPatientData()
                    Logger.info("ℹ️ No patient found for phone: +965\(phoneNumber)", tag: "CenterViewModel")
                }
            },
            onFailure: { error in
                self.isSearchingPatient = false
                self.clearPatientData()
                Logger.error("❌ Failed to search patient: \(error)", tag: "CenterViewModel")
            }
        )
    }

    private func populatePatientData(_ patient: PatientSearchInfo) {
        patientFormData.name = patient.name ?? ""
        patientFormData.civilId = patient.civilID ?? ""
        patientFormData.email = patient.email ?? ""
        patientFormData.selectedPatientId = patient.id
        patientFormData.dateOfBirth = patient.dob ?? ""
        selectedDate = patient.dob?.toDate(format: "yyyy-MM-dd")

        selectedPatientId = patient.id
    }

    private func clearPatientData() {
        selectedPatientId = nil
        // Keep phone number but clear other fields
        let currentPhone = patientFormData.phone
        let currentCountryCode = patientFormData.countryCode
        patientFormData = PatientFormData()
        patientFormData.phone = currentPhone
        patientFormData.countryCode = currentCountryCode
        selectedDate = nil
    }

    func createPatient() {
        guard validateForm() else {
            Logger.warning("Form validation failed", tag: "CenterViewModel")
            return
        }

        Task {
            await performCreatePatient()
        }
    }

    func updatePatientInformation() {
        guard let _ = selectedPatientId else {
            updatePageState(.message(config: AlertConfig(
                title: "No Patient Selected",
                text: "Please search for a patient first by entering their phone number."
            )))
            return
        }

        guard validateForm() else {
            Logger.warning("Form validation failed for patient update", tag: "CenterViewModel")
            return
        }

        Task {
            await performCreatePatient()
        }
    }

//    private func performUpdatePatient() async {
//        isCreatingPatient = true
//
//        let parameters: [String: Any] = [
//            "name": patientFormData.name,
//            "phone": "\(patientFormData.countryCode.dialCode)\(patientFormData.phone)",
//            "civilId": patientFormData.civilId,
//            "email": patientFormData.email,
//            "dob": patientFormData.dateOfBirth,
//            "altMobileNo": "",
//            "id": selectedPatientId
//        ]
//
//        await onApiCall(
//            { try await self.api.createPatient(parameters: parameters) },
//            onSuccess: { response in
//                self.isCreatingPatient = false
//                if let patientData = response.data {
//                    Logger.info("✅ Patient updated successfully: \(patientData.id)", tag: "CenterViewModel")
//                    self.updatePageState(.success(message: "Patient information updated successfully!", onDone: {
//                        // Optionally refresh patient data
//                    }))
//                }
//            },
//            onFailure: { error in
//                self.isCreatingPatient = false
//                Logger.error("❌ Failed to update patient: \(error)", tag: "CenterViewModel")
//            }
//        )
//    }

    func onDeleteCartItem(on id: Int) {
        Task {
            await deleteCartItem(id: id)
        }
    }

    private func deleteCartItem(id: Int) async {
        let parameters: [String: Any] = [
            "id": String(id)
        ]

        await onApiCall(
            { try await self.api.removeCartItem(parameters: parameters) },
            onSuccess: { _ in
                // Remove item from local cart array
                self.cartItems.removeAll { $0.id == id }
                Logger.info("✅ Cart item deleted successfully: \(id)", tag: "CenterViewModel")

                // Reload cart to ensure consistency
                Task {
                    await self.fetchCartItems()
                }
            },
            onFailure: { error in
                Logger.error("❌ Failed to delete cart item: \(error)", tag: "CenterViewModel")
            }
        )
    }

    private func assignValuesToPatientFormData(_ value: CartRXModel.PatientInfo?) {
        guard let value = value else { return }

        patientFormData.name = value.name
        patientFormData.civilId = value.civilID ?? ""
        patientFormData.email = value.email ?? ""
        patientFormData.dateOfBirth = value.dob ?? ""
        selectedDate = value.dob?.toDate(format: "yyyy-MM-dd")

        let phoneResult = value.phone.extractCountryCodeAndPhoneNumber(from: CountryCode.allCases)
        patientFormData.phone = phoneResult.phoneNumber
        patientFormData.countryCode = phoneResult.countryCode
    }

    private func performCreatePatient() async {
        isCreatingPatient = true

        var parameters: [String: Any] = [
            "name": patientFormData.name,
            "phone": "\(patientFormData.countryCode.dialCode)\(patientFormData.phone)",
            "civilId": patientFormData.civilId,
            "email": patientFormData.email,
            "dob": patientFormData.dateOfBirth,
            "altMobileNo": ""
        ]

        if let selectedPatientId = patientFormData.selectedPatientId {
            parameters.updateValue(selectedPatientId, forKey: "id")
        }

        await onApiCall(
            { try await self.api.createPatient(parameters: parameters) },
            onSuccess: { response in
                self.isCreatingPatient = false
                if let patientData = response.data {
                    self.selectedPatientId = patientData.id
                    self.appState?.showToast(.init(type: .success, message: response.message))
                    Logger.info("✅ Patient created successfully: \(patientData.id)", tag: "CenterViewModel")
                    // Show success message or navigate to next step
                }
            },
            onFailure: { error in
                self.isCreatingPatient = false
                Logger.error("❌ Failed to create patient: \(error)", tag: "CenterViewModel")
            }
        )
    }

    // MARK: - Cart Management

    func loadCartItems() {
        Task {
            await fetchCartItems()
        }
    }

    private func fetchCartItems() async {
        let parameters: [String: Any] = [:]

        await onApiCall(
            { try await self.api.getCartRX(parameters: parameters) },
            onSuccess: { response in
                if let cartData = response.data {
                    self.assignValuesToPatientFormData(cartData.patientInfo.first)
                    self.cartItems = cartData.cartItems

                    Logger.info("✅ Cart items loaded: \(cartData.cartItems.count) items", tag: "CenterViewModel")
                }
            },
            onFailure: { error in
                Logger.error("❌ Failed to load cart items: \(error)", tag: "CenterViewModel")
            }
        )
    }

    func navigateToAddMedication() {
        guard let _ = selectedPatientId else {
            // Show alert: "Please Add Patient"
            updatePageState(.message(config: AlertConfig(
                title: "Patient Required",
                text: "Please add patient information before adding medications."
            )))
            return
        }

        // Navigate to medication selection screen
        Logger.info("🔄 Navigating to add medication screen", tag: "CenterViewModel")
        routerManager?.push(to: .medicationSelection, where: .centerRoute)
    }

    // MARK: - Prescription Submission

    func createPrescription() {
        guard let _ = selectedPatientId else {
            updatePageState(.message(config: AlertConfig(
                title: "Patient Required",
                text: "Please add patient information before creating prescription."
            )))
            return
        }

        guard !cartItems.isEmpty else {
            updatePageState(.message(config: AlertConfig(
                title: "Medications Required",
                text: "Please add medications before creating prescription."
            )))
            return
        }

        // Navigate to prescription review instead of direct submission
        Logger.info("🔄 Navigating to prescription review", tag: "CenterViewModel")
        routerManager?.push(to: .prescriptionReview, where: .centerRoute)
    }

    private func submitPrescription() async {
        isSubmittingPrescription = true

        let parameters: [String: Any] = [
            "influencerId": "",
            "customerId": selectedPatientId
        ]

        await onApiCall(
            { try await self.api.submitRx(parameters: parameters) },
            onSuccess: { response in
                self.isSubmittingPrescription = false
                if let prescriptionData = response.data {
                    Logger.info("✅ Prescription created successfully: \(prescriptionData.id)", tag: "CenterViewModel")
                    // Navigate to prescription review or success screen
                    self.updatePageState(.success(message: "Prescription created successfully!", onDone: {
                        // Reset form and navigate back
                        self.resetForm()
                    }))
                }
            },
            onFailure: { error in
                self.isSubmittingPrescription = false
                Logger.error("❌ Failed to create prescription: \(error)", tag: "CenterViewModel")
            }
        )
    }

    // MARK: - Helper Methods

    func resetForm() {
        patientFormData = PatientFormData()
        formErrors.clearAll()
        cartItems.removeAll()
        selectedPatientId = nil
        isCreatingPatient = false
        isSubmittingPrescription = false
    }
}
