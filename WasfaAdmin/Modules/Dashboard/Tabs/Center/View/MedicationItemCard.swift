//
//  Untitled.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI

struct MedicationItemCard: View {
    let item: CartRXModel.CartItem
    let onDelete: (Int) -> Void
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
             
                NetworkImageView(path:item.productThumbnailImage, contentMode: .fill)
                .frame(width: 62, height: 62)
                .clipShape(Circle())
                // add some border
                .overlay(
                    Circle()
                        .stroke(style: StrokeStyle(lineWidth: 2))
                        .foregroundColor(.gray.opacity(0.1)))

                Text(item.productName)
                    .font(Font.custom("Poppins", size: 18.91).weight(.semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)

                Button {
                    onDelete(item.id)
                } label: {
                    Image(.prescriptionDelete)
                        .resizable()
                        .frame(width: 30.relativeFontSize, height: 30.relativeFontSize)
                }
            }

            HStack(alignment: .bottom) {
                // Medications Section
                HStack(alignment: .top, spacing: 10) {
                    Image(.tabMedications)
                        .renderingMode(.template)
                        .resizable()
                        .foregroundStyle(.adminMain)
                        .frame(width: 17.relativeFontSize, height: 17.relativeFontSize)
                        .frame(width: 32.relativeFontSize, height: 32.relativeFontSize)
                        .background(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.09))
                        .cornerRadius(10.relativeFontSize)

                    VStack(alignment: .center, spacing: 2) {
                        Text("Dose")
                            .font(Font.custom("Poppins", size: 12))
                            .foregroundColor(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.60))

                        Text("\(item.dose), \(item.doseday), \(item.doseTime)")
                            .font(.custom("Roboto-Bold", size: 12))
                            .foregroundColor(Color("DoctorMainColor"))
                    }
                    Spacer()

                    VStack(alignment: .center, spacing: 2) {
                        Text("Course")
                            .font(Font.custom("Poppins", size: 12))
                            .foregroundColor(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.60))

                        Text("\(item.courseDay), \(item.courseDuration)")
                            .font(.custom("Roboto-Bold", size: 12))
                            .foregroundColor(Color("DoctorMainColor"))
                    }

                    Spacer()

                }.frame(maxWidth: .infinity, alignment: .leading)

                Text(item.unitPrice)
                    .font(.custom("Roboto-Bold", size: 14).bold())
                    .foregroundColor(Color("DoctorMainColor"))
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    // create sample itens
    VStack {
        MedicationItemCard(item: CartRXModel.CartItem.DummyData, onDelete: { _ in })
    }
    .padding()
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(Color(.secondarySystemBackground))
}
