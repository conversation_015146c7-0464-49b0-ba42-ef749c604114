//
//  CenterView.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

struct CenterView: View {
    @StateObject private var viewModel = CenterViewModel()

    var body: some View {
        SuperView(viewModel: viewModel) {
            // MARK: - Content

            ScrollView {
                VStack(spacing: 20) {
                    // MARK: - Patient Information Form

                    patientFormSection

                    // MARK: - Update Patient Information

//                    UpdatePatientInformationButton

                    // MARK: - Medications Section

                    medicationsSection

                    // MARK: - Create Prescription Button

                    if !viewModel.cartItems.isEmpty {
                        createPrescriptionButton
                    }

                    Spacer(minLength: 100)
                }
                .padding(.horizontal, 16)
                .padding(.top, 40)
            }
            .navigationTitle("New Prescription")
            .navigationBarTitleDisplayMode(.large)
            .scrollIndicators(.hidden)
        }
        .onAppear(perform: viewModel.loadCartItems)
    }

    // MARK: - Header View

    private var headerView: some View {
        HStack {
            // Back Button
            Button(action: viewModel.onBackButtonTapped) {
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color(hex: "#F7F7F7") ?? Color.gray.opacity(0.1))
                    .frame(width: 32, height: 32)
                    .overlay(
                        Image(systemName: "chevron.left")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(Color("DoctorMainColor"))
                    )
            }

            Spacer()

            // Title
            Text("New Prescription")
                .font(.custom("Roboto-Bold", size: 16))
                .foregroundColor(.black)

            Spacer()

            // Invisible spacer to center the title
            Color.clear
                .frame(width: 32, height: 32)
        }
        .padding(.horizontal, 16)
        .padding(.top, 40)
    }

    // MARK: - Patient Form Section

    private var patientFormSection: some View {
        VStack(spacing: 15) {
            // Patient Name
            CustomInputField(
                title: "Patient Name",
                text: $viewModel.patientFormData.name,
                placeholder: "patient name",
                cornerRadius: 15,
                height: 53,
                onTextChanged: viewModel.onNameChanged,
                errorMessage: viewModel.formErrors.nameError
            )

            // Phone Number with Search Indicator
            VStack(alignment: .leading, spacing: 8) {
                PhoneInputField(
                    title: "Phone Number",
                    text: $viewModel.patientFormData.phone,
                    countryCode: $viewModel.patientFormData.countryCode,
                    placeholder: "phone number",
                    errorMessage: viewModel.formErrors.phoneError,
                    onTextChanged: viewModel.onPhoneNumberChanged
                )

                // Patient Search Loading Indicator
                if viewModel.isSearchingPatient {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Searching for patient...")
                            .font(.custom("Roboto-Regular", size: 12))
                            .foregroundColor(.gray)
                    }
                    .padding(.leading, 20)
                }
            }

            // Civil ID
            CustomInputField(
                title: "Civil ID",
                text: $viewModel.patientFormData.civilId,
                placeholder: "civil id",
                cornerRadius: 15,
                height: 53,
                onTextChanged: viewModel.onCivilIdChanged,
                errorMessage: viewModel.formErrors.civilIdError
            )

            // Email
            CustomInputField(
                title: "Email",
                text: $viewModel.patientFormData.email,
                placeholder: "email",
                keyboardType: .emailAddress,
                cornerRadius: 15,
                height: 53,
                onTextChanged: viewModel.onEmailChanged,
                errorMessage: viewModel.formErrors.emailError
            )

            // Date of Birth
            DatePickerField(
                title: "Date",
                selectedDate: $viewModel.selectedDate,
                placeholder: "choose date",
                errorMessage: viewModel.formErrors.dobError
            )
            .onChange(of: viewModel.selectedDate, viewModel.onDateChanged)

            // Create/Update Patient Buttons (conditionally shown)
//            if viewModel.selectedPatientId != nil {
            patientActionButtons
//            }
        }
    }

    // MARK: - Patient Action Buttons

    private var patientActionButtons: some View {
        HStack(spacing: 12) {
            // Create Patient Button
            Button(action: viewModel.createPatient) {
                Text(viewModel.selectedPatientId != nil ? "Update" : "Create Patient")
                    .font(.custom("Roboto-Regular", size: 15))
                    .foregroundColor(.white)
                    .frame(width: 118, height: 37)
                    .background(Color("AdminMainColor"))
                    .cornerRadius(10)
            }
            .disabled(viewModel.isCreatingPatient)

            // Update Button
//            Button(action: viewModel.createPatient) {
//                Text("Update")
//                    .font(.custom("Roboto-Regular", size: 15))
//                    .foregroundColor(.white)
//                    .frame(width: 118, height: 37)
//                    .background(Color("AdminMainColor"))
//                    .cornerRadius(10)
//            }
//            .disabled(viewModel.isCreatingPatient)
        }
        .padding(.top, 15)
    }

    // MARK: - Medications Section

    private var medicationsSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Section Title
            Text("Medications")
                .font(.custom("Roboto-Medium", size: 16))
                .foregroundColor(.black)

            // Add Medication Button
            Button(action: {
                viewModel.navigateToAddMedication()
            }) {
                HStack(spacing: 10) {
                    Image("icon_brown_plus")
                        .resizable()
                        .frame(width: 18, height: 18)

                    Text("Add Medication")
                        .font(.custom("Roboto-Bold", size: 16))
                        .foregroundColor(Color("DoctorMainColor"))
                }
                .frame(maxWidth: .infinity)
                .frame(height: 38)
                .background(Color(hex: "#F7EBF0") ?? Color.pink.opacity(0.1))
                .cornerRadius(12)
            }

            // Medications List
            if viewModel.cartItems.isEmpty {
//                Text("No Data")
//                    .font(.custom("Roboto-Medium", size: 14))
//                    .foregroundColor(.black)
//                    .frame(maxWidth: .infinity)
//                    .padding(.top, 25)
            } else {
                LazyVStack(spacing: 15) {
                    ForEach(viewModel.cartItems) {
                        MedicationItemCard(item: $0, onDelete: viewModel.onDeleteCartItem)
                    }
                }
                .padding(.top, 15)
            }
        }
        .padding(.top)
    }

    // MARK: - Update Patient Information

    private var UpdatePatientInformationButton: some View {
        Button(action: viewModel.updatePatientInformation) {
            HStack {
                if viewModel.isCreatingPatient {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Text("Update Patient")
                        .font(.custom("Roboto-Medium", size: 16))
                        .foregroundColor(.white)
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 38)
            .background(viewModel.selectedPatientId == nil ? Color.gray : Color("DoctorMainColor"))
            .cornerRadius(12)
        }
        .disabled(viewModel.isCreatingPatient)
        .padding(.top, 30)
    }

    // MARK: - Create Prescription Button

    private var createPrescriptionButton: some View {
        Button(action: {
            viewModel.createPrescription()
        }) {
            HStack {
                if viewModel.isSubmittingPrescription {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Text("Create Prescription")
                        .font(.custom("Roboto-Medium", size: 16))
                        .foregroundColor(.white)
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 38)
            .background(Color("DoctorMainColor"))
            .cornerRadius(12)
        }
        .disabled(viewModel.isSubmittingPrescription)
        .padding(.top, 30)
    }
}

#Preview {
    NavigationStack {
        CenterView()
            .attachAllEnvironmentObjects()
    }
}
