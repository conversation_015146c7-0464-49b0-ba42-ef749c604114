//
//  NewPrescriptionModel.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import Foundation

// MARK: - Patient Creation Models

struct CreatePatientRequest: Codable {
    let phone: String
    let civilId: String
    let name: String
    let email: String
    let altMobileNo: String
    let dob: String
    let id: String

    enum CodingKeys: String, CodingKey {
        case phone, name, email, dob, id
        case civilId
        case altMobileNo
    }
}

// MARK: - Prescription Submission Models

// MARK: - SubmitRXResponse

struct SubmitRXResponse: Codable {
    let qrCode, id: String
    let patientInfo: [Info]
    let logo: String?
    let doctorInfo: [Info]
    let prescriptionDetails: [PrescriptionDetail]

    // MARK: - Info

    struct Info: Codable {
        let phone, civilID: String
        let alternateNumber: String?
        let id: Int
        let dob: String
        let profilePic: String
        let email, type, name: String

        enum CodingKeys: String, CodingKey {
            case phone
            case civilID = "civilId"
            case alternateNumber, id, dob, profilePic, email, type, name
        }
    }

    // MARK: - PrescriptionDetail

    struct PrescriptionDetail: Codable {
        let id, productID: Int
        let description: String
        let quantity: Int
        let dose, actualPrice, courseDay, productName: String
        let variation, doseTime: String
        let price: Double
        let currencySymbol, doseday, courseDuration: String
        let productThumbnailImage: String?
        let unitPrice: String

        enum CodingKeys: String, CodingKey {
            case id
            case productID = "productId"
            case description, quantity, dose, actualPrice
            case courseDay = "course_day"
            case productName, variation
            case doseTime = "dose_time"
            case price, currencySymbol, doseday
            case courseDuration = "course_duration"
            case productThumbnailImage, unitPrice
        }
    }
}

// MARK: - Medication Cart Models

struct AddCartRXRequest: Codable {
    let productId: String
    let quantity: String
    let patientId: String
    let doseDay: String
    let dose: String
    let doseTime: String
    let description: String
    let courseDay: String
    let courseDuration: String

    enum CodingKeys: String, CodingKey {
        case productId, quantity, patientId, description
        case doseDay = "dose_day"
        case dose
        case doseTime = "dose_time"
        case courseDay = "course_day"
        case courseDuration = "course_duration"
    }
}

// MARK: - Cart Management Models

struct CartRemoveRequest: Codable {
    let id: String
}

struct UpdateRXCartRequest: Codable {
    let id: String
    let quantity: String
    let doseDay: String
    let dose: String
    let doseTime: String
    let description: String
    let courseDay: String
    let courseDuration: String

    enum CodingKeys: String, CodingKey {
        case id, quantity, description
        case doseDay = "dose_day"
        case dose
        case doseTime = "dose_time"
        case courseDay = "course_day"
        case courseDuration = "course_duration"
    }
}

// MARK: - CartRXModel

struct CartRXModel: Codable {
    let cartItems: [CartItem]
    let patientInfo: [PatientInfo]

    // MARK: - CartItem

    struct CartItem: Codable, Identifiable {
        let id, productID: Int
        let productName: String
        let productThumbnailImage: String?
        let variation: String
        let price: Double
        let unitPrice, actualPrice, currencySymbol: String
        let doseday, dose, doseTime: String?
        let courseDay, courseDuration: String?
        let description: String?
        let quantity: Int

        enum CodingKeys: String, CodingKey {
            case id
            case productID = "productId"
            case productName, productThumbnailImage, variation, price, unitPrice, actualPrice, currencySymbol, doseday, dose
            case doseTime = "dose_time"
            case courseDay = "course_day"
            case courseDuration = "course_duration"
            case description, quantity
        }

        static var DummyData: Self {
            CartItem(
                id: 2975,
                productID: 6792,
                productName: "Roshosh KW Joy 250 ml",
                productThumbnailImage: nil,
                variation: "",
                price: 2.75,
                unitPrice: "KD 2.75",
                actualPrice: "KD 2.75",
                currencySymbol: "KD",
                doseday: "Daily",
                dose: "1",
                doseTime: "before meal",
                courseDay: "day",
                courseDuration: "3",
                description: "test",
                quantity: 1
            )
        }
    }

    // MARK: - PatientInfo

    struct PatientInfo: Codable {
        let id: Int
        let name, phone: String
        let email, altPhone, civilID, dob: String?
        let address: [Address]?

        enum CodingKeys: String, CodingKey {
            case id, name, phone, email
            case altPhone = "alt_phone"
            case civilID = "civil_id"
            case dob, address
        }
    }
}

// MARK: - Address

struct Address: Codable {
    let id: Int
    let addressTitle, firstName, lastName: String
    let email: String?
    let governorateID: Int
    let governorateName: String
    let areaID: Int
    let areaName, block, phone: String
    let setDefault: Int
    let street, building: String
    let floor, appartment, alternatePhone: String?

    enum CodingKeys: String, CodingKey {
        case id, addressTitle, firstName, lastName, email
        case governorateID = "governorateId"
        case governorateName
        case areaID = "areaId"
        case areaName, block, phone, setDefault, street, building, floor, appartment, alternatePhone
    }
}

// MARK: - Patient Search Models

struct CheckPatientRequest: Codable {
    let keyword: String
}

struct PatientSearchResponse: Codable {
    let patients: [PatientSearchInfo]
    let totalCount: Int

    enum CodingKeys: String, CodingKey {
        case patients
        case totalCount = "total_count"
    }
}

// MARK: - PatientSearchInfo

struct PatientSearchInfo: Codable {
    let phone: String
    let civilID, alternateNumber, dob: String?
    let id: Int
    let profilePic, email, type, name: String?

    enum CodingKeys: String, CodingKey {
        case phone
        case civilID = "civilId"
        case alternateNumber, id, dob, profilePic, email, type, name
    }
}

// MARK: - Form Validation Models

struct PatientFormData {
    var name: String = ""
    var phone: String = ""
    var civilId: String = ""
    var email: String = ""
    var dateOfBirth: String = ""
    var selectedPatientId: Int?
    var countryCode: CountryCode = .kuwait

    var isValid: Bool {
        return !name.isEmpty &&
            !phone.isEmpty &&
            !civilId.isEmpty &&
            !email.isEmpty &&
            !dateOfBirth.isEmpty &&
            isValidEmail(email) &&
            isValidPhone(phone)
    }

    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }

    private func isValidPhone(_ phone: String) -> Bool {
        return phone.count >= 8 && phone.allSatisfy { $0.isNumber }
    }
}

// MARK: - Form Error States

struct FormValidationErrors {
    var nameError: String?
    var phoneError: String?
    var civilIdError: String?
    var emailError: String?
    var dobError: String?

    var hasErrors: Bool {
        return nameError != nil || phoneError != nil || civilIdError != nil || emailError != nil || dobError != nil
    }

    mutating func clearAll() {
        nameError = nil
        phoneError = nil
        civilIdError = nil
        emailError = nil
        dobError = nil
    }
}
