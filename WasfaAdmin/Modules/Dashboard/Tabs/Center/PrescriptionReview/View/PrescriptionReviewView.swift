//
//  PrescriptionReviewView.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI

struct PrescriptionReviewView: View {
    @StateObject private var viewModel = PrescriptionReviewViewModel()
   
    var body: some View {
        SuperView(viewModel: viewModel) {
            MainScrollBody(backButtonWithTitle: "Review Prescription") {
                VStack(spacing: 20) {
                    // MARK: - Medications List

                    medicationsSection
                    
                    // MARK: - Patient Information

                    patientInfoSection
                    
                    // MARK: - Address Information

                    patientAddressInfoSection
                    
                    // MARK: - Submit Button

                   
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, 16)
                .padding(.top, 40)
            }
            .safeAreaInset(edge: .bottom, content: {
                submitButton
                    .padding(.bottom, 38)
                    .padding(.horizontal, 16)
            })
            .navigationTitle("Prescription Review")
            .navigationBarTitleDisplayMode(.large)
            .scrollIndicators(.hidden)
        }
        .onLoad(perform: viewModel.loadPrescriptionData)
    }
    
    // MARK: - Patient Information Section
    
    private var patientInfoSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Patient Information")
                .font(.custom("Roboto-Medium", size: 16))
                .foregroundColor(.black)
            
            VStack(alignment: .leading, spacing: 10) {
                patientInfoRow(title: "Name", value: viewModel.patientInfo?.name ?? "N/A")
                patientInfoRow(title: "Phone", value: viewModel.patientInfo?.phone ?? "N/A")
                patientInfoRow(title: "Civil ID", value: viewModel.patientInfo?.civilID ?? "N/A")
                patientInfoRow(title: "Email", value: viewModel.patientInfo?.email ?? "N/A")
                patientInfoRow(title: "Date of Birth", value: viewModel.patientInfo?.dob ?? "N/A")
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Patient Address Information Section
    
    private var patientAddressInfoSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Address Information")
                .font(.custom("Roboto-Medium", size: 16))
                .foregroundColor(.black)
            
            VStack(alignment: .leading, spacing: 10) {
                
                if let address = viewModel.patientInfo?.address?.first {
                    patientInfoRow(title: "Governorate", value: address.governorateName)
                    patientInfoRow(title: "Area", value: address.areaName )
                    patientInfoRow(title: "Block Number", value: address.block)
                    patientInfoRow(title: "Street", value: address.street)
                    patientInfoRow(title: "Building", value: address.building)
                    
                    patientInfoRow(title: "Floor", value: address.floor ?? ".")
                }
                
              
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
        }
    }
    
    private func patientInfoRow(title: String, value: String) -> some View {
        HStack {
            Text(title + ":")
                .font(.custom("Roboto-Medium", size: 14))
                .foregroundColor(.gray)
                .frame( alignment: .leading)
              
            
            Text(value)
                .font(.custom("Roboto-Regular", size: 14))
                .foregroundColor(.black)
                .frame(maxWidth: .infinity, alignment: .trailing)
            
            Spacer()
        }
    }
    
    // MARK: - Medications Section
    
    private var medicationsSection: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Prescription Details")
                .font(.custom("Roboto-Medium", size: 16))
                .foregroundColor(.black)
            
            if viewModel.medications.isEmpty {
                Text("No medications added")
                    .font(.custom("Roboto-Regular", size: 14))
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity)
                    .padding()
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(viewModel.medications, id: \.id) { medication in
                        MedicationReviewCard(medication: medication)
                    }
                }
            }
        }
    }
    
    // MARK: - Submit Button
    
    private var submitButton: some View {
        HStack {
            Button(action: viewModel.onSave) {
                HStack {
                    if viewModel.isSubmitting {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Text("Save")
                            .font(.custom("Roboto-Medium", size: 16))
                            .foregroundColor(.white)
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 48)
                .background(Color("DoctorMainColor"))
                .cornerRadius(12)
            }
            .disabled(viewModel.isSubmitting || viewModel.medications.isEmpty)
            
            Button(action: viewModel.onSaveAndPrint) {
                HStack {
                    if viewModel.isSubmitting {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Label {
                            Text("Save & Print")
                                .font(.custom("Roboto-Medium", size: 16))
                                .foregroundColor(.white)
                        } icon: {
                            Image(systemName: "printer")
                                .font(.custom("Roboto-Medium", size: 16))
                                .foregroundColor(.white)
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 48)
                .background(Color("DoctorMainColor"))
                .cornerRadius(12)
            }
            .disabled(viewModel.isSubmitting || viewModel.medications.isEmpty)
        }
        
    }
    
}

// MARK: - Medication Review Card

struct MedicationReviewCard: View {
    let medication: CartRXModel.CartItem
  
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                NetworkImageView(path:medication.productThumbnailImage, contentMode: .fill)
                .frame(width: 62, height: 62)
                .clipShape(Circle())
                // add some border
                .overlay(
                    Circle()
                        .stroke(style: StrokeStyle(lineWidth: 2))
                        .foregroundColor(.gray.opacity(0.1)))

                Text(medication.productName)
                    .font(Font.custom("Poppins", size: 18.91).weight(.semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }

            HStack(alignment: .bottom) {
                // Medications Section
                HStack(alignment: .top, spacing: 10) {
                    Image(.tabMedications)
                        .renderingMode(.template)
                        .resizable()
                        .foregroundStyle(.adminMain)
                        .frame(width: 17.relativeFontSize, height: 17.relativeFontSize)
                        .frame(width: 32.relativeFontSize, height: 32.relativeFontSize)
                        .background(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.09))
                        .cornerRadius(10.relativeFontSize)

                    VStack(alignment: .center, spacing: 2) {
                        Text("Dose")
                            .font(Font.custom("Poppins", size: 12))
                            .foregroundColor(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.60))

                        Text("\(medication.dose), \(medication.doseday), \(medication.doseTime)")
                            .font(.custom("Roboto-Bold", size: 12))
                            .foregroundColor(Color("DoctorMainColor"))
                    }
                    Spacer()

                    VStack(alignment: .center, spacing: 2) {
                        Text("Course")
                            .font(Font.custom("Poppins", size: 12))
                            .foregroundColor(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.60))

                        Text("\(medication.courseDay), \(medication.courseDuration)")
                            .font(.custom("Roboto-Bold", size: 12))
                            .foregroundColor(Color("DoctorMainColor"))
                    }

                    Spacer()

                }.frame(maxWidth: .infinity, alignment: .leading)

                Text(medication.unitPrice)
                    .font(.custom("Roboto-Bold", size: 14).bold())
                    .foregroundColor(Color("DoctorMainColor"))
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    NavigationStack {
        PrescriptionReviewView().attachAllEnvironmentObjects()
    }
}
