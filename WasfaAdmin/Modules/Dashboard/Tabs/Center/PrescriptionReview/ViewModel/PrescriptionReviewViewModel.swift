//
//  PrescriptionReviewViewModel.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI

class PrescriptionReviewViewModel: SuperViewModel {
    // MARK: - Published Properties

    @Published var patientInfo: CartRXModel.PatientInfo?
    @Published var medications: [CartRXModel.CartItem] = []
    @Published var isSubmitting: Bool = false

    // MARK: - Private Properties

    private var selectedPatientId: String = ""

    // MARK: - Initialization

    override init() {
        super.init()
    }

    // MARK: - Public Methods

    func loadPrescriptionData() {
        // Load patient and medication data from shared state or passed parameters
        // For now, we'll fetch from the cart API
        Task {
            await fetchCartData()
        }
    }

    func onSave() {
        submitPrescription(shouldGeneratePDF: false)
    }

    func onSaveAndPrint() {
        submitPrescription(shouldGeneratePDF: true)
    }

    private func submitPrescription(shouldGeneratePDF: Bool = false) {
        guard patientInfo != nil else {
            updatePageState(.message(config: AlertConfig(
                title: "Error",
                text: "Patient information is missing. Please go back and add patient details."
            )))
            return
        }

        guard !medications.isEmpty else {
            updatePageState(.message(config: AlertConfig(
                title: "Error",
                text: "No medications selected. Please add medications before submitting."
            )))
            return
        }

        Task {
            await performSubmitPrescription(shouldGeneratePDF: shouldGeneratePDF)
        }
    }

    // MARK: - Private Methods

    private func fetchCartData() async {
        let parameters: [String: Any] = [:]

        await onApiCall(
            { try await self.api.getCartRX(parameters: parameters) },
            onSuccess: { response in
                if let cartData = response.data {
                    self.patientInfo = cartData.patientInfo.first
                    self.medications = cartData.cartItems

                    // Set patient ID - ensure we have a valid patient
                    if let firstPatient = cartData.patientInfo.first {
                        self.selectedPatientId = String(firstPatient.id)
                        Logger.info("✅ Cart data loaded for review. Patient ID: \(self.selectedPatientId), Patient Name: \(firstPatient.name)", tag: "PrescriptionReviewViewModel")
                    } else {
                        self.selectedPatientId = ""
                        Logger.warning("⚠️ No patient information found in cart data", tag: "PrescriptionReviewViewModel")
                    }
                }
            },
            onFailure: { error in
                Logger.error("❌ Failed to load cart data: \(error)", tag: "PrescriptionReviewViewModel")
                self.updatePageState(.message(config: AlertConfig(
                    title: "Error",
                    text: "Failed to load prescription data. Please try again."
                )))
            }
        )
    }

    private func performSubmitPrescription(shouldGeneratePDF: Bool = false) async {
        isSubmitting = true

        // Validate that we have a valid customer ID
        guard !selectedPatientId.isEmpty else {
            updatePageState(.message(config: AlertConfig(
                title: "Error",
                text: "Customer ID is missing. Please ensure patient information is loaded."
            )))
            isSubmitting = false
            return
        }

        let parameters: [String: Any] = [
            "influencerId": "",
            "customerId": selectedPatientId
        ]

        Logger.info("📤 Submitting prescription with parameters: \(parameters)", tag: "PrescriptionReviewViewModel")

        await onApiCall(
            { try await self.api.submitRx(parameters: parameters) },
            onSuccess: { response in
                self.isSubmitting = false
                Logger.info("✅ Prescription submitted successfully", tag: "PrescriptionReviewViewModel")

                if shouldGeneratePDF {
                    // Generate and show PDF for "Save & Print" action
                    Task {
                        await self.generateAndShowPDF(prescriptionData: response.data)
                    }
                } else {
                    // Regular save - just show success message
                    self.showSuccessAndNavigateBack()
                }
            },
            onFailure: { error in
                self.isSubmitting = false
                Logger.error("❌ Failed to submit prescription: \(error)", tag: "PrescriptionReviewViewModel")
            }
        )
    }

    private func clearCart() async {
        let parameters: [String: Any] = [:]

        await onApiCall(
            { try await self.api.emptyCart(parameters: parameters) },
            onSuccess: { _ in
                Logger.info("✅ Cart cleared successfully", tag: "PrescriptionReviewViewModel")
            },
            onFailure: { error in
                Logger.warning("⚠️ Failed to clear cart: \(error)", tag: "PrescriptionReviewViewModel")
            }
        )
    }

    // MARK: - Helper Methods

    private func showSuccessAndNavigateBack() {
        updatePageState(.success(message: "Prescription submitted successfully!", onDone: {
            // Navigate back to dashboard or clear cart
            self.routerManager?.popToRoot(where: .centerRoute)

            // Optionally clear the cart
            Task {
                await self.clearCart()
            }
        }))
    }

    private func generateAndShowPDF(prescriptionData: SubmitRXResponse?) async {
        guard let prescriptionData = prescriptionData else {
            Logger.error("❌ No prescription data available for PDF generation", tag: "PrescriptionReviewViewModel")
            showSuccessAndNavigateBack()
            return
        }

        Logger.info("📄 Generating PDF for prescription: \(prescriptionData.id)", tag: "PrescriptionReviewViewModel")

        PrescriptionPDFGenerator.generatePDF(prescriptionData: prescriptionData) { [weak self] pdfURL in
            guard let self = self else { return }

            if let pdfURL = pdfURL {
                // Verify PDF storage before showing success popup
                if PrescriptionPDFGenerator.verifyPDFStorage(at: pdfURL) {
                    Logger.info("✅ PDF generated and verified successfully", tag: "PrescriptionReviewViewModel")

                    // Show success message with preview option
//                    self.updatePageState(.message(config: .init(
//                        title: "Success!",
//                        text: "Prescription submitted and PDF generated successfully! Do you want to preview it?",
//                        cancelButtonText: "No",
//                        okButtonText: "Yes",
//                        alertType: .choiceAlert,
//                        onCancel: {
//                            // User chose not to preview - complete immediately
//                            self.onCompletePdfGeneration()
//                        },
//                        onOk: {
//                            // User wants to open PDF - open with native document interaction
//                            self.openPDFWithNativeViewer(at: pdfURL)
//                        }
//                    )))
                    
                    // User wants to open PDF - open with native document interaction
                    self.openPDFWithNativeViewer(at: pdfURL)
                } else {
                    Logger.error("❌ PDF generation failed - file verification failed", tag: "PrescriptionReviewViewModel")
                    self.updatePageState(.message(config: AlertConfig(
                        title: "Error",
                        text: "PDF was generated but could not be saved properly. Please try again."
                    )))
                }
            } else {
                Logger.error("❌ Failed to generate PDF", tag: "PrescriptionReviewViewModel")
                self.updatePageState(.message(config: AlertConfig(
                    title: "Error",
                    text: "Failed to generate PDF. Please try again."
                )))
            }
        }
    }

    /// Opens the PDF using iOS native document interaction for full functionality
    private func openPDFWithNativeViewer(at url: URL) {
        // Get the current view controller to present from
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController
        else {
            Logger.error("❌ Could not find root view controller for PDF viewer", tag: "PrescriptionReviewViewModel")
            onCompletePdfGeneration()
            return
        }

        // Find the topmost presented view controller
        var topViewController = rootViewController
        while let presentedViewController = topViewController.presentedViewController {
            topViewController = presentedViewController
        }

        Logger.info("📄 Opening PDF with native document interaction from view controller: \(type(of: topViewController))", tag: "PrescriptionReviewViewModel")

        // Open PDF with document interaction controller (allows printing, sharing, etc.)
        PrescriptionPDFGenerator.openPDFWithDocumentInteraction(
            at: url,
            from: topViewController
        ) { [weak self] in
            // This closure is called when the PDF viewer is dismissed
            Logger.info("📄 PDF document interaction dismissed, completing PDF generation workflow", tag: "PrescriptionReviewViewModel")
            self?.onCompletePdfGeneration()
        }
    }

    func onCompletePdfGeneration() {
        // Navigate back to dashboard or clear cart
        self.appState?.updateSelectedTab(.home)
        routerManager?.popToRoot(where: .centerRoute)

        // Optionally clear the cart
        Task {
            await self.clearCart()
        }
    }
}

// MARK: - Preview Data

// extension PrescriptionReviewViewModel {
//    static func preview() -> PrescriptionReviewViewModel {
//        let viewModel = PrescriptionReviewViewModel()
//
//        // Mock patient data
//        viewModel.patientInfo = CartRXModel.PatientInfo(
//            id: 1,
//            name: "John Doe",
//            phone: "+96512345678",
//            email: "<EMAIL>",
//            altPhone: nil,
//            civilID: "123456789",
//            dob: "01-01-1990",
//            address: []
//        )
//
//        // Mock medication data
//        viewModel.medications = [
//            CartRXModel.CartItem(
//                id: 1,
//                productID: 1,
//                productName: "Paracetamol 500mg",
//                productThumbnailImage: nil,
//                variation: "",
//                price: 5.0,
//                unitPrice: "5.000 KD",
//                actualPrice: "5.000 KD",
//                currencySymbol: "KD",
//                doseday: "twice daily",
//                dose: "1 tablet",
//                doseTime: "morning, evening",
//                courseDay: "days",
//                courseDuration: "7",
//                description: "Take with food",
//                quantity: 2
//            ),
//            CartRXModel.CartItem(
//                id: 2,
//                productID: 2,
//                productName: "Amoxicillin 250mg",
//                productThumbnailImage: nil,
//                variation: "",
//                price: 12.5,
//                unitPrice: "12.500 KD",
//                actualPrice: "12.500 KD",
//                currencySymbol: "KD",
//                doseday: "three times daily",
//                dose: "1 capsule",
//                doseTime: "morning, afternoon, evening",
//                courseDay: "days",
//                courseDuration: "10",
//                description: "Complete the full course",
//                quantity: 1
//            )
//        ]
//
//        return viewModel
//    }
// }
