//
//  PrescriptionPDFGenerator.swift
//  WasfaAdmin
//
//  Created by Apple on 30/06/2025.
//

import Foundation
import PDFKit
import UIKit
import SwiftUI
import CoreImage
import WebKit
import SVGKit

class PrescriptionPDFGenerator {
    
    // MARK: - PDF Generation
    
    static func generatePDF(
        prescriptionData: SubmitRXResponse,
        completion: @escaping (URL?) -> Void
    ) {
        Task {
            let pdfURL = await createPDF(from: prescriptionData)
            await MainActor.run {
                completion(pdfURL)
            }
        }
    }

    // MARK: - PDF Storage Verification

    /// Verifies that the PDF file exists and is readable at the specified URL
    static func verifyPDFStorage(at url: URL) -> Bool {
        let fileManager = FileManager.default

        // Check if file exists
        guard fileManager.fileExists(atPath: url.path) else {
            Logger.error("❌ PDF file does not exist at path: \(url.path)", tag: "PrescriptionPDFGenerator")
            return false
        }

        // Check if file is readable
        guard fileManager.isReadableFile(atPath: url.path) else {
            Logger.error("❌ PDF file is not readable at path: \(url.path)", tag: "PrescriptionPDFGenerator")
            return false
        }

        // Check file size (should be greater than 0)
        do {
            let attributes = try fileManager.attributesOfItem(atPath: url.path)
            if let fileSize = attributes[.size] as? Int64, fileSize > 0 {
                Logger.info("✅ PDF file verified successfully. Size: \(fileSize) bytes at: \(url.path)", tag: "PrescriptionPDFGenerator")
                return true
            } else {
                Logger.error("❌ PDF file has invalid size at path: \(url.path)", tag: "PrescriptionPDFGenerator")
                return false
            }
        } catch {
            Logger.error("❌ Failed to get PDF file attributes: \(error.localizedDescription)", tag: "PrescriptionPDFGenerator")
            return false
        }
    }
    
    // MARK: - Private Methods
    
    private static func createPDF(from data: SubmitRXResponse) async -> URL? {
        let pageSize = CGSize(width: 595, height: 842) // A4 size in points
        let renderer = UIGraphicsPDFRenderer(bounds: CGRect(origin: .zero, size: pageSize))
        
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let pdfURL = documentsPath.appendingPathComponent("prescription_\(data.id).pdf")
        
        do {
            try renderer.writePDF(to: pdfURL) { context in
                context.beginPage()
                
                let cgContext = context.cgContext
                drawPrescriptionContent(on: cgContext, data: data, pageSize: pageSize)
            }
            
            Logger.info("✅ PDF generated successfully at: \(pdfURL.path)", tag: "PrescriptionPDFGenerator")
            return pdfURL
            
        } catch {
            Logger.error("❌ Failed to generate PDF: \(error)", tag: "PrescriptionPDFGenerator")
            return nil
        }
    }
    
    private static func drawPrescriptionContent(on context: CGContext, data: SubmitRXResponse, pageSize: CGSize) {
        let margin: CGFloat = 50
        var currentY: CGFloat = margin
        
        // Title
        let titleFont = UIFont.boldSystemFont(ofSize: 18)
        let title = "RX PRESCRIPTION"
        let titleSize = title.size(withAttributes: [.font: titleFont])
        let titleX = (pageSize.width - titleSize.width) / 2
        
        title.draw(at: CGPoint(x: titleX, y: currentY), withAttributes: [
            .font: titleFont,
            .foregroundColor: UIColor.black
        ])
        currentY += titleSize.height + 30
        
        // Patient Information
        if let patient = data.patientInfo.first {
            currentY = drawPatientInfo(context: context, patient: patient, startY: currentY, margin: margin)
        }
        
        currentY += 20
        
        // Prescription Details Header
        let headerFont = UIFont.boldSystemFont(ofSize: 14)
        "Prescription Details".draw(at: CGPoint(x: margin, y: currentY), withAttributes: [
            .font: headerFont,
            .foregroundColor: UIColor.black
        ])
        currentY += 30
        
        // Medications Table
        currentY = drawMedicationsTable(context: context, medications: data.prescriptionDetails, startY: currentY, margin: margin, pageWidth: pageSize.width)
        
        currentY += 40
        
        // Doctor Information
        if let doctor = data.doctorInfo.first {
            currentY = drawDoctorInfo(context: context, doctor: doctor, startY: currentY, margin: margin)
        }
        
        // QR Code (if available)
        if !data.qrCode.isEmpty {
            Logger.info("📄 QR Code data available: \(data.qrCode.prefix(100))...", tag: "PrescriptionPDFGenerator")
            drawQRCode(context: context, qrCode: data.qrCode, startY: currentY + 20, margin: margin)
        } else {
            Logger.warning("⚠️ QR Code data is empty, generating fallback QR code", tag: "PrescriptionPDFGenerator")
            // Generate fallback QR code with prescription information
            let fallbackQRData = "Prescription ID: \(data.id)"
            drawQRCode(context: context, qrCode: fallbackQRData, startY: currentY + 20, margin: margin)
        }
    }
    
    private static func drawPatientInfo(context: CGContext, patient: SubmitRXResponse.Info, startY: CGFloat, margin: CGFloat) -> CGFloat {
        let font = UIFont.systemFont(ofSize: 12)
        let boldFont = UIFont.boldSystemFont(ofSize: 12)
        let currentY = startY

        // Patient Name on the left
        "Patient Name: \(patient.name)".draw(at: CGPoint(x: margin, y: currentY), withAttributes: [
            .font: font,
            .foregroundColor: UIColor.black
        ])

        // Age and Civil ID on the right side (matching Android layout)
        let ageText = "Age: \(patient.dob)"
        let civilText = "Civil ID: \(patient.civilID)"

        // Calculate positions for proper alignment
        let ageSize = ageText.size(withAttributes: [.font: font])
        let civilSize = civilText.size(withAttributes: [.font: font])

        // Position Age in the center-right area
        let ageX: CGFloat = 250
        ageText.draw(at: CGPoint(x: ageX, y: currentY), withAttributes: [
            .font: font,
            .foregroundColor: UIColor.black
        ])

        // Position Civil ID at the far right
        let civilX: CGFloat = 400
        civilText.draw(at: CGPoint(x: civilX, y: currentY), withAttributes: [
            .font: font,
            .foregroundColor: UIColor.black
        ])

        return currentY + 25
    }
    
    private static func drawMedicationsTable(context: CGContext, medications: [SubmitRXResponse.PrescriptionDetail], startY: CGFloat, margin: CGFloat, pageWidth: CGFloat) -> CGFloat {
        let font = UIFont.systemFont(ofSize: 10)
        let boldFont = UIFont.boldSystemFont(ofSize: 10)
        var currentY = startY

        // Table headers (matching Android layout exactly)
        let headers = ["S.No", "Product Name", "QTY", "Dose", "Time", "Course", "Additional Notes"]
        let columnWidths: [CGFloat] = [30, 160, 40, 40, 45, 55, 145]
        let tableWidth = columnWidths.reduce(0, +)
        var currentX = margin

        // Draw header row background (gray like Android)
        context.setFillColor(UIColor.systemGray4.cgColor)
        context.fill(CGRect(x: margin, y: currentY, width: tableWidth, height: 25))

        // Draw header borders
        context.setStrokeColor(UIColor.black.cgColor)
        context.setLineWidth(0.5)

        // Draw header text and vertical lines
        for i in 0..<headers.count {
            // Draw header text
            headers[i].draw(at: CGPoint(x: currentX + 5, y: currentY + 5), withAttributes: [
                .font: boldFont,
                .foregroundColor: UIColor.black
            ])

            // Draw vertical line after each column (except last)
            if i < headers.count - 1 {
                let lineX = currentX + columnWidths[i]
                context.move(to: CGPoint(x: lineX, y: currentY))
                context.addLine(to: CGPoint(x: lineX, y: currentY + 25))
                context.strokePath()
            }

            currentX += columnWidths[i]
        }

        // Draw horizontal line under header
        context.move(to: CGPoint(x: margin, y: currentY + 25))
        context.addLine(to: CGPoint(x: margin + tableWidth, y: currentY + 25))
        context.strokePath()

        currentY += 25
        
        // Draw medication rows with borders (matching Android)
        for (index, medication) in medications.enumerated() {
            currentX = margin
            let rowHeight: CGFloat = 30

            // Draw row background (alternating colors like Android)
            if index % 2 == 0 {
                context.setFillColor(UIColor.systemGray6.cgColor)
                context.fill(CGRect(x: margin, y: currentY, width: tableWidth, height: rowHeight))
            }

            let rowData = [
                "\(index + 1)",
                medication.productName,
                "\(medication.quantity)",
                medication.dose,
                medication.doseTime,
                medication.courseDuration,
                medication.description
            ]

            // Draw cell content and vertical borders
            for i in 0..<rowData.count {
                let text = rowData[i]
                let rect = CGRect(x: currentX + 5, y: currentY + 5, width: columnWidths[i] - 10, height: 20)

                text.draw(in: rect, withAttributes: [
                    .font: font,
                    .foregroundColor: UIColor.black
                ])

                // Draw vertical line after each column (except last)
                if i < rowData.count - 1 {
                    let lineX = currentX + columnWidths[i]
                    context.move(to: CGPoint(x: lineX, y: currentY))
                    context.addLine(to: CGPoint(x: lineX, y: currentY + rowHeight))
                    context.strokePath()
                }

                currentX += columnWidths[i]
            }

            // Draw horizontal line under each row
            context.move(to: CGPoint(x: margin, y: currentY + rowHeight))
            context.addLine(to: CGPoint(x: margin + tableWidth, y: currentY + rowHeight))
            context.strokePath()

            currentY += rowHeight
        }

        // Draw table border
        context.setStrokeColor(UIColor.black.cgColor)
        context.setLineWidth(1.0)
        let tableRect = CGRect(x: margin, y: startY, width: tableWidth, height: currentY - startY)
        context.stroke(tableRect)
        
        return currentY
    }
    
    private static func drawDoctorInfo(context: CGContext, doctor: SubmitRXResponse.Info, startY: CGFloat, margin: CGFloat) -> CGFloat {
        let font = UIFont.systemFont(ofSize: 12)
        let boldFont = UIFont.boldSystemFont(ofSize: 12)
        var currentY = startY

        "Prescribed by: \(doctor.name)".draw(at: CGPoint(x: margin, y: currentY), withAttributes: [
            .font: boldFont,
            .foregroundColor: UIColor.black
        ])
        currentY += 20

        "RX Id: \(doctor.id)".draw(at: CGPoint(x: margin, y: currentY), withAttributes: [
            .font: boldFont,
            .foregroundColor: UIColor.black
        ])
        currentY += 20

        doctor.type.draw(at: CGPoint(x: margin, y: currentY), withAttributes: [
            .font: font,
            .foregroundColor: UIColor.black
        ])

        return currentY + 25
    }
    
    private static func drawQRCode(context: CGContext, qrCode: String, startY: CGFloat, margin: CGFloat) {
        Logger.info("📄 Drawing QR Code - Data length: \(qrCode.count)", tag: "PrescriptionPDFGenerator")
        Logger.info("📄 QR Code content preview: \(qrCode.prefix(200))", tag: "PrescriptionPDFGenerator")

        // Generate QR code from SVG string or fallback to text-based QR code
        let qrCodeImage = generateQRCodeImage(from: qrCode, size: CGSize(width: 80, height: 80))

        // Draw "Scan here to receive the Prescription" text
        let font = UIFont.systemFont(ofSize: 10)
        "Scan here to receive the Prescription".draw(at: CGPoint(x: 300, y: startY), withAttributes: [
            .font: font,
            .foregroundColor: UIColor.black
        ])

        // Draw QR code image if generated successfully
        if let qrImage = qrCodeImage {
            Logger.info("✅ QR Code image generated successfully", tag: "PrescriptionPDFGenerator")
            let qrRect = CGRect(x: 300, y: startY + 20, width: 80, height: 80)
            qrImage.draw(in: qrRect)
        } else {
            Logger.warning("⚠️ QR Code generation failed, showing placeholder", tag: "PrescriptionPDFGenerator")
            // Fallback: draw a placeholder rectangle
            context.setStrokeColor(UIColor.black.cgColor)
            context.setLineWidth(1.0)
            let placeholderRect = CGRect(x: 300, y: startY + 20, width: 80, height: 80)
            context.stroke(placeholderRect)

            "QR Code".draw(at: CGPoint(x: 320, y: startY + 55), withAttributes: [
                .font: UIFont.systemFont(ofSize: 8),
                .foregroundColor: UIColor.black
            ])
        }

        // Add footer text (matching Android)
        let footerFont = UIFont.systemFont(ofSize: 12)
        let footerText = "Apix Medical"
        let footerSize = footerText.size(withAttributes: [.font: footerFont])
        let footerX = 595 - 20 - footerSize.width // Right aligned like Android

        footerText.draw(at: CGPoint(x: footerX, y: 842 - 40), withAttributes: [
            .font: footerFont,
            .foregroundColor: UIColor.blue
        ])
    }
    
    // MARK: - PDF Viewing

    /// Opens PDF using external app (legacy method)
    static func openPDF(at url: URL) {
        DispatchQueue.main.async {
            if UIApplication.shared.canOpenURL(url) {
                UIApplication.shared.open(url)
            } else {
                Logger.error("❌ Cannot open PDF at: \(url.path)", tag: "PrescriptionPDFGenerator")
            }
        }
    }

    /// Opens PDF using iOS native document interaction with full functionality (print, share, etc.)
    static func openPDFWithDocumentInteraction(
        at url: URL,
        from viewController: UIViewController,
        onDismiss: @escaping () -> Void
    ) {
        // Verify PDF exists before attempting to open
        guard verifyPDFStorage(at: url) else {
            Logger.error("❌ Cannot open PDF - file verification failed", tag: "PrescriptionPDFGenerator")
            onDismiss()
            return
        }

        Logger.info("📄 Opening PDF with document interaction for: \(url.path)", tag: "PrescriptionPDFGenerator")

        DispatchQueue.main.async {
            let documentController = UIDocumentInteractionController(url: url)
            let delegate = DocumentInteractionDelegate(onDismiss: onDismiss)

            documentController.delegate = delegate
            documentController.name = "Prescription_\(Date().timeIntervalSince1970).pdf"

            // Store the delegate to prevent deallocation
            objc_setAssociatedObject(
                documentController,
                AssociatedKeys.delegate,
                delegate,
                .OBJC_ASSOCIATION_RETAIN_NONATOMIC
            )

            // Present the document interaction controller
            if documentController.presentPreview(animated: true) {
                Logger.info("✅ PDF document interaction presented successfully", tag: "PrescriptionPDFGenerator")
            } else {
                Logger.error("❌ Failed to present PDF document interaction", tag: "PrescriptionPDFGenerator")
                // Fallback to opening with external app
                openPDF(at: url)
                onDismiss()
            }
        }
    }

    /// Alternative method: Open PDF with UIActivityViewController for sharing/printing
    static func openPDFWithActivityController(
        at url: URL,
        from viewController: UIViewController,
        onDismiss: @escaping () -> Void
    ) {
        // Verify PDF exists before attempting to open
        guard verifyPDFStorage(at: url) else {
            Logger.error("❌ Cannot open PDF - file verification failed", tag: "PrescriptionPDFGenerator")
            onDismiss()
            return
        }

        Logger.info("📄 Opening PDF with activity controller for: \(url.path)", tag: "PrescriptionPDFGenerator")

        DispatchQueue.main.async {
            let activityController = UIActivityViewController(
                activityItems: [url],
                applicationActivities: nil
            )

            // Configure for iPad
            if let popover = activityController.popoverPresentationController {
                popover.sourceView = viewController.view
                popover.sourceRect = CGRect(x: viewController.view.bounds.midX, y: viewController.view.bounds.midY, width: 0, height: 0)
                popover.permittedArrowDirections = []
            }

            // Set completion handler
            activityController.completionWithItemsHandler = { _, _, _, _ in
                Logger.info("📄 PDF activity controller dismissed", tag: "PrescriptionPDFGenerator")
                onDismiss()
            }

            viewController.present(activityController, animated: true)
        }
    }

    // MARK: - QR Code Generation

    /// Generates a QR code image from SVG string or plain text
    private static func generateQRCodeImage(from qrCodeData: String, size: CGSize) -> UIImage? {
        Logger.info("🔍 Processing QR code data - Is SVG: \(qrCodeData.contains("<svg"))", tag: "PrescriptionPDFGenerator")

        // If it's an SVG string, try to render it directly first
        if qrCodeData.contains("<svg") || qrCodeData.contains("<?xml") {
            Logger.info("📄 Attempting to render SVG QR code", tag: "PrescriptionPDFGenerator")
            if let svgImage = renderSVGToImage(svgString: qrCodeData, size: size) {
                return svgImage
            }

            // If SVG rendering fails, try to extract meaningful content and generate QR code
            Logger.info("📄 SVG rendering failed, extracting content for QR generation", tag: "PrescriptionPDFGenerator")
            let extractedContent = extractQRContentFromSVG(qrCodeData) ?? "Prescription QR Code"
            return generateQRCode(from: extractedContent, size: size)
        } else {
            // If it's plain text, generate QR code using CoreImage
            Logger.info("📄 Generating QR code from text content", tag: "PrescriptionPDFGenerator")
            return generateQRCode(from: qrCodeData, size: size)
        }
    }

    /// Extracts QR code content from SVG string (if applicable)
    private static func extractQRContentFromSVG(_ svgString: String) -> String? {
        // If it's already a simple string (not SVG), return as is
        if !svgString.contains("<svg") && !svgString.contains("<?xml") {
            return svgString
        }

        // Try to extract meaningful content from SVG for QR generation
        // Look for common patterns like URLs, prescription IDs, etc.

        // Check for href attributes that might contain URLs
        if let url = extractURLFromSVG(svgString) {
            Logger.info("📄 Extracted URL from SVG: \(url)", tag: "PrescriptionPDFGenerator")
            return url
        }

        // Check for text content within the SVG
        if let textContent = extractTextFromSVG(svgString) {
            Logger.info("📄 Extracted text from SVG: \(textContent)", tag: "PrescriptionPDFGenerator")
            return textContent
        }

        // As a last resort, create a QR code with a prescription identifier
        Logger.info("📄 Using fallback prescription identifier for QR code", tag: "PrescriptionPDFGenerator")
        return "Prescription QR Code - \(Date().timeIntervalSince1970)"
    }

    /// Extracts URL from SVG content
    private static func extractURLFromSVG(_ svgString: String) -> String? {
        // Look for href attributes
        let hrefPattern = "href=[\"']([^\"']+)[\"']"
        let regex = try? NSRegularExpression(pattern: hrefPattern, options: [])
        let range = NSRange(location: 0, length: svgString.count)

        if let match = regex?.firstMatch(in: svgString, options: [], range: range),
           let urlRange = Range(match.range(at: 1), in: svgString) {
            return String(svgString[urlRange])
        }

        return nil
    }

    /// Extracts text content from SVG
    private static func extractTextFromSVG(_ svgString: String) -> String? {
        // Look for text elements
        let textPattern = "<text[^>]*>([^<]+)</text>"
        let regex = try? NSRegularExpression(pattern: textPattern, options: [])
        let range = NSRange(location: 0, length: svgString.count)

        if let match = regex?.firstMatch(in: svgString, options: [], range: range),
           let textRange = Range(match.range(at: 1), in: svgString) {
            return String(svgString[textRange])
        }

        return nil
    }

    /// Generates QR code image using CoreImage
    private static func generateQRCode(from string: String, size: CGSize) -> UIImage? {
        let data = string.data(using: String.Encoding.ascii)

        guard let filter = CIFilter(name: "CIQRCodeGenerator") else {
            Logger.error("❌ Failed to create QR code filter", tag: "PrescriptionPDFGenerator")
            return nil
        }

        filter.setValue(data, forKey: "inputMessage")
        filter.setValue("M", forKey: "inputCorrectionLevel")

        guard let outputImage = filter.outputImage else {
            Logger.error("❌ Failed to generate QR code output image", tag: "PrescriptionPDFGenerator")
            return nil
        }

        // Scale the QR code to the desired size
        let scaleX = size.width / outputImage.extent.width
        let scaleY = size.height / outputImage.extent.height
        let transformedImage = outputImage.transformed(by: CGAffineTransform(scaleX: scaleX, y: scaleY))

        let context = CIContext()
        guard let cgImage = context.createCGImage(transformedImage, from: transformedImage.extent) else {
            Logger.error("❌ Failed to create CGImage from QR code", tag: "PrescriptionPDFGenerator")
            return nil
        }

        let qrImage = UIImage(cgImage: cgImage)
        Logger.info("✅ QR code generated successfully with size: \(size)", tag: "PrescriptionPDFGenerator")
        return qrImage
    }

    /// Renders SVG string to UIImage using SVGKit
    private static func renderSVGToImage(svgString: String, size: CGSize) -> UIImage? {
        Logger.info("🎨 Rendering SVG using SVGKit", tag: "PrescriptionPDFGenerator")

        guard let data = svgString.data(using: .utf8) else {
            Logger.error("❌ Failed to convert SVG string to data", tag: "PrescriptionPDFGenerator")
            return nil
        }

        guard let parsedSVG = SVGKImage(data: data) else {
            Logger.error("❌ Failed to parse SVG data with SVGKit", tag: "PrescriptionPDFGenerator")
            return nil
        }

        // Scale the SVG to the required size (80x80 for QR code)
        parsedSVG.size = size

        let renderedImage = parsedSVG.uiImage
        Logger.info("✅ SVG rendered successfully to UIImage", tag: "PrescriptionPDFGenerator")
        return renderedImage
    }

    /// Extracts data URL from SVG content
    private static func extractDataURLFromSVG(_ svgString: String) -> String? {
        // Look for data:image patterns in the SVG
        let dataURLPattern = "data:image/[^;]+;base64,[A-Za-z0-9+/=]+"
        let regex = try? NSRegularExpression(pattern: dataURLPattern, options: [])
        let range = NSRange(location: 0, length: svgString.count)

        if let match = regex?.firstMatch(in: svgString, options: [], range: range) {
            let matchRange = Range(match.range, in: svgString)!
            return String(svgString[matchRange])
        }

        return nil
    }

    /// Creates UIImage from data URL
    private static func imageFromDataURL(_ dataURL: String) -> UIImage? {
        guard dataURL.hasPrefix("data:image/") else { return nil }

        let components = dataURL.components(separatedBy: ",")
        guard components.count == 2,
              let base64String = components.last,
              let imageData = Data(base64Encoded: base64String) else {
            Logger.error("❌ Failed to decode base64 image data", tag: "PrescriptionPDFGenerator")
            return nil
        }

        let image = UIImage(data: imageData)
        Logger.info("✅ Successfully created image from data URL", tag: "PrescriptionPDFGenerator")
        return image
    }

    /// Renders SVG using WebKit (synchronous fallback)
    private static func renderSVGWithWebKit(svgString: String, size: CGSize) -> UIImage? {
        Logger.info("🌐 Attempting WebKit SVG rendering", tag: "PrescriptionPDFGenerator")

        // Create a simple HTML wrapper for the SVG
        let htmlContent = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { margin: 0; padding: 0; }
                svg { width: \(size.width)px; height: \(size.height)px; }
            </style>
        </head>
        <body>
            \(svgString)
        </body>
        </html>
        """

        // For PDF generation, we need a synchronous approach
        // This is a simplified version - in production, you might want to use a proper SVG library
        Logger.warning("⚠️ WebKit SVG rendering not implemented for synchronous PDF generation", tag: "PrescriptionPDFGenerator")
        return nil
    }
}

// MARK: - Associated Object Keys

private enum AssociatedKeys {
    static let delegate = UnsafeRawPointer(bitPattern: "DocumentInteractionDelegate".hashValue)!
}

// MARK: - Document Interaction Delegate

private class DocumentInteractionDelegate: NSObject, UIDocumentInteractionControllerDelegate {
    private let onDismiss: () -> Void

    init(onDismiss: @escaping () -> Void) {
        self.onDismiss = onDismiss
        super.init()
    }

    // MARK: - UIDocumentInteractionControllerDelegate

    func documentInteractionControllerViewControllerForPreview(_ controller: UIDocumentInteractionController) -> UIViewController {
        // Return the root view controller for presentation
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            fatalError("Could not find root view controller")
        }

        // Find the topmost presented view controller
        var topViewController = rootViewController
        while let presentedViewController = topViewController.presentedViewController {
            topViewController = presentedViewController
        }

        return topViewController
    }

    func documentInteractionControllerDidEndPreview(_ controller: UIDocumentInteractionController) {
        Logger.info("📄 PDF document interaction dismissed", tag: "PrescriptionPDFGenerator")
        onDismiss()
    }

    func documentInteractionController(_ controller: UIDocumentInteractionController, didEndSendingToApplication application: String?) {
        Logger.info("📄 PDF sent to application: \(application ?? "unknown")", tag: "PrescriptionPDFGenerator")
        onDismiss()
    }
}
