//
//  MedicationsFilterViewModel.swift
//  WasfaAdmin
//
//  Created by Apple on 30/06/2025.
//

import SwiftUI

class MedicationsFilterViewModel: SuperViewModel {
    // MARK: - Published Properties

    @Published var filterState = FilterState.empty
    @Published var brands: [Brand] = []
    @Published var sellers: [Seller] = []
    @Published var medicalReps: [MedicalRep] = []
    @Published var influencers: [Influencer] = []

    // Separate text field bindings to prevent state conflicts
    @Published var skuSearchText: String = ""
    @Published var productSearchText: String = ""
    
    // Expansion states for filter sections
    @Published var isInfluencerExpanded: Bool = false
    @Published var isBrandExpanded: Bool = false
    @Published var isSellerExpanded: Bool = false
    @Published var isMedicalRepExpanded: Bool = false
    
    // Loading states
    @Published var isLoadingBrands: Bool = false
    @Published var isLoadingSellers: Bool = false
    @Published var isLoadingMedicalReps: Bool = false
    @Published var isLoadingInfluencers: Bool = false
    
    // MARK: - Computed Properties
    
    /// Check if influencer filter should be shown based on user type
    var shouldShowInfluencerFilter: Bool {
        // TODO: Check user type from preferences/app state
        // For now, show for all users except influencers
        return true
    }
    
    /// Get selected brand name for display
    var selectedBrandName: String? {
        guard let selectedBrandId = filterState.selectedBrandId else { return nil }
        return brands.first { $0.id == selectedBrandId }?.name
    }

    /// Get selected seller name for display
    var selectedSellerName: String? {
        guard let selectedSellerId = filterState.selectedSellerId else { return nil }
        return sellers.first { $0.id == selectedSellerId }?.name
    }

    /// Get selected medical rep name for display
    var selectedMedicalRepName: String? {
        guard let selectedMedicalRepId = filterState.selectedMedicalRepId else { return nil }
        return medicalReps.first { $0.id == selectedMedicalRepId }?.name
    }

    /// Get selected influencer name for display
    var selectedInfluencerName: String? {
        guard let selectedInfluencerId = filterState.selectedInfluencerId else { return nil }
        return influencers.first { $0.id == selectedInfluencerId }?.name
    }
    
    override init() {
        super.init()
        Logger.info("🔍 MedicationsFilterViewModel initialized", tag: "MedicationsFilterViewModel")
    }
    
    // MARK: - Public Methods
    
    /// Set initial filter state
    func setInitialFilters(_ filters: FilterState) {
        Logger.info("🔧 Setting initial filters", tag: "MedicationsFilterViewModel")
        filterState = filters

        // Sync separate text fields with filter state
        skuSearchText = filters.skuSearch
        productSearchText = filters.productSearch
    }

    /// Update filter state from text field changes
    func updateSkuSearch(_ text: String) {
        skuSearchText = text
        filterState.skuSearch = text
    }

    func updateProductSearch(_ text: String) {
        productSearchText = text
        filterState.productSearch = text
    }
    
    /// Load all filter data
    func loadFilterData() {
        Logger.info("📊 Loading filter data", tag: "MedicationsFilterViewModel")
        Task {
            await withTaskGroup(of: Void.self) { group in
                group.addTask { await self.loadBrands() }
                group.addTask { await self.loadSellers() }
                group.addTask { await self.loadMedicalReps() }
                group.addTask { await self.loadInfluencers() }
            }
        }
    }
    
    /// Clear all filters
    func clearAllFilters() {
        Logger.info("🧹 Clearing all filters", tag: "MedicationsFilterViewModel")
        withAnimation(.easeInOut(duration: 0.3)) {
            filterState.clearAll()

            // Clear separate text fields
            skuSearchText = ""
            productSearchText = ""

            // Collapse all sections
            isInfluencerExpanded = false
            isBrandExpanded = false
            isSellerExpanded = false
            isMedicalRepExpanded = false
        }
    }
    
    // MARK: - Selection Methods
    
    func selectBrand(_ brand: Brand) {
        Logger.info("🏷️ Brand selected: \(brand.name)", tag: "MedicationsFilterViewModel")
        filterState.selectedBrandId = brand.id
       
        isBrandExpanded = false
    }
    
    func selectSeller(_ seller: Seller) {
        Logger.info("🏪 Seller selected: \(seller.name)", tag: "MedicationsFilterViewModel")
        filterState.selectedSellerId = seller.id
        isSellerExpanded = false
    }
    
    func selectMedicalRep(_ medicalRep: MedicalRep) {
        Logger.info("👨‍⚕️ Medical Rep selected: \(medicalRep.name)", tag: "MedicationsFilterViewModel")
        filterState.selectedMedicalRepId = medicalRep.id
       
        isMedicalRepExpanded = false
    }
    
    func selectInfluencer(_ influencer: Influencer) {
        Logger.info("🌟 Influencer selected: \(influencer.name)", tag: "MedicationsFilterViewModel")
        filterState.selectedInfluencerId = influencer.id
       
        isInfluencerExpanded = false
    }
    
    // MARK: - Private API Methods
    
    private func loadBrands() async {
        await onApiCall(
            { try await self.api.brandsList(parameters: [:]) },
            withLoadingIndicator: false,
            customLoadingBinding: binding(\.isLoadingBrands),
            
            onSuccess: { response in
                self.brands = response.data ?? []
                Logger.info("✅ Brands loaded: \(self.brands.count) items", tag: "MedicationsFilterViewModel")
            },
            onFailure: { error in
                // Revert the optimistic update on failure
                Logger.error("❌ Failed to load brands: \(error)", tag: "MedicationsFilterViewModel")
            }
        )
    }
    
    private func loadSellers() async {
        await onApiCall(
            { try await self.api.sellersList(parameters: [:]) },
            withLoadingIndicator: false,
            customLoadingBinding: binding(\.isLoadingSellers),
            onSuccess: { response in
                self.sellers = response.data ?? []
                Logger.info("✅ Sellers loaded: \(self.sellers.count) items", tag: "MedicationsFilterViewModel")
            },
            onFailure: { error in
                // Revert the optimistic update on failure
                Logger.error("❌ Failed to load Sellers: \(error)", tag: "MedicationsFilterViewModel")
            }
        )
    }
    
    private func loadMedicalReps() async {
        await onApiCall(
            { try await self.api.medicalRepsList(parameters: [:]) },
            withLoadingIndicator: false,
            customLoadingBinding: binding(\.isLoadingMedicalReps),
            onSuccess: { response in
                self.medicalReps = response.data ?? []
                Logger.info("✅ Medical Reps loaded: \(self.medicalReps.count) items", tag: "MedicationsFilterViewModel")
            },
            onFailure: { error in
                // Revert the optimistic update on failure
                Logger.error("❌ Failed to load Medical Reps: \(error)", tag: "MedicationsFilterViewModel")
            }
        )
    }
    
    private func loadInfluencers() async {
        await onApiCall(
            { try await self.api.influencersList(parameters: [:]) },
            withLoadingIndicator: false,
            customLoadingBinding: binding(\.isLoadingInfluencers),
            onSuccess: { response in
                self.influencers = response.data ?? []
                Logger.info("✅ Influencers loaded: \(self.influencers.count) items", tag: "MedicationsFilterViewModel")
            },
            onFailure: { error in
                // Revert the optimistic update on failure
                Logger.error("❌ Failed to load Influencers: \(error)", tag: "MedicationsFilterViewModel")
            }
        )
    }
}
