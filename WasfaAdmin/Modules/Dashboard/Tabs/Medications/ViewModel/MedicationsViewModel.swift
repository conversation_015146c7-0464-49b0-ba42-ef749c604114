//
//  MedicationsViewModel.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

class MedicationsViewModel: SuperViewModel {
    // MARK: - Published Properties

    @Published var products: [Product] = []
    @Published var isLoading: Bool = false
    @Published var isLoadingMore: Bool = false
    @Published var searchKeyword: String = ""
    @Published var hasMorePages: Bool = true
    @Published var showNoDataMessage: Bool = false

    @Published var searchText: String = ""
    var skuSearchText: String = ""

    // MARK: - Private Properties

    private var currentPage: Int = 1
    private let itemsPerPage: Int = 6
    private var totalPages: Int = 1
    private var isInitialLoad: Bool = false

    // MARK: - Filter Properties (matching Android implementation)

    private var selectedBrandId: Int?
    private var selectedSellerId: Int?
    private var selectedMedicalRepId: Int?
    private var selectedInfluencerId: Int?
    private var isFavouriteFilter: Bool = false

    override init() {
        super.init()
    }

    // MARK: - Public Methods

    func loadProducts() {
        currentPage = 1
        products = []
        Task {
            await fetchProducts()
        }
    }

    func refreshProducts() {
        currentPage = 1
        products = []
        hasMorePages = true
        showNoDataMessage = false
        Task {
            await fetchProducts()
        }
    }

    func searchProducts(keyword: String) {
        searchKeyword = keyword
        currentPage = 1
        products = []
        hasMorePages = true
        showNoDataMessage = false
        Task {
            await fetchProducts()
        }
    }

    func loadMoreProducts() {
        guard hasMorePages && !isLoadingMore else { return }
        currentPage += 1
        Task {
            await fetchProducts(isLoadingMore: true)
        }
    }

    func toggleFavorite(for product: Product) {
        Task {
            await updateFavoriteStatus(for: product)
        }
    }

    // MARK: - Private Methods

    private func fetchProducts(isLoadingMore: Bool = false) async {
        if isLoadingMore {
            self.isLoadingMore = true
        } else {
            isLoading = true
            showNoDataMessage = false
        }

        let request = ProductRequest(
            perPage: itemsPerPage,
            pageNo: currentPage,
            category: "",
            brand: selectedBrandId,
            sku: skuSearchText,
            seller: selectedSellerId,
            medicalRepId: selectedMedicalRepId,
            keyword: searchKeyword,
            influencerId: selectedInfluencerId,
            isFavourite: isFavouriteFilter ? "1" : "0",
            listFrom: ""
        )

        var parameters: [String: Any] = [
            "per_page": request.perPage,
            "page_no": request.pageNo,
            "category": request.category,
            "sku": request.sku,
            "keyword": request.keyword,
            "isFavourite": request.isFavourite,
            "listFrom": request.listFrom
        ]

        if let brand = request.brand {
            parameters.updateValue(brand, forKey: "brand")
        }
        if let seller = request.seller {
            parameters.updateValue(seller, forKey: "seller")
        }
        if let medicalRepId = request.medicalRepId {
            parameters.updateValue(medicalRepId, forKey: "medical_rep_id")
        }

        if let influencerId = request.influencerId {
            parameters.updateValue(influencerId, forKey: "influencer_id")
        }

        await onApiCall(
            { try await self.api.productsList(parameters: parameters) },
            withLoadingIndicator: !isInitialLoad,
            onSuccess: { response in
                if !self.isInitialLoad { self.isInitialLoad = true }

                // Handle the actual API response
                self.handleProductsResponse(response.data, isLoadingMore: isLoadingMore)

                self.isLoading = false
                self.isLoadingMore = false
            },
            onFailure: { error in
                self.isLoading = false
                self.isLoadingMore = false
                Logger.error("Failed to load products: \(error)", tag: "MedicationsViewModel")
            }
        )
    }

    private func handleProductsResponse(_ responseData: ProductListModel?, isLoadingMore: Bool) {
        guard let data = responseData else {
            // Fallback to sample data if API response is nil

            totalPages = 3
            hasMorePages = currentPage < totalPages
            showNoDataMessage = products.isEmpty

            Logger.warning("⚠️ Using sample data - API response was nil", tag: "MedicationsViewModel")
            return
        }

        // Parse actual API response
        if isLoadingMore {
            // Append new products for pagination
            products.append(contentsOf: data.products)
        } else {
            // Replace products for new search/refresh
            products = data.products
        }

        // Update pagination state from API response
        let totalPagesInt = Int(data.totalPages)
        totalPages = totalPagesInt

        hasMorePages = currentPage < totalPages

        // Show no data message if no products found
        showNoDataMessage = products.isEmpty

        Logger.info("📦 Loaded \(data.products.count) products, total: \(data.totalProductsCount), page \(currentPage)/\(totalPages)", tag: "MedicationsViewModel")
    }

    private func updateFavoriteStatus(for product: Product) async {
        // Find the product in the array and update its favorite status
        guard let index = products.firstIndex(where: { $0.id == product.id }) else { return }

        let newFavoriteStatus = !product.isFavourite

        // Optimistically update the UI
        let updatedProduct = product

        products[index] = updatedProduct

        // Make API call to update favorite status on server
        let parameters: [String: Any] = [
            "id": product.id,
            "status": newFavoriteStatus ? "1" : "0"
        ]

        await onApiCall(
            { try await self.api.updateFavorite(parameters: parameters) },
            withLoadingIndicator: false,
            onSuccess: { _ in
                Logger.info("💖 Successfully toggled favorite for product \(product.name): \(newFavoriteStatus)", tag: "MedicationsViewModel")
            },
            onFailure: { error in
                // Revert the optimistic update on failure
                let revertedProduct = updatedProduct

                self.products[index] = revertedProduct
                Logger.error("Failed to toggle favorite for product \(product.name): \(error)", tag: "MedicationsViewModel")
            }
        )
    }

    // MARK: - Filter Methods

    func applyFilters(_ filterState: FilterState) {
        Logger.info("🔍 Applying filters: \(filterState)", tag: "MedicationsViewModel")

        selectedBrandId = filterState.selectedBrandId
        selectedSellerId = filterState.selectedSellerId
        selectedMedicalRepId = filterState.selectedMedicalRepId
        selectedInfluencerId = filterState.selectedInfluencerId
        isFavouriteFilter = filterState.isFavouriteFilter

        // Update search text if product search is provided
        if !filterState.productSearch.isEmpty {
            searchText = filterState.productSearch
            searchKeyword = filterState.productSearch
        }

        if !filterState.skuSearch.isEmpty {
            skuSearchText = filterState.skuSearch
        }

        // Refresh products with new filters (immediate API call)
        refreshProducts()
    }

    func getCurrentFilterState() -> FilterState {
        FilterState(
            selectedBrandId: selectedBrandId,
            selectedSellerId: selectedSellerId,
            selectedMedicalRepId: selectedMedicalRepId,
            selectedInfluencerId: selectedInfluencerId,
            skuSearch: skuSearchText, // SKU search is handled separately in the filter
            productSearch: searchKeyword,
            isFavouriteFilter: isFavouriteFilter
        )
    }

    var checkIfAnyFilterApplied: Bool {
        selectedBrandId != nil || selectedSellerId != nil || selectedMedicalRepId != nil || selectedInfluencerId != nil || !searchKeyword.isEmpty || !skuSearchText.isEmpty
    }

    func applyFilters(
        brandId: Int?,
        sellerId: Int?,
        medicalRepId: Int?,
        influencerId: Int?,
        isFavourite: Bool = false
    ) {
        selectedBrandId = brandId
        selectedSellerId = sellerId
        selectedMedicalRepId = medicalRepId
        selectedInfluencerId = influencerId
        isFavouriteFilter = isFavourite

        // Refresh products with new filters
        refreshProducts()
    }

    func clearFilters() {
        selectedBrandId = nil
        selectedSellerId = nil
        selectedMedicalRepId = nil
        selectedInfluencerId = nil
        isFavouriteFilter = false

        // Refresh products without filters
        refreshProducts()
    }

    func handleProductTap(productId: Int) {
        Logger.info("Product tapped: \(productId)", tag: "TopSellingProductsSectionView")

        // Navigate to product detail view using RouterManager
        routerManager?.push(to: .productDetail(productId: productId), where: .medicationsRoute)
    }
}
