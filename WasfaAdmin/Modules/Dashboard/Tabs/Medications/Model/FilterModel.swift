//
//  FilterModel.swift
//  WasfaAdmin
//
//  Created by Apple on 30/06/2025.
//

import Foundation

// MARK: - Filter Data Models

/// Brand model for filter options
struct Brand: Codable, Identifiable, Hashable {
    let id: Int
    let name: String
    let logo: String?

    enum CodingKeys: String, CodingKey {
        case id, name, logo
    }
}

/// Seller model for filter options
struct Seller: Codable, Identifiable, Hashable {
    let id: Int
    let name: String

    enum CodingKeys: String, CodingKey {
        case id, name
    }
}

/// Medical Representative model for filter options
struct MedicalRep: Codable, Identifiable, Hashable {
    let id: Int
    let name: String

    enum CodingKeys: String, CodingKey {
        case id, name
    }
}

/// Influencer model for filter options
struct Influencer: Codable, Identifiable, Hashable {
    let id: Int
    let name: String

    enum CodingKeys: String, CodingKey {
        case id, name
    }
}

// MARK: - Filter State Model

/// Model to hold all filter selections
struct FilterState: Codable, Equatable, Hashable {
    var selectedBrandId: Int?
    var selectedSellerId: Int?
    var selectedMedicalRepId: Int?
    var selectedInfluencerId: Int?
    var skuSearch: String = ""
    var productSearch: String = ""
    var isFavouriteFilter: Bool = false

    /// Check if any filters are applied
    var hasActiveFilters: Bool {
        return selectedBrandId != nil ||
            selectedSellerId != nil ||
            selectedMedicalRepId != nil ||
            selectedInfluencerId != nil ||
            !skuSearch.isEmpty ||
            !productSearch.isEmpty ||
            isFavouriteFilter
    }

    /// Clear all filters
    mutating func clearAll() {
        selectedBrandId = nil
        selectedSellerId = nil
        selectedMedicalRepId = nil
        selectedInfluencerId = nil
        skuSearch = ""
        productSearch = ""
        isFavouriteFilter = false
    }
}

// MARK: - Filter Response Models

/// Response model for brands list API
struct BrandListResponse: Codable {
    let brands: [Brand]

    enum CodingKeys: String, CodingKey {
        case brands = "data"
    }
}

/// Response model for sellers list API
struct SellerListResponse: Codable {
    let sellers: [Seller]

    enum CodingKeys: String, CodingKey {
        case sellers = "data"
    }
}

/// Response model for medical reps list API
struct MedicalRepListResponse: Codable {
    let medicalReps: [MedicalRep]

    enum CodingKeys: String, CodingKey {
        case medicalReps = "data"
    }
}

/// Response model for influencers list API
struct InfluencerListResponse: Codable {
    let influencers: [Influencer]

    enum CodingKeys: String, CodingKey {
        case influencers = "data"
    }
}

// MARK: - Sample Data for Development

extension Brand {
    static var sampleData: [Brand] {
        [
            Brand(id: 1, name: "Pfizer", logo: "https://example.com/pfizer.png"),
            Brand(id: 2, name: "Johnson & Johnson", logo: "https://example.com/jj.png"),
            Brand(id: 3, name: "Novartis", logo: "https://example.com/novartis.png"),
            Brand(id: 4, name: "Roche", logo: "https://example.com/roche.png"),
            Brand(id: 5, name: "Merck", logo: "https://example.com/merck.png")
        ]
    }
}

extension Seller {
    static var sampleData: [Seller] {
        [
            Seller(id: 1, name: "Wasfa Pharmacy"),
            Seller(id: 2, name: "Al Dawaa Pharmacy"),
            Seller(id: 3, name: "Kuwait Pharmacy"),
            Seller(id: 4, name: "New Pharmacy"),
            Seller(id: 5, name: "City Pharmacy")
        ]
    }
}

extension MedicalRep {
    static var sampleData: [MedicalRep] {
        [
            MedicalRep(id: 1, name: "Ahmed Al-Rashid"),
            MedicalRep(id: 2, name: "Sarah Al-Mansouri"),
            MedicalRep(id: 3, name: "Mohammed Al-Sabah"),
            MedicalRep(id: 4, name: "Fatima Al-Zahra"),
            MedicalRep(id: 5, name: "Omar Al-Kuwaiti")
        ]
    }
}

extension Influencer {
    static var sampleData: [Influencer] {
        [
            Influencer(id: 1, name: "Dr. Ali Al-Medani"),
            Influencer(id: 2, name: "Dr. Noor Al-Sabah"),
            Influencer(id: 3, name: "Dr. Khalid Al-Rashid"),
            Influencer(id: 4, name: "Dr. Maryam Al-Zahra"),
            Influencer(id: 5, name: "Dr. Hassan Al-Kuwaiti")
        ]
    }
}

extension FilterState {
    static var empty: FilterState {
        FilterState()
    }

    static var sampleWithFilters: FilterState {
        FilterState(
            selectedBrandId: 1,
            selectedSellerId: 2,
            selectedMedicalRepId: nil,
            selectedInfluencerId: 1,
            skuSearch: "SKU123",
            productSearch: "Paracetamol",
            isFavouriteFilter: true
        )
    }
}
