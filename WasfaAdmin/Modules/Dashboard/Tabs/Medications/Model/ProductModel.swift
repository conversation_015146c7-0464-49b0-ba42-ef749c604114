//
//  ProductModel.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import Foundation

// MARK: - ProductListModel

struct ProductListModel: Codable {
    let totalProductsCount, totalPages: Int
    let products: [Product]
}

// MARK: - Product

struct Product: Codable, Identifiable {
    let publishedOnPos: Int
    let thumbnailImage: String?
    let id, numberOfsales: Int
    let sellerSku: String
    let restricted: String?
    let onRequest: Int
    let sellerDiscount: Double
    let purchasePrice: String
    let isFavourite: Bool
    let approved, featured, seller: Int
    let cog, name, createdAt: String
    let enable, isCompleted: Int
    let unitPrice: String
    let published, currentStock, isPharma: Int
    let apixMargin: String
    let isClean: Int
    let basePrice: String
    let sellingPrice: Double
    let todaysDeal: Int
    let sellerName, apixSku, discount: String
    let rating, publishedOnWebsite, influencerrMargin: Int

    enum CodingKeys: String, CodingKey {
        case publishedOnPos
        case thumbnailImage = "thumbnail_image"
        case id, numberOfsales, sellerSku
        case restricted = "Restricted"
        case onRequest, sellerDiscount, purchasePrice, isFavourite, approved, featured, seller, cog, name, createdAt, enable
        case isCompleted = "is_completed"
        case unitPrice, published, currentStock, isPharma, apixMargin, isClean, basePrice
        case sellingPrice = "selling_price"
        case todaysDeal, sellerName, apixSku, discount, rating, publishedOnWebsite, influencerrMargin
    }

    // MARK: - Computed Properties for UI

    /// Formatted price with currency symbol
    var formattedPrice: String {
        return "KD \(basePrice)"
    }

    /// Stock status for display
    var stockStatus: String {
        return currentStock > 0 ? "\(currentStock) in stock" : "Out of Stock"
    }

    /// Whether the product is in stock
    var isInStock: Bool {
        return currentStock > 0
    }

    /// Placeholder image URL for missing thumbnails
    var imageURL: String {
        return (thumbnailImage?.isEmpty ?? true) ? "https://via.placeholder.com/150" : thumbnailImage!
    }

    /// Rating as a Double for star display
    var ratingValue: Double {
        return Double(rating)
    }

    /// Sales count as an integer
    var salesCount: Int {
        return Int(numberOfsales)
    }

    // MARK: - Static Sample Data for Preview/Testing

//
    static var sampleProduct: Product {
        Product(
            publishedOnPos: 1,
            thumbnailImage: "https://apixrx.com/public/uploads/all/R15513-1.jpg",
            id: 101,
            numberOfsales: 250,
            sellerSku: "SKU123456",
            restricted: nil,
            onRequest: 0,
            sellerDiscount: 10,
            purchasePrice: "1.250",
            isFavourite: true,
            approved: 1,
            featured: 0,
            seller: 22,
            cog: "1.000",
            name: "Sample Product",
            createdAt: "2025-06-27T12:00:00Z",
            enable: 1,
            isCompleted: 1,
            unitPrice: "1.500",
            published: 1,
            currentStock: 15,
            isPharma: 0,
            apixMargin: "0.500",
            isClean: 1,
            basePrice: "1.800",
            sellingPrice: 1.950,
            todaysDeal: 1,
            sellerName: "Wasfa Seller",
            apixSku: "APIX-SKU-001",
            discount: "5%",
            rating: 4,
            publishedOnWebsite: 1,
            influencerrMargin: 10
        )
    }
}

// MARK: - Product Request Parameters

struct ProductRequest: Codable {
    let perPage: Int
    let pageNo: Int
    let category: String
    var brand: Int?
    let sku: String
    var seller: Int?
    var medicalRepId: Int?
    let keyword: String
    var influencerId: Int?
    let isFavourite: String
    let listFrom: String

    enum CodingKeys: String, CodingKey {
        case perPage = "per_page"
        case pageNo = "page_no"
        case category
        case brand
        case sku
        case seller
        case medicalRepId = "medical_rep_id"
        case keyword
        case influencerId = "influencer_id"
        case isFavourite
        case listFrom
    }

    // MARK: - Default Request

    static func defaultRequest(
        page: Int = 1,
        perPage: Int = 4,
        keyword: String = "",
        isFavourite: Bool = false
    ) -> ProductRequest {
        ProductRequest(
            perPage: perPage,
            pageNo: perPage,
            category: "",
            sku: "",
            keyword: keyword,
            isFavourite: isFavourite ? "1" : "0",
            listFrom: ""
        )
    }
}
