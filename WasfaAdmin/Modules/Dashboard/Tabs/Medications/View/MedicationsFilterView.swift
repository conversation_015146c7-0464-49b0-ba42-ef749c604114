//
//  MedicationsFilterView.swift
//  WasfaAdmin
//
//  Created by Apple on 30/06/2025.
//

import SwiftUI

/// Main filter screen for medications matching Android design
struct MedicationsFilterView: View {
    @StateObject private var viewModel = MedicationsFilterViewModel()
    @Environment(\.dismiss) private var dismiss
    
    // Filter completion callback
    let onFiltersApplied: (FilterState) -> Void
    
    // Initial filter state
    let initialFilters: FilterState
    
    init(
        initialFilters: FilterState = FilterState.empty,
        onFiltersApplied: @escaping (FilterState) -> Void
    ) {
        self.initialFilters = initialFilters
        self.onFiltersApplied = onFiltersApplied
    }
    
    var body: some View {
        SuperView(viewModel: viewModel) {
            VStack(spacing: 0) {
                // Header Section
                headerSection
                
                // Filter Content
                ScrollView {
                    VStack(spacing: 15) {
                        filterContentSection
                    }
                    .padding(20)
                    .background(
                        RoundedCorner(radius: 20, corners: [.topLeft, .topRight])
                            .fill(Color.white)
                    )
                }
                .background(Color.white)
                
                // Bottom Actions
                FilterBottomActions(
                    onApplyFilter: {
                        Logger.info("🔍 Applying filters", tag: "MedicationsFilterView")
                        onFiltersApplied(viewModel.filterState)
                        dismiss()
                    },
                    onClearFilter: {
                        Logger.info("🧹 Clearing filters", tag: "MedicationsFilterView")
                        viewModel.clearAllFilters()
                    },
                    hasActiveFilters: viewModel.filterState.hasActiveFilters
                )
            }
        }
        .navigationBarHidden(true)
        .background(Color.white)
        .onLoad {
            viewModel.setInitialFilters(initialFilters)
            viewModel.loadFilterData()
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        HStack {
            // Close Button
            Button(action: {
                Logger.info("❌ Filter screen closed", tag: "MedicationsFilterView")
                dismiss()
            }) {
                Image(systemName: "xmark")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.black)
                    .frame(width: 24, height: 24)
            }
            .padding(.leading, 10)
            
            Spacer()
            
            // Title
            Text("Filters")
                .font(.custom("Roboto-Bold", size: 18))
                .foregroundColor(.black)
            
            Spacer()
            
            // Invisible spacer for balance
            Color.clear
                .frame(width: 24, height: 24)
                .padding(.trailing, 10)
        }
        .frame(height: 50)
        .padding(.top, 15)
        .background(Color.white)
    }
    
    // MARK: - Filter Content Section
    
    private var filterContentSection: some View {
        VStack(spacing: 16) {
            // Influencer Filter (conditionally shown)
//            if viewModel.shouldShowInfluencerFilter {
//                
//                    
//            }
            // Influencer Filter
            influencerFilterSection
            

            // SKU Search
            skuSearchSection
               

            // Product Search
            productSearchSection
                

            // Brand Filter
            brandFilterSection
               

            // Seller Filter
            sellerFilterSection
               

            // Medical Rep Filter
            medicalRepFilterSection
               
        }
    }
    
    // MARK: - Individual Filter Sections
    
    private var influencerFilterSection: some View {
        FilterSectionView(
            title: "Influencer",
            placeholder: "Select Influencer",
            selectedText: viewModel.selectedInfluencerName,
            isExpanded: $viewModel.isInfluencerExpanded,
            isLoading:  $viewModel.isLoadingInfluencers
        ) {
            LazyVStack(spacing: 8) {
                ForEach(viewModel.influencers) { influencer in
                    FilterOptionRow(
                        item: influencer,
                        displayText: influencer.name,
                        isSelected: viewModel.filterState.selectedInfluencerId == influencer.id,
                        onTap: {
                            viewModel.selectInfluencer(influencer)
                        }
                    )
                }
            }
        }
        .clipped()
//
    }
    
    private var skuSearchSection: some View {
        FilterTextInputView(
            placeholder: "Search By SKU",
            text: Binding(
                get: { viewModel.skuSearchText },
                set: { viewModel.updateSkuSearch($0) }
            )
        )
        .background(Color.white) // Ensure solid background
        
    }

    private var productSearchSection: some View {
        FilterTextInputView(
            placeholder: "Search by Products",
            text: Binding(
                get: { viewModel.productSearchText },
                set: { viewModel.updateProductSearch($0) }
            )
        )
        .background(Color.white) // Ensure solid background
        .allowsHitTesting(true) // Explicitly allow hit testing
    }
    
    private var brandFilterSection: some View {
        FilterSectionView(
            title: "Brand",
            placeholder: "Select Brand",
            selectedText: viewModel.selectedBrandName,
            isExpanded: $viewModel.isBrandExpanded,
            isLoading:  $viewModel.isLoadingBrands
        ) {
            LazyVStack(spacing: 8) {
                ForEach(viewModel.brands) { brand in
                    FilterOptionRow(
                        item: brand,
                        displayText: brand.name,
                        isSelected: viewModel.filterState.selectedBrandId == brand.id,
                        onTap: {
                            viewModel.selectBrand(brand)
                        }
                    )
                }
            }
        }
        .clipped()
    }
    
    private var sellerFilterSection: some View {
        FilterSectionView(
            title: "Seller",
            placeholder: "Select Seller",
            selectedText: viewModel.selectedSellerName,
            isExpanded: $viewModel.isSellerExpanded,
            isLoading:  $viewModel.isLoadingSellers
        ) {
            LazyVStack(spacing: 8) {
                ForEach(viewModel.sellers) { seller in
                    FilterOptionRow(
                        item: seller,
                        displayText: seller.name,
                        isSelected: viewModel.filterState.selectedSellerId == seller.id,
                        onTap: {
                            viewModel.selectSeller(seller)
                        }
                    )
                }
            }
        }
        .clipped()
    }
    
    private var medicalRepFilterSection: some View {
        FilterSectionView(
            title: "Medical Rep",
            placeholder: "Select Medical Rep",
            selectedText: viewModel.selectedMedicalRepName,
            isExpanded: $viewModel.isMedicalRepExpanded,
            isLoading:  $viewModel.isLoadingMedicalReps
        ) {
            LazyVStack(spacing: 8) {
                ForEach(viewModel.medicalReps) { medicalRep in
                    FilterOptionRow(
                        item: medicalRep,
                        displayText: medicalRep.name,
                        isSelected: viewModel.filterState.selectedMedicalRepId == medicalRep.id,
                        onTap: {
                            viewModel.selectMedicalRep(medicalRep)
                        }
                    )
                }
            }
        }
        .clipped()
  
    }
}


// MARK: - Preview

#Preview {
    NavigationStack {
        MedicationsFilterView(
            initialFilters: FilterState.sampleWithFilters,
            onFiltersApplied: { filters in
                print("Filters applied: \(filters)")
            }
        )
        .attachAllEnvironmentObjects()
    }
}
