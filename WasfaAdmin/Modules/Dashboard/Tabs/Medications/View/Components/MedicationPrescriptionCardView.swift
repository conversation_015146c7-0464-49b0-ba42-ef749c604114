//
//  MedicationPrescriptionCardView.swift
//  WasfaAdmin
//
//  Created by Apple on 28/06/2025.
//

//import SwiftUI
//
///// Prescription card component matching the Android item_pres.xml layout exactly
//struct MedicationPrescriptionCardView: View {
//    let product: Product
//    let onViewDetails: () -> Void
//    
//    var body: some View {
//        HStack(spacing: 16) {
//            // Image section
//            AsyncImage(url: URL(string: product.imageURL )) { phase in
//                switch phase {
//                case .empty:
//                    ProgressView()
//                case .success(let image):
//                    image
//                        .resizable()
//                        .scaledToFit()
//                        .frame(width: 90, height: 90)
//                        .cornerRadius(8)
//                case .failure:
//                    Image(systemName: "photo")
//                        .resizable()
//                        .scaledToFit()
//                        .frame(width: 90, height: 90)
//                        .cornerRadius(8)
//                @unknown default:
//                    EmptyView()
//                }
//            }
//            .padding(.leading, 10)
//
//            VStack(alignment: .leading, spacing: 4) {
//                // Brand + Title
//                Text("Wasfa Kuwait")
//                    .font(.custom("Roboto-Regular", size: 14))
//                    .foregroundColor(.black)
//                
//                Text(product.name)
//                    .font(.custom("Roboto-Bold", size: 18))
//                    .foregroundColor(.black)
//
//                Spacer()
//
//                HStack {
//                    VStack(alignment: .leading, spacing: 2) {
//                        Text("Stock")
//                            .font(.custom("Roboto-Regular", size: 14))
//                            .foregroundColor(.gray)
//                        Text("\(product.currentStock)")
//                            .font(.custom("Roboto-Medium", size: 14))
//                            .foregroundColor(.black)
//                    }
//
//                    Spacer()
//
//                    VStack(alignment: .leading, spacing: 2) {
//                        Text("Price")
//                            .font(.custom("Roboto-Regular", size: 14))
//                            .foregroundColor(.gray)
//                        Text("KD 3.730") // Replace with dynamic value if needed
//                            .font(.custom("Roboto-Medium", size: 14))
//                            .foregroundColor(.black)
//                    }
//                }
//            }
//            .padding(.vertical, 12)
//            .padding(.trailing, 10)
//        }
//        .frame(height: 130)
//        .background(Color.white)
//        .cornerRadius(20)
//        .shadow(color: Color.black.opacity(0.05), radius: 6, x: 0, y: 1)
//        .padding(.horizontal, 8)
//    }
//    
//    // MARK: - Patient Info Section
//    private var patientInfoSection: some View {
//        HStack(alignment: .center, spacing: 10) {
//            // Product Image - matching Android 62dp x 62dp
//            CachedAsyncImageView(
//                imageUrl: product.imageURL,
//                placeholder: Image(systemName: "photo"),
//                errorImage: Image(systemName: "photo")
//            )
//            .frame(width: 62, height: 62)
//            .clipShape(Circle())
//            
//            // Product Details
//            VStack(alignment: .leading, spacing: 5) {
//                Text(product.name)
//                    .font(.custom("Roboto-Bold", size: 18))
//                    .foregroundColor(.black)
//                    .lineLimit(1)
//                
//                Text("Product")
//                    .font(.custom("Roboto-Bold", size: 14))
//                    .foregroundColor(Color(hex: "#B9B9B9") ?? .gray)
//                    .lineLimit(2)
//            }
//            
//            Spacer()
//        }
//    }
//    
//    // MARK: - Medications Section
//    private var medicationsSection: some View {
//        HStack(alignment: .top, spacing: 10) {
//            // Medication Icon - matching Android 32dp x 32dp
//            Image("med_bg_icon")
//                .resizable()
//                .frame(width: 32, height: 32)
//            
//            // Medications Info
//            VStack(alignment: .leading, spacing: 2) {
//                // Label - matching Android TextView styling
//                Text("Medications Prescribed")
//                    .font(.custom("Roboto-Medium", size: 12))
//                    .foregroundColor(Color(hex: "#99A61C5C") ?? .gray)
//
//                // Count - matching Android TextView styling
//                Text("\(product.currentStock)")
//                    .font(.custom("Roboto-Bold", size: 12))
//                    .foregroundColor(Color(hex: "#A42161") ?? Color.purple) // doctor_main_color
//            }
//            
//            Spacer()
//        }
//    }
//    
//    // MARK: - View Details Button
//    private var viewDetailsButton: some View {
//        Button(action: onViewDetails) {
//            Text("View Details")
//                .font(.custom("Roboto-Regular", size: 16))
//                .foregroundColor(.white)
//                .frame(maxWidth: .infinity)
//                .frame(height: 38)
//                .background(Color(hex: "#A42161") ?? Color.purple) // doctor_main_color
//                .cornerRadius(12)
//        }
//    }
//}
//
//// MARK: - Preview
//#Preview {
//    VStack(spacing: 20) {
//        MedicationPrescriptionCardView(
//            product: Product.sampleProduct,
//            onViewDetails: {
//                print("View details tapped")
//            }
//        )
//    }
//    .padding()
//    .background(Color(hex: "#FAFAFE") ?? Color.gray.opacity(0.1))
//}
