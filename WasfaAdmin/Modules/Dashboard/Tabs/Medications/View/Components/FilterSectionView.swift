//
//  FilterSectionView.swift
//  WasfaAdmin
//
//  Created by Apple on 30/06/2025.
//

import SwiftUI

/// Reusable expandable filter section component matching Android design
struct FilterSectionView<Content: View>: View {
    let title: String
    let placeholder: String
    let selectedText: String?
    @Binding var isExpanded: Bool
    @Binding var isLoading: Bool
    let content: Content
    
    init(
        title: String,
        placeholder: String,
        selectedText: String? = nil,
        isExpanded: Binding<Bool>,
        isLoading: Binding<Bool>,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.placeholder = placeholder
        self.selectedText = selectedText
        self._isExpanded = isExpanded
        self._isLoading = isLoading
        self.content = content()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header Section
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                    }else{
                        Text(selectedText ?? placeholder)
                            .font(.custom("Roboto-Regular", size: 12))
                            .foregroundColor(selectedText != nil ? .black : (Color(hex: "#B4ABAB") ?? .gray))
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    
                  
                   
                        Image(systemName: "chevron.down")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(Color(hex: "#222222") ?? .black)
                            .rotationEffect(.degrees(isExpanded ? 180 : 0))
                        .animation(.bouncy, value: isExpanded)
                }
                .padding(.horizontal, 10)
                .frame(height: 40)
            }
            .buttonStyle(PlainButtonStyle())
            
            // Expandable Content
            if isExpanded {
                VStack(spacing: 0) {
                    // Divider
                    Rectangle()
                        .fill(Color.black.opacity(0.25))
                        .frame(height: 1)

                    // Content
                    content
                        .padding(10)
                        .clipped() // Prevent content from extending beyond bounds
                }
               
//                .transition(.move(edge: .bottom).combined(with: .opacity))
            }
        }
//        .background(Color.white)
        .disableWithGray(isLoading)
        
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(.gray, lineWidth: 1)
        )
        .clipShape(RoundedRectangle(cornerRadius: 12))
       
    }
}

/// Individual filter option row component
struct FilterOptionRow<T: Identifiable & Hashable>: View {
    let item: T
    let displayText: String
    let isSelected: Bool
    let onTap: () -> Void
    
    init(
        item: T,
        displayText: String,
        isSelected: Bool = false,
        onTap: @escaping () -> Void
    ) {
        self.item = item
        self.displayText = displayText
        self.isSelected = isSelected
        self.onTap = onTap
    }
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                Text(displayText)
                    .font(.custom("Roboto-Regular", size: 14))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)

                if isSelected {
                    Image(systemName: "checkmark")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: "#A61C5C") ?? .blue)
                }
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 4)
            .background(isSelected ? (Color(hex: "#A61C5C") ?? .blue).opacity(0.1) : Color.clear)
            .cornerRadius(4)
        }
        .buttonStyle(PlainButtonStyle())
        .contentShape(Rectangle()) // Define explicit touch target
        .clipped() // Prevent touch events from extending beyond bounds
    }
}

/// Text input filter section for SKU and product search
struct FilterTextInputView: View {
    let placeholder: String
    @Binding var text: String

    var body: some View {
        HStack {
            TextField(placeholder, text: $text)
                .font(.custom("Roboto-Regular", size: 12))
                .foregroundColor(.black)
                .textFieldStyle(PlainTextFieldStyle())
                .frame(maxWidth: .infinity)
        }
        .padding(.horizontal, 10)
        .frame(height: 40)
//        .background(Color.white)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(.gray, lineWidth: 1)
        )
        .clipShape(RoundedRectangle(cornerRadius: 12))
       
    }
}

/// Bottom action buttons for apply and clear filters
struct FilterBottomActions: View {
    let onApplyFilter: () -> Void
    let onClearFilter: () -> Void
    let hasActiveFilters: Bool
    
    var body: some View {
        HStack(spacing: 10) {
            
            // Apply Filter Button
            Button(action: onApplyFilter) {
                Text("Apply Filter")
                    .font(.custom("Roboto-Medium", size: 18))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 48)
                    .background(.doctorMain)
                    .cornerRadius(8)
            }
            .buttonStyle(PlainButtonStyle())
            
            
            // Clear Filter Button
            Button(action: onClearFilter) {
                Text("Clear")
                    .font(.custom("Roboto-Medium", size: 18))
                    .foregroundColor(.doctorMain)
                    .frame(maxWidth: 120)
                    .frame(height: 48)
//                    .background(hasActiveFilters ? (Color(hex: "#666666") ?? .gray) : (Color(hex: "#CCCCCC") ?? .gray))
                    .overlay(
                           RoundedRectangle(cornerRadius: 12)
                               .stroke(.doctorMain, lineWidth: 2)
                       )
            }
      
            .disableWithGray(!hasActiveFilters)
            
          
        }
        .padding(.horizontal)
        .padding(.top, 10)
        .padding(.bottom, 25)
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        // Expandable Section Preview
        FilterSectionView(
            title: "Select Brand",
            placeholder: "Select Brand",
            selectedText: "Pfizer",
            isExpanded: .constant(true),
            isLoading: .constant(true)
        ) {
            VStack(spacing: 8) {
                FilterOptionRow(
                    item: Brand.sampleData[0],
                    displayText: "Pfizer",
                    isSelected: true,
                    onTap: { print("Pfizer selected") }
                )
                FilterOptionRow(
                    item: Brand.sampleData[1],
                    displayText: "Johnson & Johnson",
                    isSelected: false,
                    onTap: { print("J&J selected") }
                )
            }
        }
        
        // Text Input Preview
        FilterTextInputView(
            placeholder: "Search By SKU",
            text: .constant("SKU123")
        )
        
        // Bottom Actions Preview
        FilterBottomActions(
            onApplyFilter: { print("Apply filter") },
            onClearFilter: { print("Clear filter") },
            hasActiveFilters: true
        )
        
        Spacer()
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
