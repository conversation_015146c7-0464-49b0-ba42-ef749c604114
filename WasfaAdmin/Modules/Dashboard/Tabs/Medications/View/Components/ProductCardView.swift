//
//  ProductCardView.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI

/// Product card component matching the Android item_pres.xml layout exactly
struct ProductCardView: View {
    let product: Product
    let onViewDetails: (Int) -> Void
    
    var body: some View {
        Button {
            onViewDetails(product.id)
        } label: {
            HStack(spacing: 16) {
                NetworkImageView(path: product.imageURL)
                    .frame(width: 62, height: 62)
                   
                
                VStack(spacing: 0) {
                    // MARK: - Customer Info Section (matching Android LinearLayout with ImageView + TextViews)

                    customerInfoSection
                       
                    // MARK: - Medications Section (matching Android LinearLayout with icon + text)

                    medicationsSection
                      
                        .padding(.top, 20)
                }
            }
            .padding()
            .background(Color.white)
            .cornerRadius(16)
            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
            .padding(.horizontal, 1)
            .padding(.bottom, 10)
        }
    }
    
    // MARK: - Customer Info Section

    private var customerInfoSection: some View {
        HStack(alignment: .center, spacing: 10) {
            // Customer Details
            VStack(alignment: .leading, spacing: 12) {
                // Medical Condition - matching Android TextView styling
                HStack(spacing: 8) {
                    Image(.seller)
                        .frame(width: 20, height: 20)
                        .clipShape(Circle())
                    
                    Text(product.sellerName)
                        .font(Font.custom("Poppins", size: 12))
                        .lineSpacing(12)
                        .foregroundColor(.black)
                        .lineLimit(2)
                }
                // Customer Name - matching Android TextView styling
                Text(product.name)
                   
                    .font(Font.custom("Poppins", size: 15).weight(.semibold))
                    .multilineTextAlignment(.leading)
                    .foregroundColor(.black)
                
            }
            
            Spacer()
        }
    }
    
    private func patientInfoRow(title: String, value: String) -> some View {
        HStack {
            Text(title + ":")
                .font(Font.custom("Poppins", size: 12))
                .lineSpacing(12)
                .foregroundColor(Color(red: 0.73, green: 0.73, blue: 0.73))
                .frame(alignment: .leading)
              
            Text(value)
                .font(Font.custom("Poppins", size: 12).weight(.medium))
                .lineSpacing(12)
                .foregroundColor(.black)
            .frame(maxWidth: .infinity, alignment: .trailing)
            
            Spacer()
        }
    }
    
    // MARK: - Medications Section

    private var medicationsSection: some View {
        VStack(alignment: .leading,spacing: 12) {
            patientInfoRow(title: "Stock", value: "\(product.currentStock)")
            Text(product.purchasePrice)
                .font(.custom("Roboto-Bold", size: 16).bold())
                .foregroundColor(Color("DoctorMainColor"))
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 20) {
        ProductCardView(
            product: Product.sampleProduct,
            onViewDetails: { _ in
                print("View details tapped")
            }
        )
        
        ProductCardView(
            product: Product.sampleProduct,
            onViewDetails: { _ in
                print("View details tapped")
            }
        )
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
