//
//  MedicationsView.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

struct MedicationsView: View {
    @StateObject private var viewModel = MedicationsViewModel()
    @Environment(\.routerManager) private var routerManager
    @State private var showFilterSheet = false

    var body: some View {
        SuperView(viewModel: viewModel) {
            // MARK: - Main Content (matching Android NestedScrollView structure)

            ScrollView {
                LazyVStack(spacing: 0) {
                    // MARK: - Search Bar Section

                    searchBarSection
                        .padding(.top, 30) // Matching Android layout_marginTop="30dp"
                        .padding(.horizontal, 16) // Matching Android layout margins

                    // MARK: - Prescriptions List Section

                    prescriptionsListSection
                        .padding(.top, 20) // Matching Android layout_marginTop="20dp"
                        .padding(.horizontal, 16) // Matching Android layout margins

                    // MARK: - Load More Section

                    if viewModel.hasMorePages && !viewModel.products.isEmpty {
                        loadMoreSection
                    }

                    // MARK: - Bottom Spacing

                    Spacer(minLength: 20)
                }
            }
            .scrollDismissesKeyboard(.interactively)
            .scrollIndicators(.hidden) // Matching Android scrollbars="none"
            .background(.white) // Matching Android background="#FAFAFE"
            .navigationTitle("All Products") // Hide default navigation title
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                viewModel.loadProducts()
            }
        }
        .onLoad(perform: viewModel.loadProducts)
        .sheet(isPresented: $showFilterSheet) {
            MedicationsFilterView(
                initialFilters: viewModel.getCurrentFilterState(),
                onFiltersApplied: { filters in
                    Logger.info("🔍 Filters applied: \(filters)", tag: "MedicationsView")
                    viewModel.applyFilters(filters)
                }
            )
            .presentationDetents([.large])
            .presentationDragIndicator(.visible)
        }
    }

    // MARK: - Search Bar Section

    private var searchBarSection: some View {
        SearchBarView(
            searchText: $viewModel.searchText,
            placeholder: "Search... ",
            showFilterIndicator: viewModel.checkIfAnyFilterApplied,
            onSearchChanged: {
                viewModel.searchProducts(keyword: $0)
            },
            
            onFilterTapped: {
                Logger.info("🔍 Filter button tapped", tag: "MedicationsView")
                showFilterSheet = true
            }
        )
    }

    // MARK: - Prescriptions List Section

    private var prescriptionsListSection: some View {
        Group {
            if viewModel.isLoading && viewModel.products.isEmpty {
                // Loading state for initial load
                loadingView
            } else if viewModel.products.isEmpty && !viewModel.isLoading {
                // No data state (matching Android txt_no_data)
                noDataView
            } else {
                // Prescriptions list
                prescriptionsGrid
            }
        }
    }

    // MARK: - Loading View

    private var loadingView: some View {
        VStack {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: Color.purple))
                .scaleEffect(1.2)

            Text("Loading...")
                .font(.custom("Roboto-Regular", size: 14))
                .foregroundColor(.gray)
                .padding(.top, 8)
        }
        .frame(maxWidth: .infinity)
        .padding(.top, 50)
    }

    // MARK: - No Data View

    private var noDataView: some View {
        Text("No Data Found")
            .font(.custom("Roboto-Bold", size: 14))
            .foregroundColor(.black)
            .frame(maxWidth: .infinity)
            .padding(.top, 50)
    }

    // MARK: - Prescriptions Grid

    private var prescriptionsGrid: some View {
        LazyVStack(spacing: 10) { // Matching Android RecyclerView item spacing
            ForEach(viewModel.products) { product in
                ProductCardView(product: product, onViewDetails: viewModel.handleProductTap)
            }
        }
    }

    // MARK: - Load More Section

    private var loadMoreSection: some View {
        VStack {
            if viewModel.isLoadingMore {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: Color.purple))
                    .padding()
            } else {
                Button("Load More") {
                    viewModel.loadMoreProducts()
                }
                .font(.custom("Roboto-Medium", size: 16))
                .foregroundColor(Color.purple)
                .padding()
            }
        }
        .onAppear {
            viewModel.loadMoreProducts()
        }
    }
}

#Preview {
    NavigationStack {
        MedicationsView()
            .attachAllEnvironmentObjects()
    }
}
