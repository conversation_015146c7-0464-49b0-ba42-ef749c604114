//
//  ProductDetailViewModel.swift
//  WasfaAdmin
//
//  Created by Apple on 29/06/2025.
//

import Foundation

class ProductDetailViewModel: SuperViewModel {
    @Published var productDetail: ProductDetailsModel?
    
    override init() {
        super.init()
        Logger.debug("ProductDetailViewModel initialized", tag: "ProductDetailViewModel")
    }
    
    deinit {
        Logger.debug("ProductDetailViewModel deinitialized", tag: "ProductDetailViewModel")
    }
    
    // MARK: - Public Methods
    
    /// Load product details for the given product ID
    /// - Parameter productId: The ID of the product to load details for
    func loadProductDetails(productId: Int) {
        Task {
            await fetchProductDetails(productId: productId)
        }
    }
    
    // MARK: - Private Methods
    
    /// Fetch product details from the API
    /// - Parameter productId: The ID of the product to fetch details for
    private func fetchProductDetails(productId: Int) async {
    
        productDetail = nil
        
        let parameters: [String: Any] = [
            "productId": String(productId)
        ]
        
        await onApiCall(
            { try await self.api.productDetails(parameters: parameters) },
          
            onSuccess: { response in
                // Handle successful product details response
                if let productDetails = response.data?.first {
                    self.productDetail = productDetails
                    Logger.info("Product details loaded successfully for ID: \(productId)", tag: "ProductDetailViewModel")
                } else {
                   
                    Logger.warning("No product details found for ID: \(productId)", tag: "ProductDetailViewModel")
                }
               
            },
            onFailure: { error in
                // Handle product details loading failure
               
              
                Logger.error("Failed to load product details for ID \(productId): \(error)", tag: "ProductDetailViewModel")
            }
        )
    }
    
    // MARK: - Helper Methods
    
    /// Retry loading product details
    /// - Parameter productId: The ID of the product to retry loading for
    func retryLoadingProductDetails(productId: Int) {
        loadProductDetails(productId: productId)
    }
    
    /// Check if product is available for purchase
    var isProductAvailable: Bool {
        guard let product = productDetail else { return false }
        return product.isInStock
    }
    
    /// Get formatted product price
    var formattedPrice: String {
        guard let product = productDetail else { return "" }
        return product.unitPrice
    }
    
    /// Get product stock status
    var stockStatus: String {
        guard let product = productDetail else { return "Unknown" }
        return product.stockStatus
    }
}
