//
//  PrescriptionDetailViewModel.swift
//  WasfaAdmin
//
//  Created by Apple on 30/06/2025.
//

import Foundation

class PrescriptionDetailViewModel: SuperViewModel {
    @Published var prescriptionDetail: PrescriptionDetailsModel?
    
    override init() {
        super.init()
        Logger.debug("PrescriptionDetailViewModel initialized", tag: "PrescriptionDetailViewModel")
    }
    
    deinit {
        Logger.debug("PrescriptionDetailViewModel deinitialized", tag: "PrescriptionDetailViewModel")
    }
    
    
    
    // MARK: - Public Methods
    
    /// Load prescription details for the given prescription ID
    /// - Parameter prescriptionId: The ID of the prescription to load details for
    func loadPrescriptionDetails(prescriptionId: String) {
        Task {
            await fetchPrescriptionDetails(prescriptionId: prescriptionId)
        }
    }
    
    // MARK: - Private Methods
    
    /// Fetch prescription details from the API
    /// - Parameter prescriptionId: The ID of the prescription to fetch details for
    private func fetchPrescriptionDetails(prescriptionId: String) async {
       
        prescriptionDetail = nil
        
        let parameters: [String: Any] = [
            "id": prescriptionId
        ]
        
        await onApiCall(
            { try await self.api.prescriptionDetails(parameters: parameters) },
           
            onSuccess: { response in
                // Handle successful prescription details response
                if let prescriptionDetails = response.data?.first {
                    self.prescriptionDetail = prescriptionDetails
                    Logger.info("Prescription details loaded successfully for ID: \(prescriptionId)", tag: "PrescriptionDetailViewModel")
                } else {
                   
                    Logger.warning("No prescription details found for ID: \(prescriptionId)", tag: "PrescriptionDetailViewModel")
                }
               
            },
            onFailure: { error in
                // Handle prescription details loading failure
               
                Logger.error("Failed to load prescription details for ID \(prescriptionId): \(error)", tag: "PrescriptionDetailViewModel")
            }
        )
    }
    
    // MARK: - Helper Methods
    
    /// Retry loading prescription details
    /// - Parameter prescriptionId: The ID of the prescription to retry loading for
    func retryLoadingPrescriptionDetails(prescriptionId: String) {
        loadPrescriptionDetails(prescriptionId: prescriptionId)
    }
    
    /// Get formatted patient name
    var formattedPatientName: String {
        guard let prescription = prescriptionDetail else { return "Unknown Patient" }
        return prescription.patientInfo.name
    }
    
    /// Get total medications count
    var totalMedicationsCount: Int {
        guard let prescription = prescriptionDetail else { return 0 }
        return prescription.medications.count 
    }
    
    /// Check if prescription has patient address
    var hasPatientAddress: Bool {
        guard let prescription = prescriptionDetail else { return false }
        return !prescription.patientInfo.address.isEmpty
    }
    
    /// Get formatted prescription description
    var formattedDescription: String {
        guard let prescription = prescriptionDetail else { return "" }
        return prescription.description ?? ""
    }
}
