//
//  HomeViewModel.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import Charts
import Foundation
import SwiftUI

class HomeViewModel: SuperViewModel {
    @Published var homeData: HomeModel?
    @Published var isLoading: Bool = false

    @Published private(set) var prescriptionCardViewList: [PrescriptionCardView] = []
    @Published var selectedPrescriptionCardView: Int? = 0

    // MARK: - Visual Data Section Properties

    @Published var filterDateText: String = "Filter by date"
    @Published var salesAnalyticsAmount: Double = 0.0
    @Published var chartData: [ChartDataPoint] = []
    @Published var graphData: GraphResponse?

    // MARK: - Filter Properties for FilterBarView

    @Published var selectedFilter: FilterOption?
    @Published var showingDateFilter: Bool = false
    @Published var customDateRange: String? = nil
    @Published var showingCustomDatePicker: Bool = false

    // MARK: - Top Selling Products Properties

    @Published var topSellingProducts: [TopSellingProduct] = []

    // MARK: - Recent Prescriptions Properties

    @Published var recentPrescriptions: [Prescription] = []

    override init() {
        super.init()
        // Initialize with empty chart data - will be populated by API
    }

    func loadHomePage() {
        Task {
            await withTaskGroup(of: Void.self) { group in
                group.addTask { await self.fetchHomePage() }
                group.addTask { await self.fetchGraphData() }
            }
        }
    }

    private func assignPrescriptionCardViewList() {
        guard let homeData = homeData else { return }
        prescriptionCardViewList = [
            PrescriptionCardView(id: 0, title: "Amount", value: "\(homeData.amount.toString(decimalPlaces: 3))"),
            PrescriptionCardView(id: 1, title: "Count of RX", value: "\(homeData.countOfSale)"),
            PrescriptionCardView(id: 2, title: "Pending Amount", value: "\(homeData.pendingCommission)"),
            PrescriptionCardView(id: 3, title: "Paid Amount", value: "\(homeData.completedPayment)"),
            PrescriptionCardView(id: 4, title: "Refund Orders & Amount", value: "\(homeData.refundAmount)")
        ]
    }

    private func assignVisualDataSection() {
        guard let homeData = homeData else { return }
        salesAnalyticsAmount = homeData.amount

        // Update chart data from graph response if available
        if let graphData = graphData {
            updateChartDataFromGraphResponse(graphData)
        }
    }

    private func updateChartDataFromGraphResponse(_ graphResponse: GraphResponse) {
        let values = graphResponse.numOfSaleValue
        let dates = graphResponse.weekdays

        var newChartData: [ChartDataPoint] = []

        // Ensure we have matching arrays
        let count = min(values.count, dates.count)
        for i in 0 ..< count {
            let value = Double(values[i])
            let fullDate = dates[i]

            newChartData.append(ChartDataPoint(date: fullDate, value: value))
        }

        // Update chart data on main thread (matching Android's UI thread updates)
        DispatchQueue.main.async {
            self.chartData = newChartData
            Logger.info("📊 Chart updated with \(newChartData.count) data points from API", tag: "HomeViewModel")
        }
    }

    private func assignTopSellingProducts() {
        guard let homeData = homeData else { return }
        topSellingProducts = homeData.topSellingProducts
    }

    private func assignRecentPrescriptions() {
        guard let homeData = homeData else { return }
        recentPrescriptions = homeData.prescriptions
    }

    private func fetchHomePage() async {
        isLoading = true
        let parameters: [String: Any] = [:]

        await onApiCall(
            { try await self.api.homePage(parameters: parameters) },
            onSuccess: { response in
                // Handle successful home page data
                self.homeData = response.data
                self.assignPrescriptionCardViewList()
                self.assignVisualDataSection()
                self.assignTopSellingProducts()
                self.assignRecentPrescriptions()
                self.isLoading = false

            },
            onFailure: { error in
                // Handle home page loading failure
                self.isLoading = false
                Logger.error("Failed to load home page: \(error)", tag: "HomeViewModel")
            }
        )
    }

    private func fetchGraphData(dateString: String? = nil) async {
        chartData.removeAll()

        var parameters: [String: Any] = [:]

        if let dateString = dateString {
            parameters.updateValue(dateString, forKey: "date")
        }

        await onApiCall(
            { try await self.api.prescriptionGraphData(parameters: parameters) },
            withLoadingIndicator: false,
            customLoadingBinding: binding(\.isLoading),
            onSuccess: { response in
                // Handle successful graph data
                self.graphData = response.data
                self.assignVisualDataSection() // Update chart with new data
            },
            onFailure: { error in
                // Handle graph data loading failure
                Logger.error("Failed to load graph data: \(error)", tag: "HomeViewModel")
            }
        )
    }

    func loadProfile() {
        Task {
            await fetchProfile()
        }
    }

    private func fetchProfile() async {
        let parameters: [String: Any] = [:]

        await onApiCall(
            { try await self.api.profile(parameters: parameters) },
            onSuccess: { response in
                // Handle successful profile data
                Logger.info("Profile response: \(response)", tag: "HomeViewModel")
            },
            onFailure: { error in
                // Handle profile loading failure
                Logger.error("Failed to load profile: \(error)", tag: "HomeViewModel")
            }
        )
    }

    func loadProductsList() {
        Task {
            await fetchProductsList()
        }
    }

    private func fetchProductsList() async {
        let parameters: [String: Any] = [
            "page": 1,
            "limit": 20
        ]

        await onApiCall(
            { try await self.api.productsList(parameters: parameters) },
            onSuccess: { response in
                // Handle successful products list
                Logger.info("Products list response: \(response)", tag: "HomeViewModel")
            },
            onFailure: { error in
                // Handle products list loading failure
                Logger.error("Failed to load products list: \(error)", tag: "HomeViewModel")
            }
        )
    }

    // MARK: - Filter Methods

    func handleFilterChange(_ filter: FilterOption) {
        selectedFilter = filter
        customDateRange = nil

        // Update filter text based on selection
        filterDateText = filter.dateString
        Logger.info("🔍 Filter changed to: \(filter.rawValue)", tag: "HomeViewModel")
        Task { await fetchGraphData(dateString: filter.dateString) }

        // TODO: Implement API call with filter parameters
        // This would typically trigger a new API call with date parameters
    }

    func handleResetFilter() {
        selectedFilter = nil
        customDateRange = nil
        filterDateText = "Filter by date"

        Logger.info("🔄 Filter reset to default", tag: "HomeViewModel")

        // TODO: Implement API call to reset to default data
        // This would typically trigger a new API call without date filters
        Task { await fetchGraphData() }
    }

    func handleCustomDateRange(startDate: Date, endDate: Date) {
        selectedFilter = .custom

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd-MM-yyyy"
        let startString = dateFormatter.string(from: startDate)
        let endString = dateFormatter.string(from: endDate)

        customDateRange = "\(startString) to \(endString)"
        filterDateText = customDateRange ?? "Custom Range"

        Logger.info("📅 Custom date range selected: \(customDateRange ?? "Unknown")", tag: "HomeViewModel")
        Task { await fetchGraphData(dateString: customDateRange) }
        // TODO: Implement API call with custom date range
        // This would typically trigger a new API call with the selected date range
    }

    func handleProductTap(productId: Int) {
        Logger.info("Product tapped: \(productId)", tag: "TopSellingProductsSectionView")

        // Navigate to product detail view using RouterManager
        routerManager?.push(to: .productDetail(productId: productId), where: .homeRoute)
    }

    func handlePrescriptionTap(prescriptionId: String) {
        Logger.info("Prescription tapped: \(prescriptionId)", tag: "RecentPrescriptionsSectionView")

        // Navigate to prescription detail view using RouterManager
        routerManager?.push(to: .prescriptionDetails(prescriptionId: prescriptionId), where: .homeRoute)
    }
}

// MARK: - Chart Data Model

struct ChartDataPoint: Identifiable, Equatable {
    let id = UUID()
    let date: String
    let value: Double

    static func == (lhs: ChartDataPoint, rhs: ChartDataPoint) -> Bool {
        return lhs.date == rhs.date && lhs.value == rhs.value
    }
}
