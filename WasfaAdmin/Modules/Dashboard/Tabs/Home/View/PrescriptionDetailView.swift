//
//  PrescriptionDetailView.swift
//  WasfaAdmin
//
//  Created by Apple on 30/06/2025.
//

import SwiftUI

struct PrescriptionDetailView: View {
    let prescriptionId: String
    @StateObject private var viewModel = PrescriptionDetailViewModel()

    var body: some View {
        SuperView(viewModel: viewModel) {
            MainScrollBody(backButtonWithTitle: "Prescription Details") {
                VStack(spacing: 24) {
                    if let prescriptionDetail = viewModel.prescriptionDetail {
                        // MARK: - Medications Section

                        medicationsSection(prescriptionDetail.medications)

                        // MARK: - Patient Information Section

                        patientInfoSection(prescriptionDetail.patientInfo)

                        // MARK: - Address Information Section

                        addressInfoSection(prescriptionDetail.patientInfo.address)
                    }

                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.top, 20)
            }
            .navigationTitle("Prescription Details")
            .navigationBarTitleDisplayMode(.large)
            .scrollIndicators(.hidden)
        }
        .onLoad {
            viewModel.loadPrescriptionDetails(prescriptionId: prescriptionId)
        }
    }

    // MARK: - Medications Section

    private func medicationsSection(_ medications: [PrescriptionDetailsModel.Medication]) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            ForEach(medications) {
                MedicationCard(medication: $0)
            }
        }
    }

    // MARK: - Patient Information Section

    private func patientInfoSection(_ patientInfo: PrescriptionDetailsModel.PatientInfo) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section Header
            Text("Patient Information")
                .font(.custom("Roboto-Bold", size: 20))
                .foregroundColor(.black)

            VStack(spacing: 0) {
                // Patient Details
                patientDetailRow(title: "Name", value: patientInfo.name)

                Divider()
                    .padding(.vertical, 12)

                patientDetailRow(title: "DOB", value: patientInfo.dob ?? "")

                Divider()
                    .padding(.vertical, 12)

                patientDetailRow(title: "Civil Id", value: patientInfo.civilID ?? "")

                Divider()
                    .padding(.vertical, 12)

                patientDetailRow(title: "Phone", value: patientInfo.phone)
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
        }
    }

    // MARK: - Patient Detail Row

    private func patientDetailRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.custom("Roboto-Regular", size: 14))
                .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                .frame(maxWidth: .infinity, alignment: .leading)

            Spacer()

            Text(value.isEmpty ? "" : value)
                .font(.custom("Roboto-Medium", size: 14))
                .foregroundColor(.black)
                .multilineTextAlignment(.trailing)
        }
    }

    // MARK: - Address Information Section

    private func addressInfoSection(_ addresses: [PrescriptionDetailsModel.Address]) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            if let address = addresses.first {
            // Section Header
            Text("Address Information")
                .font(.custom("Roboto-Bold", size: 20))
                .foregroundColor(.black)

          
                VStack(spacing: 0) {
                    addressDetailRow(title: "Governorate", value: address.governorateName)

                    Divider()
                        .padding(.vertical, 12)

                    addressDetailRow(title: "Area", value: address.areaName)

                    Divider()
                        .padding(.vertical, 12)

                    addressDetailRow(title: "Block Number", value: address.block)

                    Divider()
                        .padding(.vertical, 12)

                    addressDetailRow(title: "Street", value: address.street)

                    Divider()
                        .padding(.vertical, 12)

                    addressDetailRow(title: "Building", value: address.building)

                    Divider()
                        .padding(.vertical, 12)

                    addressDetailRow(title: "Floor", value: address.floor ?? "N/A")
                }
                .padding(16)
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
            }
        }
    }

    // MARK: - Address Detail Row

    private func addressDetailRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.custom("Roboto-Regular", size: 14))
                .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                .frame(maxWidth: .infinity, alignment: .leading)

            Spacer()

            Text(value.isEmpty ? "" : value)
                .font(.custom("Roboto-Medium", size: 14))
                .foregroundColor(.black)
                .multilineTextAlignment(.trailing)
        }
    }

    // MARK: - Loading View

    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)

            Text("Loading prescription details...")
                .font(.custom("Roboto-Medium", size: 16))
                .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 100)
    }

    // MARK: - Error View

    private var errorView: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.red)

            Text("Failed to load prescription details")
                .font(.custom("Roboto-Medium", size: 16))
                .foregroundColor(.black)

            Button("Retry") {
                viewModel.loadPrescriptionDetails(prescriptionId: prescriptionId)
            }
            .font(.custom("Roboto-Bold", size: 16))
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.doctorMain)
            .cornerRadius(8)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 100)
    }
}

#Preview {
    PrescriptionDetailView(prescriptionId: "100022-4239")
        .attachAllEnvironmentObjects()
}

// MARK: - Medication Card

struct MedicationCard: View {
    let medication: PrescriptionDetailsModel.Medication

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                NetworkImageView(path: medication.productThumbnailImage)
                    .frame(width: 60, height: 60)
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )

                Text(medication.productName)
                    .font(Font.custom("Poppins", size: 18.91).weight(.semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }

            HStack(alignment: .bottom) {
                // Medications Section
                HStack(alignment: .top, spacing: 10) {
                    Image(.tabMedications)
                        .renderingMode(.template)
                        .resizable()
                        .foregroundStyle(.adminMain)
                        .frame(width: 17.relativeFontSize, height: 17.relativeFontSize)
                        .frame(width: 32.relativeFontSize, height: 32.relativeFontSize)
                        .background(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.09))
                        .cornerRadius(10.relativeFontSize)

                    VStack(alignment: .center, spacing: 2) {
                        Text("Dose")
                            .font(Font.custom("Poppins", size: 12))
                            .foregroundColor(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.60))

                        Text("\(medication.dose ?? "N/A"), \(medication.doseday ?? "N/A"), \(medication.doseTime ?? "N/A")")
                            .font(.custom("Roboto-Bold", size: 12))
                            .foregroundColor(Color("DoctorMainColor"))
                    }
                    Spacer()

                    VStack(alignment: .center, spacing: 2) {
                        Text("Course")
                            .font(Font.custom("Poppins", size: 12))
                            .foregroundColor(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.60))

                        Text("\(medication.courseDay ?? "N/A"), \(medication.courseDuration ?? "N/A")")
                            .font(.custom("Roboto-Bold", size: 12))
                            .foregroundColor(Color("DoctorMainColor"))
                    }

                    Spacer()

                }.frame(maxWidth: .infinity, alignment: .leading)

                Text(medication.unitPrice)
                    .font(.custom("Roboto-Bold", size: 14).bold())
                    .foregroundColor(Color("DoctorMainColor"))
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}
