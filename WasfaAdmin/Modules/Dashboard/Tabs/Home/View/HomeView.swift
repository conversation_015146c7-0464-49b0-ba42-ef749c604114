//
//  HomeView.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

struct HomeView: View {
    @StateObject private var viewModel = HomeViewModel()
    

    var body: some View {
        SuperView(viewModel: viewModel) {
            ScrollView {
                VStack(spacing: 32) {
                    // MARK: - Prescription Insight Card

                    PrescriptionInsightCardSection(viewModel: viewModel)

                    // MARK: - Visual Data Section

                    VisualDataSectionView(viewModel: viewModel)


                    // MARK: - Top Selling Products Section

                    TopSellingProductsSectionView(viewModel: viewModel)
                        .padding(.top)

                    // MARK: - Recent Prescriptions Section

                    RecentPrescriptionsSectionView(recentPrescriptions: viewModel.recentPrescriptions, viewModel: viewModel)

                    // MARK: - Loading State

                    if viewModel.isLoading {
                        ProgressView("Loading...")
                            .padding()
                    }

                    Spacer()
                }
                .padding()
            }
            .scrollIndicators(.hidden)
            .navigationTitle("Prescription Insights")
            .navigationBarTitleDisplayMode(.large)
        }
        .onLoad(perform: viewModel.loadHomePage)
    }


}

#Preview {
    HomeView()
        .onLoad(perform: {
            let token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvc3RhZ2dpbmcuYXBpeHJ4LmNvbVwvYXBpXC92MlwvYXV0aFwvbG9naW4iLCJpYXQiOjE3NTEyMjcyMDcsImV4cCI6MTc1MTMxMzYwNywibmJmIjoxNzUxMjI3MjA3LCJqdGkiOiJzUnI3Z0NDaFNQMWNCS05KIiwic3ViIjozMzYsInBydiI6IjIzYmQ1Yzg5NDlmNjAwYWRiMzllNzAxYzQwMDg3MmRiN2E1OTc2ZjcifQ.BhZL0d4DO73i4jRqky0_dQNT2-D-t5nE_FXvwTfiAVQ"
            UserDefaultsSecure.sharedInstance.setGeneratedTokenStringValue(value: token)
        })
        .attachAllEnvironmentObjects()
}
