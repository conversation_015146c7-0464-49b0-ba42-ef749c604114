//
//  VisualDataSectionView.swift
//  WasfaAdmin
//
//  Created by Apple on 28/06/2025.
//

import SwiftUI

/// Complete Visual Data section matching Android layout exactly
struct VisualDataSectionView: View {
    @ObservedObject var viewModel: HomeViewModel

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section title (matching Android)
            Text("Visual Data")
                .font(.custom("Roboto-Bold", size: 22))
                .foregroundColor(.black)

            // Filter bar
            FilterBarView(
                selectedFilter: $viewModel.selectedFilter,
                showingDateFilter: $viewModel.showingDateFilter,
                customDateRange: $viewModel.customDateRange,
                showingCustomDatePicker: $viewModel.showingCustomDatePicker,
                onFilterChange: viewModel.handleFilterChange,
                onResetFilter: viewModel.handleResetFilter,
                onCustomDateRange: viewModel.handleCustomDateRange
            )
            .padding(.trailing, 10) // Matching Android marginEnd="10dp"

            // Sales Analytics
            SalesAnalyticsView(
                amount: viewModel.salesAnalyticsAmount,
                chartData: viewModel.chartData
            )
            .padding(.trailing, 20)
            .padding(.top)
        }
    }
}

#Preview {
    VisualDataSectionView(viewModel: HomeViewModel())
        .padding()
        .background(Color(hex: "#FAFAFE") ?? Color.gray.opacity(0.05))
}
