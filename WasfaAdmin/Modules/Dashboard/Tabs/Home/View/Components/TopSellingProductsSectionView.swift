//
//  TopSellingProductsSectionView.swift
//  WasfaAdmin
//
//  Created by Apple on 28/06/2025.
//

import SwiftUI

/// Top Selling Products section with horizontal scrolling matching Android RecyclerView
struct TopSellingProductsSectionView: View {
    @ObservedObject var viewModel: HomeViewModel
   

    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            // Section title (matching Android)
            Text("Top Selling Products")
                .font(.custom("Roboto-Bold", size: 22))
                .foregroundColor(.black)
            
            // Horizontal scrolling products
            if viewModel.topSellingProducts.isEmpty {
                noDataView
            } else {
                horizontalProductsList
            }
        }
    }
    
    private var horizontalProductsList: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: 20) { // Matching Android marginEnd="20dp"
                ForEach(viewModel.topSellingProducts) { product in
                    TopSellingProductCardView(
                        product: product,
                        onProductTap: viewModel.handleProductTap
                    )
                }
            }
            .padding(.horizontal, 1) // Matching Android marginStart="1dp"
            .padding(.leading, 0) // Start from the edge
        }
        .scrollClipDisabled() // Allow cards to extend beyond scroll view bounds
    }
    
    private var noDataView: some View {
        VStack {
            Text("No top selling products available")
                .font(.custom("Roboto-Regular", size: 16))
                .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                .padding()
            
            Spacer()
        }
        .frame(height: 176) // Match card height for consistent spacing
    }
}

// #Preview {
//    // Preview removed due to complex model dependencies
// }
