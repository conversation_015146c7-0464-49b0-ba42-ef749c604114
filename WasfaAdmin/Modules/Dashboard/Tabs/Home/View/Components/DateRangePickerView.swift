//
//  DateRangePickerView.swift
//  WasfaAdmin
//
//  Created by Apple on 29/06/2025.
//

import SwiftUI
import OpenDateInterval
import DateRangePicker

struct DateRangePickerView: View {
    let onSave: (Date, Date) -> Void
    @Environment(\.dismiss) private var dismiss
    @State private var month = Calendar.current.component(.month, from: Date())
    @State private var year = Calendar.current.component(.year, from: Date())
    @State private var selection: OpenDateInterval?

    private let calendar = Calendar.current
    private let today = Date()

    var body: some View {
        VStack(spacing: 0) {
            VStack {
                HStack {
                    Button("Cancel") {
                        dismiss()
                    }
                    .padding()
                    .foregroundColor(.red)

                    Spacer()

                    Button("Submit") {
                        if let interval = selection,
                           let endDate = interval.end
                        {
                            onSave(interval.start, endDate)
                        }
                        dismiss()
                    }
                    .padding()
                    .foregroundColor(.blue)
                }

                .frame(height: 30)
//                .background(.orange)
                .padding(.top, 8)
                Divider()
                    .padding(.bottom)
            }

            // MrAsterisco DateRangePicker
            DateRangePicker(
                month: $month,
                year: $year,
                selection: $selection,
                minimumDate: calendar.date(byAdding: .year, value: -2, to: today),
                maximumDate: today
            )
            .tint(.doctorMain) // Apply tint directly to the DateRangePicker
            .accentColor(.doctorMain) // Fallback for older iOS versions
//            .frame(height: 350, alignment: .top)
        }
        .frame(height: 400.relativeHeight, alignment: .top)
        .tint(.doctorMain) // Also apply to the container for consistency
        .presentationDetents([.height(400.relativeHeight)])
    }
}
