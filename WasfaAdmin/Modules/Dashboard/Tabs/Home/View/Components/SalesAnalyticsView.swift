//
//  SalesAnalyticsView.swift
//  WasfaAdmin
//
//  Created by Apple on 28/06/2025.
//

import Charts
import SwiftUI
import UIKit

struct SalesAnalyticsView: View {
    let amount: Double
    let chartData: [ChartDataPoint]

    // MARK: - Enhanced Selection State (iOS 17+ chartXSelection)

    @State private var selectedDate: String?
    @State private var selectedDataPoint: ChartDataPoint?
    @State private var isInteractionEnabled: Bool = true

    // MARK: - Modern Chart State (iOS 18+)

    @State private var scrollPosition: String = ""
    @State private var visibleDataPoints: Int = 10
    @State private var showCrosshairs: Bool = false

    // MARK: - Performance Optimization

    private var optimizedVisibleDataPoints: Int {
        // Dynamically adjust visible points based on data size for optimal performance
        let dataCount = chartData.count
        if dataCount <= 10 {
            return dataCount
        } else if dataCount <= 50 {
            return min(15, dataCount)
        } else {
            return min(20, dataCount)
        }
    }

    @State private var selectedXPosition: CGFloat = 0
    @State private var selectedYPosition: CGFloat = 0

    @State private var domain: CGFloat = 5.0

    // Android gradient colors matching the doctor theme
    private var androidGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color("DoctorMainColor").opacity(0.3),
                Color("DoctorMainColor").opacity(0.1),
                Color.clear
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            // Sales Analytics title (matching Android)
            Text("Sales Analytics")
                .font(.custom("Roboto-Regular", size: 14))
                .foregroundColor(Color(hex: "#737D93") ?? Color.gray)

            // Amount display (matching Android)
            HStack {
                Text("KD \(amount.toString(decimalPlaces: 3))")
                    .font(.custom("Roboto-Medium", size: 24))
                    .foregroundColor(Color(hex: "#212325") ?? Color.black)

                Spacer()
            }

            // Chart section with loading state handling
            if chartData.isEmpty {
                // Show loading placeholder matching Android's progress bar approach
                chartLoadingView
            } else {
                VStack(spacing: 8) {
                    chartView
                }
            }

            // Selection info now shown in floating tooltip (CustomChartMarkerView)
        }
    }

    // Loading placeholder matching Android's approach
    private var chartLoadingView: some View {
        VStack {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: Color("DoctorMainColor")))
                .scaleEffect(1.2)

            Text("Loading chart data...")
                .font(.custom("Roboto-Regular", size: 12))
                .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                .padding(.top, 8)
        }
        .frame(height: 340) // Matching chart height
        .frame(maxWidth: .infinity)
    }

    private var chartView: some View {
        Chart(chartData) { dataPoint in
            chartMarks(for: dataPoint)
            
        }
        .scrollClipDisabled(true)
        .frame(maxWidth: .infinity)
        .frame(height: 340)
       
//        .clipped() // Prevent chart content from overflowing beyond boundaries

        // MARK: - Modern iOS 18 Chart APIs
//        .chartXVisibleDomain(length: optimizedVisibleDataPoints) // Optimized visible data range
//        .chartYVisibleDomain(length:optimizedVisibleDataPoints)
//        .chartScrollableAxes(.horizontal)
//        .chartXVisibleDomain(length: 10) // Show only 10 data points at a time
//        .chartScrollPosition(x: $scrollPosition)
        
        
//        .chartScrollTargetBehavior(.valueAligned(unit: 1)) // Snap-to-value behavior
        .chartXAxis {
            AxisMarks(values: .automatic) { value in
                AxisGridLine()
                AxisTick()
                AxisValueLabel {
                    if let stringValue = value.as(String.self) {
                        Text(stringValue.formatDateForChart())
                            .font(.custom("Roboto-Regular", size: 12))
                            .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
//                            .rotationEffect(.degrees(-45))
                           
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading, values: .stride(by: 0.1)) { value in
//                AxisGridLine(stroke: StrokeStyle(lineWidth: 1, dash: [10, 10])) // Matching Android dashed grid
//                    .foregroundStyle(Color.init(hex: "#E9ECEF") ?? Color.gray.opacity(0.3)) // Matching Android grid_color
                AxisTick()
                AxisValueLabel {
                    if let doubleValue = value.as(Double.self) {
                        Text("\(doubleValue, specifier: "%.1f")") // Matching Android 1 decimal format
                            .font(.custom("Roboto-Regular", size: 12))
                            .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                    }
                }
            }
        }
        .chartYScale(domain: 0 ... (chartData.map(\.value).max() ?? 2.0) + 0.5)

        // MARK: - Modern iOS 18 Chart Selection

        .chartXSelection(value: $selectedDate)
        .onChange(of: selectedDate) { _, newDate in
            handleModernSelection(selectedDate: newDate)
        }

        // MARK: - Performance Optimization

        .onAppear {
            // Initialize scroll position to show recent data
            if !chartData.isEmpty && scrollPosition.isEmpty {
                scrollPosition = chartData.first?.date ?? ""
            }
        }

        // MARK: - Accessibility Support

        .accessibilityElement(children: .combine)
        .accessibilityLabel("Sales Analytics Chart")
        .accessibilityValue(accessibilityDescription)
        .accessibilityAddTraits(.allowsDirectInteraction)
    }

    // MARK: - Chart Marks Helper

    @ChartContentBuilder
    private func chartMarks(for dataPoint: ChartDataPoint) -> some ChartContent {
        // Area fill to match Android gradient
        AreaMark(
            x: .value("Date", dataPoint.date),
            y: .value("Value", dataPoint.value)
        )
        .foregroundStyle(androidGradient)
        .interpolationMethod(.catmullRom)

        // Line mark with smooth curves
        LineMark(
            x: .value("Date", dataPoint.date),
            y: .value("Value", dataPoint.value)
        )
        .foregroundStyle(Color("DoctorMainColor"))
        .lineStyle(StrokeStyle(lineWidth: 2))
        .interpolationMethod(.catmullRom)

        // Enhanced selection markers with crosshairs
        if let selectedDataPoint = selectedDataPoint,
           selectedDataPoint.date == dataPoint.date
        {
            selectionMarks(for: dataPoint, selectedDataPoint: selectedDataPoint)
        }
    }

    @ChartContentBuilder
    private func selectionMarks(for dataPoint: ChartDataPoint, selectedDataPoint: ChartDataPoint) -> some ChartContent {
        // Vertical crosshair with tooltip annotation
        RuleMark(x: .value("Date", dataPoint.date))
            .foregroundStyle(Color("DoctorMainColor").opacity(0.8))
            .lineStyle(StrokeStyle(lineWidth: 1.5, dash: [6, 4]))

        // Horizontal crosshair (new feature)
        if showCrosshairs {
//            RuleMark(y: .value("Value", selectedDataPoint.value))
//                .foregroundStyle(Color("DoctorMainColor").opacity(0.6))
//                .lineStyle(StrokeStyle(lineWidth: 1, dash: [4, 3]))
        }

        // Enhanced selection point with glow effect
        PointMark(
            x: .value("Date", dataPoint.date),
            y: .value("Value", dataPoint.value)
        )
        .foregroundStyle(Color("DoctorMainColor"))
        .symbolSize(60)
        .symbol(.circle)
        .annotation(position: .top, spacing: 0) {
            CustomChartMarkerView(
                date: selectedDataPoint.date,
                value: selectedDataPoint.value
            )
            .transition(.scale.combined(with: .opacity))
            .padding(.bottom)
        }

        // Glow effect for selected point (separate mark)
        PointMark(
            x: .value("Date", dataPoint.date),
            y: .value("Value", dataPoint.value)
        )
        .foregroundStyle(Color("DoctorMainColor").opacity(0.3))
        .symbolSize(80)
        .symbol(.circle)
    }

    // MARK: - Modern iOS 18 Selection Methods

    /// Handles modern selection using chartXSelection (iOS 18+)
    private func handleModernSelection(selectedDate: String?) {
        guard let selectedDate = selectedDate,
              let dataPoint = chartData.first(where: { $0.date == selectedDate })
        else {
            // Clear selection
            selectedDataPoint = nil
            showCrosshairs = false
            return
        }

        // Update selection with smooth animation
//        withAnimation(.easeInOut(duration: 0.2)) {
        selectedDataPoint = dataPoint
        showCrosshairs = true
//        }

        // Professional haptic feedback
        let selectionFeedback = UISelectionFeedbackGenerator()
        selectionFeedback.selectionChanged()
    }

    // MARK: - Accessibility Support

    /// Provides accessibility description for the chart
    private var accessibilityDescription: String {
        if let selectedDataPoint = selectedDataPoint {
            return "Selected data point: \(selectedDataPoint.date) with value \(String(format: "%.1f", selectedDataPoint.value))"
        } else if chartData.isEmpty {
            return "Chart is loading"
        } else {
            let minValue = chartData.map(\.value).min() ?? 0
            let maxValue = chartData.map(\.value).max() ?? 0
            return "Chart showing \(chartData.count) data points from \(chartData.first?.date ?? "") to \(chartData.last?.date ?? ""), ranging from \(String(format: "%.1f", minValue)) to \(String(format: "%.1f", maxValue))"
        }
    }
}

#Preview("Modern iOS 18 Interactive Chart") {
    SalesAnalyticsView(
        amount: 1.250,
        chartData: [
            ChartDataPoint(date: "2025-05-01", value: 2),
            ChartDataPoint(date: "2025-05-02", value: 0),
            ChartDataPoint(date: "2025-05-03", value: 0),
            ChartDataPoint(date: "2025-05-04", value: 1),
            ChartDataPoint(date: "2025-05-05", value: 0),
            ChartDataPoint(date: "2025-05-06", value: 0),
            ChartDataPoint(date: "2025-05-07", value: 0),
            ChartDataPoint(date: "2025-05-08", value: 0),
            ChartDataPoint(date: "2025-05-09", value: 0),
            ChartDataPoint(date: "2025-05-10", value: 0),
            ChartDataPoint(date: "2025-05-11", value: 0),
            ChartDataPoint(date: "2025-05-12", value: 0),
            ChartDataPoint(date: "2025-05-13", value: 0),
            ChartDataPoint(date: "2025-05-14", value: 0),
            ChartDataPoint(date: "2025-05-15", value: 0),
            ChartDataPoint(date: "2025-05-16", value: 1),
            ChartDataPoint(date: "2025-05-17", value: 0),
            ChartDataPoint(date: "2025-05-18", value: 2),
            ChartDataPoint(date: "2025-05-19", value: 0),
            ChartDataPoint(date: "2025-05-20", value: 0),
            ChartDataPoint(date: "2025-05-21", value: 1),
            ChartDataPoint(date: "2025-05-22", value: 0),
            ChartDataPoint(date: "2025-05-23", value: 2),
            ChartDataPoint(date: "2025-05-24", value: 0),
            ChartDataPoint(date: "2025-05-25", value: 0),
            ChartDataPoint(date: "2025-05-26", value: 0),
            ChartDataPoint(date: "2025-05-27", value: 1),
            ChartDataPoint(date: "2025-05-28", value: 0),
            ChartDataPoint(date: "2025-05-29", value: 0),
            ChartDataPoint(date: "2025-05-30", value: 0),
            ChartDataPoint(date: "2025-05-31", value: 1)
        ]
    )
    .padding()
    .background(Color(hex: "#FAFAFE") ?? Color.gray.opacity(0.05))
}

#Preview("Loading State") {
    SalesAnalyticsView(
        amount: 0.000,
        chartData: [] // Empty data shows loading state
    )
    .padding()
    .background(Color(hex: "#FAFAFE") ?? Color.gray.opacity(0.05))
}
