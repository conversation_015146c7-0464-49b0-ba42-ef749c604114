//
//  FilterBarView.swift
//  WasfaAdmin
//
//  Created by Apple on 28/06/2025.
//

import DateRangePicker
import OpenDateInterval
import SwiftUI

/// Filter bar component matching Android card_filter_by_date exactly
struct FilterBarView: View {
    @Binding var selectedFilter: FilterOption?
    @Binding var showingDateFilter: Bool
    @Binding var customDateRange: String?
    @Binding var showingCustomDatePicker: Bool
    let onFilterChange: (FilterOption) -> Void
    let onResetFilter: () -> Void
    let onCustomDateRange: (Date, Date) -> Void

    init(
        selectedFilter: Binding<FilterOption?>,
        showingDateFilter: Binding<Bool>,
        customDateRange: Binding<String?>,
        showingCustomDatePicker: Binding<Bool>,
        onFilterChange: @escaping (FilterOption) -> Void,
        onResetFilter: @escaping () -> Void,
        onCustomDateRange: @escaping (Date, Date) -> Void
    ) {
        self._selectedFilter = selectedFilter
        self._showingDateFilter = showingDateFilter
        self._customDateRange = customDateRange
        self._showingCustomDatePicker = showingCustomDatePicker
        self.onFilterChange = onFilterChange
        self.onResetFilter = onResetFilter
        self.onCustomDateRange = onCustomDateRange
    }

    var body: some View {
        HStack(spacing: 0) {
            // Native SwiftUI Menu for dropdown functionality
            Menu {
                // Standard filter options
                ForEach(FilterOption.allCases.filter { $0 != .custom }, id: \.self) { option in
                    Button(action: {
                        onFilterChange(option)
                    }) {
                        HStack {
                            Text(option.rawValue)
                            if selectedFilter == option && customDateRange == nil {
                                Spacer()
                                Image(systemName: "checkmark.circle")
                                    .foregroundColor(.primary)
                            }
                        }
                    }
                }

                Divider()

                // Custom date range option
                Button(action: {
                    showingCustomDatePicker = true
                }) {
                    HStack {
                        Text("Custom Date Range")
                        if selectedFilter == .custom {
                            Spacer()
                            Image(systemName: "checkmark.circle")
                                .foregroundColor(.primary)
                        }
                    }
                }
            } label: {
                HStack(spacing: 10) { // Android layout_marginStart="10dp" between icon and text
                    // Filter icon - matching Android ImageView (25×25dp)
                    Image(systemName: "line.horizontal.3.decrease")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.black)
                        .frame(width: 25, height: 25) // Android layout_width="25dp", layout_height="25dp"

                    Text(filterDisplayText)
//
                        .foregroundColor(selectedFilter == nil && customDateRange == nil ?
                            .gray : .black)
                        .lineLimit(1) // Android singleLine="true"
                        .frame(maxWidth: .infinity, alignment: .leading)

                    // Dropdown arrow indicator - iOS native pattern
                    Image(systemName: "chevron.down")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.black)
                        .frame(width: 16, height: 16)
                }
                .padding(.leading, 16)
                .padding(.trailing, selectedFilter != nil ? 0 : 16) //
            }

            if selectedFilter != nil {
                Button(action: onResetFilter) {
                    Image(systemName: "arrow.trianglehead.counterclockwise.rotate.90")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.black)
                        .frame(width: 24, height: 24) // Android
                }
                .padding(.horizontal) // Prevent menu from triggering
            }
        }
        .frame(height: 48) // Android layout_height="48dp"
        .frame(maxWidth: .infinity)
        .background(Color.white) // Android cardBackgroundColor="@color/white"
        .clipShape(RoundedRectangle(cornerRadius: 25)) 
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1) // Android cardElevation="2dp"
        .padding(.horizontal, 1)
        .padding(.top,8)
        .sheet(isPresented: $showingCustomDatePicker) {
            DateRangePickerView(
                onSave: { startDate, endDate in
                    onCustomDateRange(startDate, endDate)
                }
            )
        }
    }

    private var filterDisplayText: String {
        if let customRange = customDateRange {
            return customRange
        }

        // Match Android behavior: show calculated date range when filter is applied
        switch selectedFilter {
        case  .today, .yesterday, .thisWeek, .last7Days, .last30Days, .thisMonth, .lastMonth:
            // Show the calculated date range like Android
            return selectedFilter?.dateString ?? "Filter by date"
        case .custom:
            return customDateRange ?? "Custom Date Range"
        case .none:
            return "Filter by date"
        }
    }
}



#Preview {
    ScrollView {
        VStack(spacing: 30) {
            // MARK: - Default State Preview

            VStack(alignment: .leading, spacing: 8) {
                Text("Default State")
                    .font(.headline)
                    .foregroundColor(.primary)

                Text("Shows 'Filter by date' with no active filter")
                    .font(.caption)
                    .foregroundColor(.secondary)

                FilterBarView(
                    selectedFilter: .constant(.today),
                    showingDateFilter: .constant(true),
                 
                    customDateRange: .constant(nil),
                    showingCustomDatePicker: .constant(false),
                    onFilterChange: { filter in
                        print("Filter changed to: \(filter.rawValue)")
                    },
                    onResetFilter: {
                        print("Filter reset")
                    },
                    onCustomDateRange: { startDate, endDate in
                        print("Custom date range: \(startDate) to \(endDate)")
                    }
                )
            }

            // MARK: - Interactive Demo Section

            VStack(alignment: .leading, spacing: 8) {
                Text("Interactive Demo")
                    .font(.headline)
                    .foregroundColor(.primary)

                Text("Fully functional FilterBarView for testing interactions")
                    .font(.caption)
                    .foregroundColor(.secondary)

                FilterBarViewDemo()
            }
        }
        .padding()
    }
    .background(Color(hex: "#FAFAFE") ?? Color.gray.opacity(0.05))
}

// MARK: - Interactive Demo Component

private struct FilterBarViewDemo: View {
    @State private var selectedFilter: FilterOption?
    @State private var showingDateFilter: Bool = false
    @State private var customDateRange: String? = nil
    @State private var showingCustomDatePicker: Bool = true

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            FilterBarView(
                selectedFilter: $selectedFilter,
                showingDateFilter: $showingDateFilter,
              
                customDateRange: $customDateRange,
                showingCustomDatePicker: $showingCustomDatePicker,
                onFilterChange: handleFilterChange,
                onResetFilter: handleResetFilter,
                onCustomDateRange: handleCustomDateRange
            )

            // Status display for demo purposes
            VStack(alignment: .leading, spacing: 4) {
                Text("Current State:")
                    .font(.caption)
                    .fontWeight(.semibold)

                Text("Selected Filter: \(selectedFilter?.rawValue)")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Text("Reset Visible: \(selectedFilter != nil ? "Yes" : "No")")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                if let customRange = customDateRange {
                    Text("Custom Range: \(customRange)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.top, 8)
        }
    }

    private func handleFilterChange(_ filter: FilterOption?) {
        selectedFilter = filter
        customDateRange = nil
    }

    private func handleResetFilter() {
        selectedFilter = .today
       
        customDateRange = nil
    }

    private func handleCustomDateRange(startDate: Date, endDate: Date) {
        selectedFilter = .custom
        

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd-MM-yyyy"
        let startString = dateFormatter.string(from: startDate)
        let endString = dateFormatter.string(from: endDate)

        customDateRange = "\(startString) to \(endString)"
    }
}
