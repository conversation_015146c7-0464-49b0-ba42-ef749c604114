//
//  CustomChartMarkerView.swift
//  WasfaAdmin
//
//  Created by Apple on 28/06/2025.
//

import SwiftUI

/// Custom chart marker view matching Android's CustomMarkerView exactly
struct CustomChartMarkerView: View {
    let date: String
    let value: Double
    
    var body: some View {
        VStack(spacing: 5) {
            // Date label (matching Android tvDate)
            Text(date)
                .font(.custom("Roboto-Bold", size: 13))
                .foregroundColor(.white)
            
            // Value label (matching Android tvValue)
            Text("Number of Rx : \(value.toString(decimalPlaces: 1))")
                .font(.custom("Roboto-Regular", size: 13))
                .foregroundColor(.white)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            // Professional mobile-optimized tooltip design
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.black)
                .shadow(color: Color.black.opacity(0.3), radius: 12, x: 0, y: 6)
        )
        .overlay(
            // Small triangle pointer pointing down to the data point
            Triangle()
                .fill(Color.black)
                .frame(width: 12, height: 6)
                .offset(y: 20)
        )
    }
}

/// Triangle shape for marker pointer
struct Triangle: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: rect.midX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.minY))
        path.closeSubpath()
        return path
    }
}

#Preview {
    CustomChartMarkerView(date: "06-28", value: 5)
        .padding()
        .background(Color.gray.opacity(0.1))
}
