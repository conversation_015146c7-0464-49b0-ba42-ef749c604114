//
//  SalesAnalyticsViewLegacy.swift
//  WasfaAdmin
//
//  Created by Apple on 29/06/2025.
//

import Charts
import SwiftUI

/// Legacy Sales Analytics view for iOS 16 compatibility
@available(iOS 16.0, *)
struct SalesAnalyticsViewLegacy: View {
    let amount: Double
    let chartData: [ChartDataPoint]
    @State private var selectedDataPoint: ChartDataPoint?
    @State private var isInteractionEnabled: Bool = true
    @State private var dragLocation: CGPoint = .zero
    @State private var isDragging: Bool = false
    @State private var selectedXPosition: CGFloat = 0
    @State private var selectedYPosition: CGFloat = 0

    @State private var domain: CGFloat = 5.0

    private var androidGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color("DoctorMainColor").opacity(0.3),
                Color("DoctorMainColor").opacity(0.1),
                Color.clear
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            // Sales Analytics title (matching Android)
            Text("Sales Analytics")
                .font(.custom("Roboto-Regular", size: 14))
                .foregroundColor(Color(hex: "#737D93") ?? Color.gray)

            // Amount display (matching Android)
            HStack {
                Text("KD \(amount.toString(decimalPlaces: 3))")
                    .font(.custom("Roboto-Medium", size: 24))
                    .foregroundColor(Color(hex: "#212325") ?? Color.black)

                Spacer()
            }

            // Chart section with loading state handling
            if chartData.isEmpty {
                // Show loading placeholder matching Android's progress bar approach
                chartLoadingView
            } else {
                VStack(spacing: 8) {
                    chartView
                }
            }

            // Selection info now shown in floating tooltip (CustomChartMarkerView)
        }
    }

    private var chartLoadingView: some View {
        VStack(spacing: 12) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: Color("DoctorMainColor")))
                .scaleEffect(1.2)

            Text("Loading chart data...")
                .font(.custom("Roboto-Regular", size: 12))
                .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                .padding(.top, 8)
        }
        .frame(height: 200)
        .frame(maxWidth: .infinity)
    }

    private var chartView: some View {
        Chart(chartData) { dataPoint in
            // Area fill to match Android gradient
            AreaMark(
                x: .value("Date", dataPoint.date),
                y: .value("Value", dataPoint.value)
            )
            .foregroundStyle(androidGradient)
            .interpolationMethod(.catmullRom)

            // Line mark with smooth curves
            LineMark(
                x: .value("Date", dataPoint.date),
                y: .value("Value", dataPoint.value)
            )
            .foregroundStyle(Color("DoctorMainColor"))
            .lineStyle(StrokeStyle(lineWidth: 2))
            .interpolationMethod(.catmullRom)

            // Selection point marker
            if let selectedDataPoint = selectedDataPoint,
               selectedDataPoint.date == dataPoint.date
            {
                RuleMark(x: .value("Date", dataPoint.date))
                    .foregroundStyle(Color("DoctorMainColor"))
                    .lineStyle(StrokeStyle(lineWidth: 1, dash: [4]))

                PointMark(
                    x: .value("Date", dataPoint.date),
                    y: .value("Value", dataPoint.value)
                )
                .foregroundStyle(Color("DoctorMainColor"))
                .symbolSize(50)
                .annotation(position: .top) {
                    CustomChartMarkerView(
                        date: selectedDataPoint.date,
                        value: selectedDataPoint.value
                    )
                }
            }
        }
        .frame(height: 200)
        .chartXAxis {
            AxisMarks(position: .bottom, values: .stride(by: .day)) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 1, dash: [10, 10])) // Matching Android dashed grid
                    .foregroundStyle(Color(hex: "#E9ECEF") ?? Color.gray.opacity(0.3)) // Matching Android grid_color
                AxisTick()
                AxisValueLabel {
                    if let stringValue = value.as(String.self) {
                        Text(stringValue.formatDateForChart())
                            .font(.custom("Roboto-Regular", size: 12))
                            .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading, values: .stride(by: 0.1)) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 1, dash: [10, 10])) // Matching Android dashed grid
                    .foregroundStyle(Color(hex: "#E9ECEF") ?? Color.gray.opacity(0.3)) // Matching Android grid_color
                AxisTick()
                AxisValueLabel {
                    if let doubleValue = value.as(Double.self) {
                        Text("\(doubleValue, specifier: "%.1f")") // Matching Android 1 decimal format
                            .font(.custom("Roboto-Regular", size: 12))
                            .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                    }
                }
            }
        }
        .chartYScale(domain: 0 ... (chartData.map(\.value).max() ?? 2.0) + 0.5) // Matching Android axisMinimum/Maximum
        .chartOverlay { chartProxy in
            GeometryReader { geometry in
                ZStack {
                    // Invisible interaction layer
                    Rectangle()
                        .fill(Color.clear)
                        .contentShape(Rectangle())
                        .gesture(
                            DragGesture(minimumDistance: 0)
                                .onChanged { value in
                                    handleContinuousTracking(at: value.location, chartProxy: chartProxy, geometry: geometry)
                                }
                                .onEnded { _ in
                                    handleDragEnd()
                                }
                        )
                }
            }
        }
    }

    /// Handles continuous tracking during drag gesture - industry standard for mobile charts
    private func handleContinuousTracking(at location: CGPoint, chartProxy: ChartProxy, geometry: GeometryProxy) {
        guard !chartData.isEmpty && isInteractionEnabled else { return }

        // Update drag state
        isDragging = true
        dragLocation = location

        // Get the actual plot area frame - this is the key to accurate positioning
        let plotAreaFrame = chartProxy.plotSize
        let geometryFrame = geometry.frame(in: .local)

        // Define chart content bounds (matching the visual overlay bounds)
        let chartLeftBound = geometryFrame.minX + 20 // Left margin for Y-axis
        let chartRightBound = geometryFrame.maxX - 20 // Right margin
        let chartContentWidth = chartRightBound - chartLeftBound

        // Constrain touch position to chart content bounds (don't return early)
        let constrainedTouchX = max(chartLeftBound, min(chartRightBound, location.x))

        // Calculate relative position within the chart content area using constrained touch
        let relativeX = (constrainedTouchX - chartLeftBound) / chartContentWidth
        let clampedRelativeX = max(0, min(1, relativeX))

        // Find the closest data point using professional interpolation
        let exactIndex = clampedRelativeX * Double(chartData.count - 1)
        let closestIndex = Int(round(exactIndex))
        let validIndex = max(0, min(chartData.count - 1, closestIndex))

        // Update selected data point with smooth tracking
        let newSelectedPoint = chartData[validIndex]

        // Calculate X position for the selected data point
        selectedXPosition = chartLeftBound + (clampedRelativeX * chartContentWidth)

        // Calculate Y position based on chart scale using geometry frame
        let maxValue = chartData.map(\.value).max() ?? 1.0
        let chartMaxValue = maxValue + 0.5 // Matching chartYScale domain
        let relativeY = 1.0 - (newSelectedPoint.value / chartMaxValue)
        selectedYPosition = geometryFrame.minY + (relativeY * geometryFrame.height)

        // Only update if we've moved to a different data point (reduces unnecessary updates)
        if selectedDataPoint?.date != newSelectedPoint.date {
            selectedDataPoint = newSelectedPoint

            // Subtle haptic feedback for data point changes (professional UX)
            let selectionFeedback = UISelectionFeedbackGenerator()
            selectionFeedback.selectionChanged()
        }
    }

    /// Handles the end of drag interaction
    private func handleDragEnd() {
        // Keep the tooltip visible for a moment after drag ends (better UX)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            withAnimation(.easeOut(duration: 0.3)) {
                isDragging = false
                selectedDataPoint = nil
            }
        }
    }
}

#Preview("Legacy iOS 16 Chart") {
    SalesAnalyticsViewLegacy(
        amount: 1.250,
        chartData: [
            ChartDataPoint(date: "2025-06-22", value: 0.2),
            ChartDataPoint(date: "2025-06-23", value: 1.1),
            ChartDataPoint(date: "2025-06-24", value: 1.8),
            ChartDataPoint(date: "2025-06-25", value: 1.2),
            ChartDataPoint(date: "2025-06-26", value: 1.7),
            ChartDataPoint(date: "2025-06-27", value: 1.9),
            ChartDataPoint(date: "2025-06-28", value: 1.4)
        ]
    )
    .padding()
    .background(Color(hex: "#FAFAFE") ?? Color.gray.opacity(0.05))
}
