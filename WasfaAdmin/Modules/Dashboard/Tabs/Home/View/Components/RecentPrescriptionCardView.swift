//
//  RecentPrescriptionCardView.swift
//  WasfaAdmin
//
//  Created by Apple on 28/06/2025.
//

import SwiftUI

/// Recent Prescription card component matching Android item_pres.xml exactly
struct RecentPrescriptionCardView: View {
    let prescription: Prescription
    let onViewDetails: (String) -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // Customer info section (matching Android LinearLayout)
            customerInfoSection
                .padding(.horizontal, 15) // Matching Android marginStart="5dp" + padding
                .padding(.top, 15) // Matching Android marginTop="5dp" + padding
            
            // Medications section (matching Android LinearLayout)
            medicationsSection
                .padding(.horizontal, 15)
                .padding(.top, 20) // Matching Android marginTop="20dp"
            
            Spacer()
            
            // View Details button (matching Android MaterialCardView)
            viewDetailsButton
                .padding(.horizontal, 10)
                .padding(.bottom, 10)
        }
        .frame(height: 212) // Matching Android layout_height="212dp"
        .background(Color.white)
        .cornerRadius(16) // Matching Android cardCornerRadius="16dp"
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1) // Matching Android cardElevation="2dp"
        .padding(.bottom, 10) // Matching Android marginBottom="10dp"
    }
    
    private var customerInfoSection: some View {
        HStack(spacing: 10) {
            // Customer profile image (matching Android ImageView 62dp x 62dp)
            customerProfileImage
            
            // Customer details
            VStack(alignment: .leading, spacing: 5) {
                // Customer name (matching Android TextView)
                Text(prescription.patientInfo.first?.name ?? "Unknown Customer")
                    .font(.custom("Roboto-Bold", size: 18))
                    .foregroundColor(.black)
                    .lineLimit(1)
                
                // Medical condition/description (matching Android TextView)
                Text(prescription.patientInfo.first?.phone ?? "")
                    .font(.custom("Roboto-Bold", size: 14))
                    .foregroundColor(Color(hex: "#B9B9B9") ?? Color.gray)
                    .lineLimit(1)
            }
            
            Spacer()
        }   
    }
    
    @ViewBuilder
    private var customerProfileImage: some View {
        if let profilePic = prescription.patientInfo.first?.profilePic, !profilePic.isEmpty {
            NetworkImageView(path: profilePic)
                .frame(width: 62, height: 62)
                .clipShape(Circle())
        } else {
            Image(systemName: "person.circle.fill") // Using SF Symbol as fallback
                .resizable()
                .scaledToFit()
                .foregroundStyle(.gray.gradient)
                .frame(width: 62, height: 62)
                .clipShape(Circle())
        }
    }
    
    private var medicationsSection: some View {
        HStack(spacing: 10) {
            Image(.tabMedications)
                .renderingMode(.template)
                .resizable()
                .foregroundStyle(.adminMain)
                .frame(width: 17.relativeFontSize, height: 17.relativeFontSize)
                .frame(width: 32.relativeFontSize, height: 32.relativeFontSize)
                .background(Color(red: 0.65, green: 0.11, blue: 0.36).opacity(0.09))
                .cornerRadius(10.relativeFontSize)
            
            // Medication details
            VStack(alignment: .leading, spacing: 2) {
                Text("Medications Prescribed")
                    .font(.custom("Roboto-Medium", size: 12))
                    .foregroundColor(Color(hex: "#99A61C5C") ?? Color.gray.opacity(0.6)) // Matching Android textColor="#99A61C5C"
                
                Text("\(prescription.medicationsPrescribed)")
                    .font(.custom("Roboto-Bold", size: 12))
                    .foregroundColor(Color.doctorMain) // Matching Android textColor="@color/doctor_main_color"
            }
            
            Spacer()
        }
    }
    
    private var viewDetailsButton: some View {
        Button {
            onViewDetails(prescription.prescriptionID)
        } label: {
            Text("View Details")
                .font(.custom("Roboto-Regular", size: 16))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 38) // Matching Android layout_height="38dp"
                .background(Color.doctorMain) // Matching Android cardBackgroundColor="@color/doctor_main_color"
                .cornerRadius(12) // Matching Android cardCornerRadius="12dp"
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ScrollView(.horizontal) {
        HStack(spacing: 20) {
            RecentPrescriptionCardView(
                prescription: Prescription(
                    id: "1", prescriptionID: "122",
                    medicationsPrescribed: 3,
                    patientInfo: [
                        PatientInfo(
                            phone: "+96512345678",
                            civilID: "123456789",
                            alternateNumber: nil,
                            id: 1,
                            dob: "1990-01-01",
                            profilePic: "https://example.com/profile.jpg",
                            email: "<EMAIL>",
                            type: "customer",
                            name: "Wasfa customer"
                        )
                    ],
                    name: "Prescription #1",
                    description: "Sinus Infection",
                    medications: []
                ),
                onViewDetails: { _ in }
            )
            
            RecentPrescriptionCardView(
                prescription: Prescription(
                    id: "2", prescriptionID: "134",
                    medicationsPrescribed: 1,
                    patientInfo: [
                        PatientInfo(
                            phone: "+96512345679",
                            civilID: "123456790",
                            alternateNumber: nil,
                            id: 2,
                            dob: "1985-05-15",
                            profilePic: "",
                            email: "<EMAIL>",
                            type: "customer",
                            name: "Another Customer"
                        )
                    ],
                    name: "Prescription #2",
                    description: "Headache",
                    medications: []
                ),
                onViewDetails: { _ in }
            )
        }
        .padding()
    }
    .background(Color(hex: "#FAFAFE") ?? Color.gray.opacity(0.05))
}
