//
//  ScrollProgressIndicator.swift
//  WasfaAdmin
//
//  Created by Apple on 27/06/2025.
//

import SwiftUI

struct ScrollProgressIndicator: View {
    let totalSteps: Int
    @Binding var currentStep: Int?

    private var visibleRange: Range<Int> {
        let start = max(0, min((currentStep ?? 0) - 1, totalSteps - 3))
        let end = min(start + 3, totalSteps)
        return start..<end
    }

    var body: some View {
        HStack(spacing: 4) {
            ForEach(visibleRange, id: \.self) { index in
                Capsule()
                    .fill(index == currentStep ? .doctorMain : .doctorMain.opacity(0.3))
                    .frame(width: index == currentStep ? 85 : 20, height: 6)
                    .animation(.easeInOut(duration: 0.2), value: currentStep)
            }
        }
    }
}


#Preview {
    ScrollProgressIndicator(totalSteps: 10, currentStep: .constant(3))
}
