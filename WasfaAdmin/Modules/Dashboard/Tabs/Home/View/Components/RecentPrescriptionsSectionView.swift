//
//  RecentPrescriptionsSectionView.swift
//  WasfaAdmin
//
//  Created by Apple on 28/06/2025.
//

import SwiftUI

/// Recent Prescriptions section with horizontal scrolling matching Android RecyclerView
struct RecentPrescriptionsSectionView: View {
   let recentPrescriptions: [Prescription]
   @ObservedObject var viewModel: HomeViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            // Section title (matching Android)
            Text("Recent Prescriptions")
                .font(.custom("Roboto-Bold", size: 22))
                .foregroundColor(.black)
            
            // Horizontal scrolling prescriptions
            if recentPrescriptions.isEmpty {
                noDataView
            } else {
                horizontalPrescriptionsList
            }
        }
    }
    
    private var horizontalPrescriptionsList: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: 15) { // Spacing between cards
                ForEach(recentPrescriptions, id: \.id) { prescription in
                    RecentPrescriptionCardView(
                        prescription: prescription,
                        onViewDetails: viewModel.handlePrescriptionTap
                    )
                    .frame(width: 300) // Fixed width for consistent card sizing
                }
            }
            .padding(.horizontal, 1) // Matching Android marginStart="1dp"
            .padding(.leading, 0) // Start from the edge
        }
        .scrollClipDisabled() // Allow cards to extend beyond scroll view bounds
    }
    
    private var noDataView: some View {
        VStack {
            Text("No recent prescriptions available")
                .font(.custom("Roboto-Regular", size: 16))
                .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                .padding()
            
            Spacer()
        }
        .frame(height: 212) // Match card height for consistent spacing
    }
    
   
}

//#Preview {
//    // Preview removed due to complex model dependencies
//}
