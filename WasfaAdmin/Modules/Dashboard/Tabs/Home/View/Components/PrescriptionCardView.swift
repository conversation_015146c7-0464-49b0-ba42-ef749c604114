//
//  PrescriptionStatCardView.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

struct PrescriptionCardView: View, Identifiable {
    let id: Int
    let title: String
    let value: String
    var isBoldValue: Bool = true

    var body: some View {
        VStack(alignment: .leading, spacing: 12.relativeHeight) {
            HStack(alignment: .top, spacing: 12.relativeWidth) {
                HStack {
                    Image(.homeGraph)
                        .resizable()
                        .scaledToFit()
                        .frame(width: 28.relativeFontSize, height: 28.relativeFontSize)
                        .foregroundColor(.pink)

                    Text(title)
                        .font(Font.custom("Poppins", size: 16).weight(.medium))
                        .foregroundColor(.black)
                }
            }
            Text(value)
                .font(.system(size: 18, weight: isBoldValue ? .bold : .semibold))
                .foregroundColor(.doctorMain)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .frame(width: 174.relativeWidth, height: 99.relativeHeight, alignment: .leading)
        .background(Color(red: 0.97, green: 0.97, blue: 0.97))
        .clipShape(RoundedRectangle(cornerRadius: 22))
        .shadow(color: .black.opacity(0.15), radius: 4, x: 0, y: 2)
        // add some drop shadow
    }
}

// show preview
#Preview {
    PrescriptionCardView(id: 0, title: "Total Prescriptions", value: "100")
}
