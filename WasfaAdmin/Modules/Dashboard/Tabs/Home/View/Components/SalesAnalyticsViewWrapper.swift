//
//  SalesAnalyticsViewWrapper.swift
//  WasfaAdmin
//
//  Created by Apple on 29/06/2025.
//

import Charts
import SwiftUI

/// Wrapper view that automatically selects the appropriate SalesAnalyticsView based on iOS version
struct SalesAnalyticsViewWrapper: View {
    let amount: Double
    let chartData: [ChartDataPoint]
    
    var body: some View {
        if #available(iOS 17.0, *) {
            // Use enhanced interactive chart for iOS 17+
            SalesAnalyticsView(amount: amount, chartData: chartData)
        } else if #available(iOS 16.0, *) {
            // Use legacy chart for iOS 16
            SalesAnalyticsViewLegacy(amount: amount, chartData: chartData)
        } else {
            // Fallback for older iOS versions
            VStack(alignment: .leading, spacing: 15) {
                Text("Sales Analytics")
                    .font(.custom("Roboto-Regular", size: 14))
                    .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                
                HStack {
                    Text("KD \(amount.toString(decimalPlaces: 3))")
                        .font(.custom("Roboto-Medium", size: 24))
                        .foregroundColor(Color(hex: "#212325") ?? Color.black)
                    
                    Spacer()
                }
                
                VStack(spacing: 12) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.system(size: 48))
                        .foregroundColor(Color("DoctorMainColor").opacity(0.6))
                    
                    Text("Charts require iOS 16 or later")
                        .font(.custom("Roboto-Regular", size: 14))
                        .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                        .multilineTextAlignment(.center)
                }
                .frame(height: 200)
                .frame(maxWidth: .infinity)
            }
        }
    }
}

#Preview("Wrapper - iOS 17+") {
    SalesAnalyticsViewWrapper(
        amount: 1.250,
        chartData: [
            ChartDataPoint(date: "2025-06-22", value: 0.2),
            ChartDataPoint(date: "2025-06-23", value: 1.1),
            ChartDataPoint(date: "2025-06-24", value: 1.8),
            ChartDataPoint(date: "2025-06-25", value: 1.2),
            ChartDataPoint(date: "2025-06-26", value: 1.7),
            ChartDataPoint(date: "2025-06-27", value: 1.9),
            ChartDataPoint(date: "2025-06-28", value: 1.4)
        ]
    )
    .padding()
    .background(Color(hex: "#FAFAFE") ?? Color.gray.opacity(0.05))
}

#Preview("Wrapper - Loading State") {
    SalesAnalyticsViewWrapper(
        amount: 0.000,
        chartData: []
    )
    .padding()
    .background(Color(hex: "#FAFAFE") ?? Color.gray.opacity(0.05))
}
