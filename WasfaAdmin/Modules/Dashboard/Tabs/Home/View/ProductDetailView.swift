//
//  ProductDetailView.swift
//  WasfaAdmin
//
//  Created by Apple on 29/06/2025.
//

import SwiftUI

struct ProductDetailView: View {
    let productId: Int
    @StateObject private var viewModel = ProductDetailViewModel()
    
    
    var body: some View {
        SuperView(viewModel: viewModel) {
            MainScrollBody(backButtonWithTitle: "Product Details") {
                VStack(spacing: 24) {
                    if let productDetail = viewModel.productDetail {
                        // MARK: - Product Image Section
                        
                        productImageSection(productDetail)
                        
                        // MARK: - Product Info Section
                        
                        productInfoSection(productDetail)
                        
                        // MARK: - Description Section
                        
                        descriptionSection(productDetail)
                        
                        // MARK: - Add to Cart Section
                        
//                        addToCartSection(productDetail)
                        
                    }
//                    else if viewModel.isLoading {
//                        // MARK: - Loading State
//                        
//                        loadingView
//                        
//                    } else {
//                        // MARK: - Error State
//                        
//                        errorView
//                    }
                    
                    Spacer()
                }
                .padding()
            }
            .scrollIndicators(.hidden)
            
        }
        .onLoad { viewModel.loadProductDetails(productId: productId)}
        
    }
    
    // MARK: - Product Image Section
    
    private func productImageSection(_ product: ProductDetailsModel) -> some View {
        VStack(spacing: 16) {
            NetworkImageView(path: product.thumbnailImage)
                .frame(width: 200, height: 200)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
            
            // Rating and Stock Status
            HStack(spacing: 20) {
                // Rating
                HStack(spacing: 4) {
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                        .font(.system(size: 14))
                    
                    Text("\(product.rating)")
                        .font(.custom("Roboto-Medium", size: 14))
                        .foregroundColor(.black)
                }
                
                Spacer()
                
                // Stock Status
                Text(product.stockStatus)
                    .font(.custom("Roboto-Medium", size: 14))
                    .foregroundColor(product.isInStock ? .green : .red)
            }
        }
    }
    
    // MARK: - Product Info Section
    
    private func productInfoSection(_ product: ProductDetailsModel) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // Product Name
            Text(product.name)
                .font(.custom("Roboto-Bold", size: 20))
                .foregroundColor(.black)
                .multilineTextAlignment(.leading)
            
            
            // Price Section
            HStack(spacing: 12) {
                Text(product.unitPrice)
                    .font(.custom("Roboto-Bold", size: 24))
                    .foregroundColor(.doctorMain)
                
                if product.hasDiscount {
                    Text(product.strikedPrice)
                        .font(.custom("Roboto-Medium", size: 16))
                        .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
                        .strikethrough()
                    
                    Text("\(product.discountPercentage)")
                        .font(.custom("Roboto-Bold", size: 14))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.red)
                        .cornerRadius(4)
                }
                
                Spacer()
            }
            
            // Brand
            Text("Brand: \(Text(product.brand).foregroundStyle(Color(hex: "#737D93") ?? .gray))")
                .font(.custom("Roboto-Medium", size: 16))
                .foregroundColor(.blue300)
            
            // Seller
            Text("Seller: \(Text(product.seller).foregroundStyle(Color(hex: "#737D93") ?? .gray))")
                .font(.custom("Roboto-Medium", size: 16))
                .foregroundColor(.blue300)
            
           
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Description Section
    
    private func descriptionSection(_ product: ProductDetailsModel) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Description")
                .font(.custom("Roboto-Bold", size: 18))
                .foregroundColor(.black)
            
            // HTML description rendered as text (simplified)
            if let description = product.description {
                CustomRichText(html: description, fontFamily: "RobotoRegular", fontSrc: "RobotoRegular.ttf", sizeAdjust: "100", fontColor: Color(hex: "#737D93") ?? Color.gray, lineHeight: 110.0.relativeFontSize)
            }
            
            
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Add to Cart Section
    
    private func addToCartSection(_ product: ProductDetailsModel) -> some View {
        VStack(spacing: 16) {
            Button(action: {
                // Handle add to cart action
                handleAddToCart(product)
            }) {
                Text("Add to Cart")
                    .font(.custom("Roboto-Bold", size: 16))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(product.isInStock ? Color.blue : Color.gray)
                    .cornerRadius(8)
            }
            .disabled(!product.isInStock)
            .opacity(product.isInStock ? 1.0 : 0.5)
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Loading product details...")
                .font(.custom("Roboto-Medium", size: 16))
                .foregroundColor(Color(hex: "#737D93") ?? Color.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 100)
    }
    
    // MARK: - Error View
    
    private var errorView: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.red)
            
            Text("Failed to load product details")
                .font(.custom("Roboto-Medium", size: 16))
                .foregroundColor(.black)
            
            Button("Retry") {
                viewModel.loadProductDetails(productId: productId)
            }
            .font(.custom("Roboto-Bold", size: 16))
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.blue)
            .cornerRadius(8)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 100)
    }
    
    // MARK: - Actions
    
    private func handleAddToCart(_ product: ProductDetailsModel) {
        // TODO: Implement add to cart functionality
        Logger.info("Add to cart tapped for product: \(product.name)", tag: "ProductDetailView")
    }
}

// MARK: - String Extension for HTML

extension String {
    func htmlToString() -> String {
        // Simple HTML tag removal - in a real app, you might want to use a proper HTML parser
        return self.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression, range: nil)
            .replacingOccurrences(of: "&nbsp;", with: " ")
            .replacingOccurrences(of: "&amp;", with: "&")
            .replacingOccurrences(of: "&lt;", with: "<")
            .replacingOccurrences(of: "&gt;", with: ">")
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

#Preview {
    NavigationStack{
        ProductDetailView(productId: 83417)
            .attachAllEnvironmentObjects()
    }
}
