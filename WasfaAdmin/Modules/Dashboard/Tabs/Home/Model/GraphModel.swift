//
//  GraphModel.swift
//  WasfaAdmin
//
//  Created by Apple on 28/06/2025.
//

import Foundation

// MARK: - GraphResponse
struct GraphResponse: Codable {
    let numOfSaleValue: [Int]
    let borderColor: [String]
    let totalPrescriptions: Int
    let currentMonthAmount, pendingCommission, amount: String
    let countOfSale: Int
    let refundAmount: String
    let backgroundColor, weekdays: [String]
    let completedPayment: Double
    let refundOrders: Int
    let mostlyPrescibed: String

    enum CodingKeys: String, CodingKey {
        case numOfSaleValue = "num_of_sale_value"
        case borderColor, totalPrescriptions, currentMonthAmount, pendingCommission, amount, countOfSale, refundAmount, backgroundColor, weekdays, completedPayment, refundOrders, mostlyPrescibed
    }
}

// MARK: - GraphRequest
struct GraphRequest: Codable {
    let date: String
}
