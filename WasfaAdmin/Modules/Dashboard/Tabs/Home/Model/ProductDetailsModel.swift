//
//  ProductDetailsModel.swift
//  WasfaAdmin
//
//  Created by Apple on 29/06/2025.
//

import Foundation
// MARK: - ProductDetailsModel
struct ProductDetailsModel: Codable {
    let id: Int
    let discount: Int
    let currentStock: Int
    let thumbnailImage: String?
    let strikedPrice: String
    let rating: Int
    let description, shortDescription: String?
    let hasDiscount: Bool
    let brand: String
    let relatedProducts, upSellProducts, crossSellProducts: [ProductDetailsModel]
    let reviewCount: Int
    let reviews: [VoidStruct]
    let photos: [Photo]
    let seller, name, unitPrice: String
    
    
  
    
    /// Whether the product is in stock
    var isInStock: Bool {
        return Int(currentStock) > 0
    }
    
    /// Stock status for display
    var stockStatus: String {
        let stock = Int(currentStock)
        return stock > 0 ? "\(stock) in stock" : "Out of Stock"
    }
    
    /// Rating as a Double for star display
    var ratingValue: Double {
        return Double(rating)
    }
    
    /// Formatted discount percentage
    var discountPercentage: String {
        return hasDiscount ? "\(discount)" : ""
    }
    
    
    // MARK: - Static Sample Data for Preview/Testing
    
//    static var sampleProductDetails: ProductDetailsModel {
//        ProductDetailsModel(
//            id: "101",
//            name: "Sample Medicine 250mg Tablets - High Quality Pharmaceutical Product",
//            seller: "Wasfa Pharmacy",
//            thumbnailImage: "https://apixrx.com/public/uploads/all/R15513-1.jpg",
//            hasDiscount: "true",
//            discount: "10%",
//            strikedPrice: "2.000",
//            unitPrice: "1.800",
//            currentStock: "25",
//            rating: "4.5",
//            description: "<p>This is a high-quality pharmaceutical product designed to provide effective treatment. It contains active ingredients that have been clinically tested and proven safe for use.</p><p><strong>Key Features:</strong></p><ul><li>Fast-acting formula</li><li>Clinically tested</li><li>Safe for daily use</li><li>No side effects when used as directed</li></ul>",
//            shortDescription: "High-quality pharmaceutical product for effective treatment",
//            brand: "MediCorp"
//        )
//    }
}

// MARK: - Photo
struct Photo: Codable {
    let path: String
}

//struct ProductDetailsModel: Codable, Identifiable {
//    let id: String
//    let name: String
//    let seller: String
//    let thumbnailImage: String
//    let hasDiscount: String
//    let discount: String
//    let strikedPrice: String
//    let unitPrice: String
//    let currentStock: String
//    let rating: String
//    let description: String
//    let shortDescription: String
//    let brand: String
//    
//    enum CodingKeys: String, CodingKey {
//        case id, name, seller
//        case thumbnailImage = "thumbnailImage"
//        case hasDiscount, discount, strikedPrice, unitPrice, currentStock, rating, description, shortDescription, brand
//    }
//    
//    // MARK: - Computed Properties for UI
//    
//    /// Whether the product has a discount
//    var hasDiscountBool: Bool {
//        return hasDiscount.lowercased() == "true"
//    }
//    
//    /// Whether the product is in stock
//    var isInStock: Bool {
//        return Int(currentStock) ?? 0 > 0
//    }
//    
//    /// Stock status for display
//    var stockStatus: String {
//        let stock = Int(currentStock) ?? 0
//        return stock > 0 ? "\(stock) in stock" : "Out of Stock"
//    }
//    
//    /// Rating as a Double for star display
//    var ratingValue: Double {
//        return Double(rating) ?? 0.0
//    }
//    
//    /// Formatted discount percentage
//    var discountPercentage: String {
//        return hasDiscountBool ? discount : ""
//    }
//    
//    /// Product ID as Int for navigation
//    var productId: Int {
//        return Int(id) ?? 0
//    }
//    
//    // MARK: - Static Sample Data for Preview/Testing
//    
//    static var sampleProductDetails: ProductDetailsModel {
//        ProductDetailsModel(
//            id: "101",
//            name: "Sample Medicine 250mg Tablets - High Quality Pharmaceutical Product",
//            seller: "Wasfa Pharmacy",
//            thumbnailImage: "https://apixrx.com/public/uploads/all/R15513-1.jpg",
//            hasDiscount: "true",
//            discount: "10%",
//            strikedPrice: "2.000",
//            unitPrice: "1.800",
//            currentStock: "25",
//            rating: "4.5",
//            description: "<p>This is a high-quality pharmaceutical product designed to provide effective treatment. It contains active ingredients that have been clinically tested and proven safe for use.</p><p><strong>Key Features:</strong></p><ul><li>Fast-acting formula</li><li>Clinically tested</li><li>Safe for daily use</li><li>No side effects when used as directed</li></ul>",
//            shortDescription: "High-quality pharmaceutical product for effective treatment",
//            brand: "MediCorp"
//        )
//    }
//}

// MARK: - Product Details Request Parameters

struct ProductDetailsRequest: Codable {
    let productId: String
    
    init(productId: Int) {
        self.productId = String(productId)
    }
    
    init(productId: String) {
        self.productId = productId
    }
}
