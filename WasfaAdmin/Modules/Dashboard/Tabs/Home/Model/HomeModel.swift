//
//  HomeModel.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import Foundation

// MARK: - HomeModel

struct HomeModel: Codable {
    let amount: Double
    let countOfSale, totalPrescriptions, refundAmount, currentMonthAmount: Int
    let prescriptions: [Prescription]
    let topSellingProducts: [TopSellingProduct]
    let mostlyPrescibed: String
    let completedPayment: Double
    let refundOrders: Int
    let pendingCommission: Double
}

// MARK: - Prescription

struct Prescription: Codable {
    var id: String = UUID().uuidString
    let prescriptionID: String
    let medicationsPrescribed: Int
    let patientInfo: [PatientInfo]
    let name: String
    let description: String?
    let medications: [Medication]

    enum CodingKeys: String, CodingKey {
        case prescriptionID = "id"
        case medicationsPrescribed
        case patientInfo
        case name
        case description
        case medications
    }
}

// MARK: - Medication

struct Medication: Codable {
    let doseTime: String?
    let dosage, productName: String
    let description: String?
    let duration: String

    enum CodingKeys: String, CodingKey {
        case doseTime = "dose_time"
        case dosage
        case productName = "product_name"
        case description, duration
    }
}

// MARK: - PatientInfo

struct PatientInfo: Codable {
    let phone: String
    let civilID: String?
    let alternateNumber: String?
    let id: Int
    let dob: String?
    let profilePic: String
    let email: String?
    let type, name: String

    enum CodingKeys: String, CodingKey {
        case phone
        case civilID = "civilId"
        case alternateNumber, id, dob, profilePic, email, type, name
    }
}

// MARK: - TopSellingProduct

struct TopSellingProduct: Codable, Identifiable {
    let publishedOnPos: Int
    let thumbnailImage: String?
    let id, numberOfsales: Int
    let sellerSku: String?
    let restricted, onRequest, sellerDiscount: Int
    let purchasePrice: String
    let isFavourite: Bool
    let approved, featured, seller: Int
    let cog, name, createdAt: String
    let enable, isCompleted: Int
    let unitPrice: String
    let published, currentStock, isPharma: Int
    let apixMargin: String
    let isClean: Int
    let basePrice: String
    let sellingPrice: Double
    let todaysDeal: Int
    let sellerName, apixSku, discount: String
    let rating, publishedOnWebsite, influencerrMargin: Int

    enum CodingKeys: String, CodingKey {
        case publishedOnPos
        case thumbnailImage = "thumbnail_image"
        case id, numberOfsales, sellerSku
        case restricted = "Restricted"
        case onRequest, sellerDiscount, purchasePrice, isFavourite, approved, featured, seller, cog, name, createdAt, enable
        case isCompleted = "is_completed"
        case unitPrice, published, currentStock, isPharma, apixMargin, isClean, basePrice
        case sellingPrice = "selling_price"
        case todaysDeal, sellerName, apixSku, discount, rating, publishedOnWebsite, influencerrMargin
    }

    static var dummyData: Self {
        TopSellingProduct(
            publishedOnPos: 1,
            thumbnailImage: "https://example.com/image.jpg",
            id: 101,
            numberOfsales: 150,
            sellerSku: "TSKU-001",
            restricted: 0,
            onRequest: 0,
            sellerDiscount: 5,
            purchasePrice: "120.00",
            isFavourite: true,
            approved: 1,
            featured: 0,
            seller: 23,
            cog: "110.00",
            name: String(repeating: "Sample Medicine 250mg", count: 10),
            createdAt: "2025-06-28T12:00:00Z",
            enable: 1,
            isCompleted: 1,
            unitPrice: "₹150.00",
            published: 1,
            currentStock: 50,
            isPharma: 1,
            apixMargin: "10.0",
            isClean: 1,
            basePrice: "140.00",
            sellingPrice: 150.0,
            todaysDeal: 0,
            sellerName: "Wasfa Sellers",
            apixSku: "APIX-XYZ-001",
            discount: "10%",
            rating: 5,
            publishedOnWebsite: 1,
            influencerrMargin: 3
        )
    }
}


public enum FilterOption: String, CaseIterable, Equatable {
    case today = "Today"
    case yesterday = "Yesterday"
    case thisWeek = "This Week"
    case last7Days = "Last 7 Days"
    case last30Days = "Last 30 Days"
    case thisMonth = "This Month"
    case lastMonth = "Last Month"
    case custom = "Custom Range"

    // Calculate date range string exactly like Android implementation
    public var dateString: String {
        let dateFormat = DateFormatter()
        dateFormat.dateFormat = "dd-MM-yyyy" // Android format: SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
        var calendar = Calendar.current

        switch self {
        case .today:
            // Android: date = dateFormat.format(calendar.time)
            let date = dateFormat.string(from: Date())
            return "\(date) to \(date)"

        case .yesterday:
            // Android: calendar.add(Calendar.DAY_OF_YEAR, -1)
            calendar = Calendar.current
            let yesterday = calendar.date(byAdding: .day, value: -1, to: Date()) ?? Date()
            let date = dateFormat.string(from: yesterday)
            return "\(date) to \(date)"

        case .thisWeek:
            // Note: Android doesn't have "This Week", but we'll implement it as current week
            let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: Date())?.start ?? Date()
            let endOfWeek = Date()
            let start = dateFormat.string(from: startOfWeek)
            let end = dateFormat.string(from: endOfWeek)
            return "\(start) to \(end)"

        case .last7Days:
            // Android: calendar.add(Calendar.DAY_OF_YEAR, -6) (7 days including today)
            let end = dateFormat.string(from: Date())
            let startDate = calendar.date(byAdding: .day, value: -6, to: Date()) ?? Date()
            let start = dateFormat.string(from: startDate)
            return "\(start) to \(end)"

        case .last30Days:
            // Android: calendar.add(Calendar.DAY_OF_YEAR, -29) (30 days including today)
            let end = dateFormat.string(from: Date())
            let startDate = calendar.date(byAdding: .day, value: -29, to: Date()) ?? Date()
            let start = dateFormat.string(from: startDate)
            return "\(start) to \(end)"

        case .thisMonth:
            // Android: calendar.set(Calendar.DAY_OF_MONTH, 1)
            let startOfMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: Date())) ?? Date()
            let endOfMonth = Date()
            let start = dateFormat.string(from: startOfMonth)
            let end = dateFormat.string(from: endOfMonth)
            return "\(start) to \(end)"

        case .lastMonth:
            // Android: calendar.add(Calendar.MONTH, -1); calendar.set(Calendar.DAY_OF_MONTH, 1)
            let lastMonth = calendar.date(byAdding: .month, value: -1, to: Date()) ?? Date()
            let startOfLastMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: lastMonth)) ?? Date()
            let endOfLastMonth = calendar.date(byAdding: .day, value: -1, to: calendar.date(from: calendar.dateComponents([.year, .month], from: Date())) ?? Date()) ?? Date()
            let start = dateFormat.string(from: startOfLastMonth)
            let end = dateFormat.string(from: endOfLastMonth)
            return "\(start) to \(end)"

        case .custom:
            return "" // Custom ranges are handled separately
        }
    }
}
