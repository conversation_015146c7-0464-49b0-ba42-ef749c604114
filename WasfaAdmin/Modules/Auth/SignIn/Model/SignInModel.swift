//
//  SignInModel.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import Foundation

// MARK: - User Session Data
public struct UserSession: Codable, Equatable, Sendable {
    public let user: User
    public let token: String
    public let loginTimestamp: Date
    
    public init(user: User, token: String, loginTimestamp: Date = Date()) {
        self.user = user
        self.token = token
        self.loginTimestamp = loginTimestamp
    }
    
    public var isInfluencer: Bool {
        return user.userType == UserType.influencer.rawValue
    }
    
    public var isAdmin: Bool {
        return user.userType == UserType.admin.rawValue
    }
    
    public var authorizationHeader: String {
        return "Bearer \(token)"
    }
}

// MARK: - Remember Me Data
public struct RememberMeData: Codable, Equatable, Sendable {
    public let email: String
    public let password: String
    public let isEnabled: Bool
    
    public init(email: String, password: String, isEnabled: Bool) {
        self.email = email
        self.password = password
        self.isEnabled = isEnabled
    }
}


public struct User: Codable, Equatable, Sendable {
    public let id: String
    public let phone: String
    public let name: String
    public let email: String
    public let userType: String

    
    private enum CodingKeys: String, CodingKey {
        case id, phone, name, email
        case userType = "user_type"
    }
}


// MARK: - User Type Enum
public enum UserType: String, CaseIterable, Sendable {
    case influencer = "influencer"
    case admin = "admin"
    
    public var displayName: String {
        switch self {
        case .influencer:
            return "Influencer"
        case .admin:
            return "Admin"
        }
    }
}
