//
//  SignInViewModel.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI
import Combine

class SignInViewModel: SuperViewModel {
    // MARK: - Memory-Safe Published Properties

    @Published var email: String = "" {
        didSet {
            // Direct synchronous call to avoid Task retain cycles
            emailChanged(oldValue: oldValue, newValue: email)
        }
    }

    @Published var password: String = "" {
        didSet {
            // Direct synchronous call to avoid Task retain cycles
            passwordChanged(oldValue: oldValue, newValue: password)
        }
    }

    @Published var rememberMe: Bool = false
    @Published private(set) var errorMessage: String?

    // MARK: - Task Management for Memory Safety

    /// Track ongoing login task for proper cancellation
    private var loginTask: Task<Void, Never>?

    override init() {
        super.init()
        self.assignTestCredentials()
        Logger.debug("✅ SignInViewModel initialized with memory-safe patterns", tag: "SignInViewModel")
    }

    deinit {
        Logger.debug("🧹 SignInViewModel deinit started", tag: "SignInViewModel")

        // Cancel ongoing login task (<PERSON> handles automatic cancellation)
        loginTask?.cancel()
        loginTask = nil

        Logger.debug("✅ SignInViewModel deinit completed", tag: "SignInViewModel")
    }
    

    // MARK: - Memory-Safe Methods

    func assignTestCredentials() {
        email = "<EMAIL>"
        password = "99462210"
    }

    var isLoginButtonDisabled: Bool {
        email.isEmpty || password.isEmpty
    }

    /// Memory-safe email change handler
    private func emailChanged(oldValue: String, newValue: String) {
        // Implement email validation logic here if needed
        Logger.debug("📧 Email changed from '\(oldValue)' to '\(newValue)'", tag: "SignInViewModel")
    }

    /// Memory-safe password change handler
    private func passwordChanged(oldValue: String, newValue: String) {
        // Implement password validation logic here if needed
        Logger.debug("🔒 Password changed (length: \(newValue.count))", tag: "SignInViewModel")
    }

    func rememberMeToggled() {
        rememberMe.toggle()
    }

    func forgotPasswordTapped() {
        // Implement forgot password logic
        Logger.info("🔄 Forgot password tapped", tag: "SignInViewModel")
    }

    /// Memory-safe login button handler with proper task management
    func loginButtonTapped() {
        // Cancel any existing login task
        loginTask?.cancel()

        // Create new login task with weak self
        loginTask = Task { [weak self] in
            guard let self = self else {
                Logger.warning("⚠️ SignInViewModel deallocated during login", tag: "SignInViewModel")
                return
            }

            await self.performLogin()
        }
    }

    /// Memory-safe login implementation with proper weak self usage
    private func performLogin() async {
        // Check if task was cancelled before starting
        guard !Task.isCancelled else {
            Logger.warning("⚠️ Login task cancelled before execution", tag: "SignInViewModel")
            return
        }

        // Capture values to avoid accessing self in closures
        let emailValue = email
        let passwordValue = password

        let parameters: [String: Any] = [
            "email": emailValue,
            "password": passwordValue
        ]

        // Use weak self in all closures to prevent retain cycles
        await onApiCall(
            { [weak self] in
                guard let self = self else {
                    throw NSError(domain: "SignInViewModel", code: -1, userInfo: [NSLocalizedDescriptionKey: "ViewModel deallocated"])
                }
                return try await self.api.login(parameters: parameters)
            },
            onSuccess: { [weak self] response in
                guard let self = self else {
                    Logger.warning("⚠️ SignInViewModel deallocated during login success", tag: "SignInViewModel")
                    return
                }

                // Handle successful login synchronously to avoid Task retain cycles
                self.errorMessage = nil

                // Handle Remember Me preference for token storage
                self.handleRememberMeTokenStorage()
                self.appState?.updateSelectedTab(.home)
                self.appState?.updateInitialScreen(.dashboard)
                self.appState?.showToast(.init(type: .success, message: response.message))
                Logger.info("✅ Login successful, navigating to dashboard", tag: "SignInViewModel")
            },
            onFailure: { [weak self] error in
                guard let self = self else {
                    Logger.warning("⚠️ SignInViewModel deallocated during login failure", tag: "SignInViewModel")
                    return
                }

                // Handle login failure synchronously to avoid Task retain cycles
                self.errorMessage = error
                Logger.error("❌ Login failed: \(error)", tag: "SignInViewModel")
            }
        )
    }

    // MARK: - Remember Me Token Management

    /// Handles token storage based on Remember Me preference
    private func handleRememberMeTokenStorage() {
        // Get the current token from the existing storage (temporarily stored by BaseAPI)
        if let currentToken = UserDefaultsSecure.sharedInstance.getGeneratedToken() {
            // Apply the user's Remember Me preference through TokenManager
            TokenManager.shared.setToken(currentToken, rememberMe: rememberMe)
            Logger.info("🔐 Token stored with Remember Me preference: \(rememberMe)", tag: "SignInViewModel")

            // If Remember Me is disabled, we need to clear the persistent storage
            // since TokenManager will handle temporary storage
            if !rememberMe {
                UserDefaultsSecure.sharedInstance.setGeneratedTokenStringValue(value: nil)
                Logger.info("🧹 Cleared persistent storage for temporary session", tag: "SignInViewModel")
            }
        } else {
            Logger.warning("⚠️ No token found to apply Remember Me preference", tag: "SignInViewModel")
        }
    }

    // MARK: - Memory Safety Utilities

    /// Cancel any ongoing operations (called when view disappears)
    func cancelOngoingOperations() {
        loginTask?.cancel()
        loginTask = nil
        Logger.debug("🚫 SignInViewModel operations cancelled", tag: "SignInViewModel")
    }

    /// Reset form state (memory-safe)
    func resetForm() {
        Task { @MainActor [weak self] in
            guard let self = self else { return }
            self.email = ""
            self.password = ""
            self.rememberMe = false
            self.errorMessage = nil
            Logger.debug("🔄 SignInViewModel form reset", tag: "SignInViewModel")
        }
    }

    /// Validate current state without retain cycles
    func validateCurrentState() -> Bool {
        return !email.isEmpty && !password.isEmpty
    }
}
