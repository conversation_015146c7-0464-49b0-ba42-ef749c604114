//
//  SignInView.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

struct SignInView: View {
    @StateObject private var viewModel = SignInViewModel()

    var body: some View {
        
        SuperView(viewModel: viewModel) {
            ScrollView {
                VStack(spacing: 16) {
                    // Static header/logo view
                    HeaderView()
                    // Email Field
                    CustomInputField(
                        title: "Email Address",
                        text: $viewModel.email,
                        placeholder: "Enter your email address",
                        cornerRadius: 24.relativeFontSize,
                        height: 52.relativeHeight
                    )
                    .accessibilityHint("Enter the email address associated with your account.")

                    // Password Field
                    CustomInputField(
                        title: "Password",
                        text: $viewModel.password,
                        placeholder: "Enter your password",
                        isSecure: true,
                        cornerRadius: 24.relativeFontSize,
                        height: 52.relativeHeight
                    )
                    .accessibilityHint("Enter your account password.")

                    // Remember Me & Forgot Password Row
                    HStack {
                        // Custom Checkbox
                        HStack(spacing: 8) {
                            Image(systemName: viewModel.rememberMe ? "checkmark.circle.fill" : "circle")
                                .foregroundColor(viewModel.rememberMe ? Color.Admin.primary : Color.Admin.textMediumGray)

                            Text("Remember Me")
                                .font(Font.Admin.subheading1)
                                .foregroundColor(Color.Admin.textDarkGray)
                        }
                        .onTapGesture(perform: viewModel.rememberMeToggled)
                        .accessibilityElement(children: .combine)
                        .accessibilityLabel("Remember Me checkbox")
                        .accessibilityValue(viewModel.rememberMe ? "Checked" : "Unchecked")
                        .accessibilityHint("Keeps you logged in on this device.")

                        Spacer()

                        Button("Forgot Password?", action: viewModel.forgotPasswordTapped)
                            .font(Font.Admin.subheading1)
                            .foregroundColor(Color.Admin.secondaryRed)
                            .accessibilityHint("Navigates to the password recovery screen.")
                    }

                    // Error Message Display
                    if let errorMessage = viewModel.errorMessage {
                        Text(errorMessage)
                            .foregroundColor(Color.Admin.textError)
                            .font(Font.Admin.caption1)
                            .padding(.top, 5)
                    }
                    let isLoading:Bool = viewModel.pageState == .loading()
                    // Sign In Button
                    Button(action: viewModel.loginButtonTapped) {

                        HStack {
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            }

                            Text(isLoading ? "Signing In..." : "Sign In")
                                .font(Font.Admin.heading2)
                                .foregroundColor(Color.Admin.textWhite)
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.Admin.primary)
                        .cornerRadius(24)
                    }
                    .disableWithGray(viewModel.isLoginButtonDisabled)
                    .accessibilityHint(isLoading ? "Attempting to sign in." : "Attempts to sign you in.")
                    .padding(.top, 32)

                    Spacer()
                }
                .padding(.horizontal, 30)
            }
            .scrollIndicators(.hidden)
            .background(Color.white.edgesIgnoringSafeArea(.all))
            .onAppear {
                // View appeared - no additional setup needed
                Logger.info("📱 SignInView appeared", tag: "SignInView")
            }
            .onDisappear {
                // Cancel any ongoing operations when view disappears
                viewModel.cancelOngoingOperations()
                Logger.info("📱 SignInView disappeared - operations cancelled", tag: "SignInView")
            }
        }
        
        
    }
}

#Preview {
    SignInView()
}
