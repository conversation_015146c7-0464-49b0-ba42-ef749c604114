//
//  HeaderView.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

// Memoized header/logo block to prevent unnecessary re-rendering
struct HeaderView: View {
    var body: some View {
        Group {
            Text("Sign In")
                .font(FontScheme.kNunitoBold(size: 18))
                .fontWeight(.bold)
                .foregroundColor(ColorConstants.Black90001)
                .padding(.bottom, 30)
                .accessibilityAddTraits(.isHeader)

            Image(.wasfaLogo)
                .resizable()
                .scaledToFit()
                .frame(width: 106, height: 69)
                .padding(.bottom, 64)
                .accessibilityLabel("Wasfa Logo")
        }
    }
}

private enum Constants {
    static let iconSize: CGFloat = 24.0
    static let menuItemHeight: CGFloat = 54.0
    static let headerFontSize: CGFloat = 18.0
    static let itemFontSize: CGFloat = 13.0.relativeFontSize
    static let headerVerticalPadding: CGFloat = 13.0
    static let closeButtonSize: CGFloat = 48.0
    static let topPadding: CGFloat = 30.0
    static let bottomPadding: CGFloat = 10.0
}
