//
//  RepositoriesNetworking.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import Alamofire

typealias Parameter = [String: Any]

enum RepositoriesNetworking {
    // HTTP method: GET
    case homePage
    case profile
    case getCartRX
    case emptyCart
    case brandsList
    case sellersList
    case medicalRepsList
    case influencersList

    // HTTP method: POST
    case login(parameters: Parameter)
    case productsList(parameters: Parameter)
    case productDetails(parameters: Parameter)
    case updateFavorite(parameters: Parameter)
    case updateProfile(parameters: Parameter)
    case prescriptionsList(parameters: Parameter)
    case prescriptionDetails(parameters: Parameter)
    case createPatient(parameters: Parameter)
    case submitRx(parameters: Parameter)
    case addCartRX(parameters: Parameter)
    case updateRXQuantity(parameters: Parameter)
    case customerList(parameters: Parameter)
    case removeCartItem(parameters: Parameter)
    case prescriptionGraphData(parameters: Parameter)
}

extension RepositoriesNetworking: TargetType {
    var path: String {
        switch self {
        // HTTP method: GET
        case .homePage: APIEndPoints.homePage
        case .profile: APIEndPoints.profile
        case .getCartRX: APIEndPoints.getCartRX
        case .emptyCart: APIEndPoints.emptyCart
        case .brandsList: APIEndPoints.brandsList
        case .sellersList: APIEndPoints.sellersList
        case .medicalRepsList: APIEndPoints.medicalRepsList
        case .influencersList: APIEndPoints.influencersList

        // HTTP method: POST
        case .login: APIEndPoints.login
        case .productsList: APIEndPoints.productsList
        case .productDetails: APIEndPoints.productDetails
        case .updateFavorite: APIEndPoints.updateFavorite
        case .updateProfile: APIEndPoints.updateProfile
        case .prescriptionsList: APIEndPoints.prescriptionsList
        case .prescriptionDetails: APIEndPoints.prescriptionDetails
        case .createPatient: APIEndPoints.createPatient
        case .submitRx: APIEndPoints.submitRx
        case .addCartRX: APIEndPoints.addCartRX
        case .updateRXQuantity: APIEndPoints.updateRXQuantity
        case .customerList: APIEndPoints.customerList
        case .removeCartItem: APIEndPoints.removeCartItem
        case .prescriptionGraphData: APIEndPoints.prescriptionGraphData
        }
    }

    var method: HTTPMethod {
        switch self {
        // HTTP method: GET
        case .homePage,
             .profile,
             .getCartRX,
             .emptyCart,
             .brandsList,
             .sellersList,
             .medicalRepsList,
             .influencersList:
            .get

        // HTTP method: POST
        case .login,
             .productsList,
             .productDetails,
             .updateFavorite,
             .updateProfile,
             .prescriptionsList,
             .prescriptionDetails,
             .createPatient,
             .submitRx,
             .addCartRX,
             .updateRXQuantity,
             .customerList,
             .removeCartItem,
             .prescriptionGraphData:
            .post
        }
    }

    var task: NetworkTask {
        switch self {
        // HTTP method: GET
        case .homePage,
             .profile,
             .getCartRX,
             .emptyCart,
             .brandsList,
             .sellersList,
             .medicalRepsList,
             .influencersList:
            .requestPlain

        // HTTP method: POST with parameters
        case
            let .login(parameters),
            let .productsList(parameters),
            let .productDetails(parameters),
            let .updateFavorite(parameters),
            let .updateProfile(parameters),
            let .prescriptionsList(parameters),
            let .prescriptionDetails(parameters),
            let .createPatient(parameters),
            let .submitRx(parameters),
            let .addCartRX(parameters),
            let .updateRXQuantity(parameters),
            let .customerList(parameters),
            let .removeCartItem(parameters),
            let .prescriptionGraphData(parameters):
            .requestParameters(parameters: parameters)
        }
    }
}

func urlParameters(from parameters: [String: Any]) -> String {
    parameters
        .compactMap { key, value in
            guard let escapedKey = key.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
                  let escapedValue = "\(value)".addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)
            else { return nil }
            return "\(escapedKey)=\(escapedValue)"
        }
        .joined(separator: "&")
}
