//
//  RepositoriesAPI.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import Alamofire
import Foundation

typealias EmptyStringArray = [String]
typealias CommonApiResponse = ApiBaseModel
typealias CommonResult<T: Codable> = Result<CommonApiResponse<T>, AFError>
typealias ApiResult<T: Codable> = Result<T, AFError>
var emptyDictionary: DictionaryType { [:] }

protocol RepositoriesAPIProtocol {
    // MARK: - Async/Await Methods
    // HTTP method: GET
    func homePage(parameters: Parameters) async throws -> CommonApiResponse<HomeModel>
    func profile(parameters: Parameters) async throws -> CommonApiResponse<ProfileModel>
    func getCartRX(parameters: Parameters) async throws -> CommonApiResponse<CartRXModel>
    func emptyCart(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct>
    func brandsList(parameters: Parameters) async throws -> CommonApiResponse<[Brand]>
    func sellersList(parameters: Parameters) async throws -> CommonApiResponse<[Seller]>
    func medicalRepsList(parameters: Parameters) async throws -> CommonApiResponse<[MedicalRep]>
    func influencersList(parameters: Parameters) async throws -> CommonApiResponse<[Influencer]>

    // HTTP method: POST
    func login(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct>
    func productsList(parameters: Parameters) async throws -> CommonApiResponse<ProductListModel>
    func productDetails(parameters: Parameters) async throws -> CommonApiResponse<[ProductDetailsModel]>
    func updateFavorite(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct>
    func updateProfile(parameters: Parameters) async throws -> CommonApiResponse<ProfileModel>
    func prescriptionsList(parameters: Parameters) async throws -> CommonApiResponse<PrescriptionModel>
    func prescriptionDetails(parameters: Parameters) async throws -> CommonApiResponse<[PrescriptionDetailsModel]>
    func createPatient(parameters: Parameters) async throws -> CommonApiResponse<CartRXModel.PatientInfo>
    func submitRx(parameters: Parameters) async throws -> CommonApiResponse<SubmitRXResponse>
    func addCartRX(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct>
    func updateRXQuantity(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct>
    func customerList(parameters: Parameters) async throws -> CommonApiResponse<[PatientSearchInfo]>
    func removeCartItem(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct>
    func prescriptionGraphData(parameters: Parameters) async throws -> CommonApiResponse<GraphResponse>
    func prescriptionReport(parameters: Parameters) async throws -> CommonApiResponse<ReportResponse>
}

class RepositoriesAPI: BaseAPI<RepositoriesNetworking>, RepositoriesAPIProtocol {
   
    public static let shared = RepositoriesAPI()

    override private init() { Logger.info("RepositoriesAPI class is initialized", tag: "RepositoriesAPI") }

    deinit { Logger.info("RepositoriesAPI class is deinitialized", tag: "RepositoriesAPI") }

    // MARK: - Async/Await Methods

    // MARK: - GET Requests

    func homePage(parameters: Parameters) async throws -> CommonApiResponse<HomeModel> {
        return try await fetchData(target: .homePage, responseClass: CommonApiResponse<HomeModel>.self)
    }

    func profile(parameters: Parameters) async throws -> CommonApiResponse<ProfileModel> {
        return try await fetchData(target: .profile, responseClass: CommonApiResponse<ProfileModel>.self)
    }

    func getCartRX(parameters: Parameters) async throws -> CommonApiResponse<CartRXModel> {
        return try await fetchData(target: .getCartRX, responseClass: CommonApiResponse<CartRXModel>.self)
    }

    func emptyCart(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct> {
        return try await fetchData(target: .emptyCart, responseClass: CommonApiResponse<VoidStruct>.self)
    }

    func brandsList(parameters: Parameters) async throws -> CommonApiResponse<[Brand]> {
        return try await fetchData(target: .brandsList, responseClass: CommonApiResponse<[Brand]>.self)
    }

    func sellersList(parameters: Parameters) async throws -> CommonApiResponse<[Seller]> {
        return try await fetchData(target: .sellersList, responseClass: CommonApiResponse<[Seller]>.self)
    }

    func medicalRepsList(parameters: Parameters) async throws -> CommonApiResponse<[MedicalRep]> {
        return try await fetchData(target: .medicalRepsList, responseClass: CommonApiResponse<[MedicalRep]>.self)
    }

    func influencersList(parameters: Parameters) async throws -> CommonApiResponse<[Influencer]> {
        return try await fetchData(target: .influencersList, responseClass: CommonApiResponse<[Influencer]>.self)
    }

    // MARK: - POST Requests

    func login(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct> {
        return try await fetchData(target: .login(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self)
    }

    func productsList(parameters: Parameters) async throws -> CommonApiResponse<ProductListModel> {
        return try await fetchData(target: .productsList(parameters: parameters), responseClass: CommonApiResponse<ProductListModel>.self)
    }

    func productDetails(parameters: Parameters) async throws -> CommonApiResponse<[ProductDetailsModel]> {
        return try await fetchData(target: .productDetails(parameters: parameters), responseClass: CommonApiResponse<[ProductDetailsModel]>.self)
    }

    func updateFavorite(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct> {
        return try await fetchData(target: .updateFavorite(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self)
    }

    func updateProfile(parameters: Parameters) async throws -> CommonApiResponse<ProfileModel> {
        return try await fetchData(target: .updateProfile(parameters: parameters), responseClass: CommonApiResponse<ProfileModel>.self)
    }

    func prescriptionsList(parameters: Parameters) async throws -> CommonApiResponse<PrescriptionModel> {
        return try await fetchData(target: .prescriptionsList(parameters: parameters), responseClass: CommonApiResponse<PrescriptionModel>.self)
    }

    func prescriptionDetails(parameters: Parameters) async throws -> CommonApiResponse<[PrescriptionDetailsModel]> {
        return try await fetchData(target: .prescriptionDetails(parameters: parameters), responseClass: CommonApiResponse<[PrescriptionDetailsModel]>.self)
    }

    func createPatient(parameters: Parameters) async throws -> CommonApiResponse<CartRXModel.PatientInfo> {
        return try await fetchData(target: .createPatient(parameters: parameters), responseClass: CommonApiResponse<CartRXModel.PatientInfo>.self)
    }

    func submitRx(parameters: Parameters) async throws -> CommonApiResponse<SubmitRXResponse> {
        return try await fetchData(target: .submitRx(parameters: parameters), responseClass: CommonApiResponse<SubmitRXResponse>.self)
    }

    func addCartRX(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct> {
        return try await fetchData(target: .addCartRX(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self)
    }

    func updateRXQuantity(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct> {
        return try await fetchData(target: .updateRXQuantity(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self)
    }

    func customerList(parameters: Parameters) async throws -> CommonApiResponse<[PatientSearchInfo]> {
        return try await fetchData(target: .customerList(parameters: parameters), responseClass: CommonApiResponse<[PatientSearchInfo]>.self)
    }

    func removeCartItem(parameters: Parameters) async throws -> CommonApiResponse<VoidStruct> {
        return try await fetchData(target: .removeCartItem(parameters: parameters), responseClass: CommonApiResponse<VoidStruct>.self)
    }

    func prescriptionGraphData(parameters: Parameters) async throws -> CommonApiResponse<GraphResponse> {
        return try await fetchData(target: .prescriptionGraphData(parameters: parameters), responseClass: CommonApiResponse<GraphResponse>.self)
    }

    func prescriptionReport(parameters: Parameters) async throws -> CommonApiResponse<ReportResponse> {
        return try await fetchData(target: .prescriptionReport(parameters: parameters), responseClass: CommonApiResponse<ReportResponse>.self)
    }
}
