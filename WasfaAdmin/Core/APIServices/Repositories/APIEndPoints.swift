//
//  APIEndPoints.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import Foundation

enum APIEndPoints {
    // HTTP method: GET
    static let homePage: String = "homePage"
    static let profile: String = "profile"
    static let getCartRX: String = "rxList"
    static let emptyCart: String = "emptyCart"
    static let brandsList: String = "brandsList"
    static let sellersList: String = "sellersList"
    static let medicalRepsList: String = "medicalRepList"
    static let influencersList: String = "influencerList"

    // HTTP method: POST
    static let login: String = "auth/login"
    static let productsList: String = "productsList"
    static let productDetails: String = "productDetails"
    static let updateFavorite: String = "updateFavourite"
    static let updateProfile: String = "updateProfile"
    static let prescriptionsList: String = "prescriptions"
    static let prescriptionDetails: String = "prescriptionDetails"
    static let createPatient: String = "createPatient"
    static let submitRx: String = "submitRx"
    static let addCartRX: String = "addToRx"
    static let updateRXQuantity: String = "updateRxQuantity"
    static let customerList: String = "customerList"
    static let removeCartItem: String = "removeCartItem"
    static let prescriptionGraphData: String = "prescriptionGraphData"
}
