//
//  BaseAPI.swift
//  Wasfa
//
//  Created by Apple on 26/10/2024.
//

import Alamofire
import Foundation
import SwiftyJ<PERSON>N

// import UIKit

class BaseAPI<T: TargetType> {
    let imageKey = "profilePic"

    // MARK: - Async/Await Methods

    func fetchData<M: Decodable>(target: T, responseClass: M.Type) async throws -> M {
        let method = Alamofire.HTTPMethod(rawValue: target.method.rawValue)
        let headers = Alamofire.HTTPHeaders(target.headers ?? [:])
        let parameters = buildParams(task: target.task)
        let apiURL = target.baseURL + target.path
        let params = parameters.0.isEmpty ? nil : parameters.0
        let encoding = parameters.1
        let imageData = params?[imageKey] as? Data
        Logger.debug("API Parameters: \(params as Any)", tag: "BaseAPI")

        if let selectedImageData = imageData {
            var headers: HTTPHeaders
//            headers = ["Content-type": "multipart/form-data",
//                       "Content-Disposition": "form-data"]
            
            headers = ["X-Requested-With":"XMLHttpRequest"]
            
            if let token = TokenManager.shared.getToken() {
                headers["Authorization"] = "Bearer \(token)"
            }
            
            return try await withCheckedThrowingContinuation { continuation in
                AF.upload(multipartFormData: { multipartFormData in
                    if let params = params, !params.isEmpty {
                        for (key, value) in params {
                            if key == self.imageKey {
                                multipartFormData.append(selectedImageData, withName: key, fileName: String(Date.currentTimeStamp) + ".jpeg", mimeType: "image/jpeg")
                            } else {
                                multipartFormData.append((value as! String).data(using: String.Encoding.utf8)!, withName: key)
                            }
                        }
                    }
                }, to: URL(string: apiURL)!, usingThreshold: UInt64(), method: method, headers: headers)
                    .validate(statusCode: 200 ..< 500)
                    .responseData(completionHandler: self.handleTokenResponse)
                    .responseDecodable(of: M.self) { response in
                        switch response.result {
                        case let .success(data):
                            continuation.resume(returning: data)
                        case let .failure(error):
                            Logger.error("API Request Failed: \(error)", tag: "BaseAPI")
                            continuation.resume(throwing: error)
                        }
                    }
            }
        } else {
            return try await withCheckedThrowingContinuation { continuation in
                AF.request(apiURL, method: method, parameters: params, encoding: encoding, headers: headers)
                    .validate(statusCode: 200 ..< 500)
                    .responseData(completionHandler: self.handleTokenResponse)
                    .responseDecodable(of: M.self) { response in
                        switch response.result {
                        case let .success(data):
                            continuation.resume(returning: data)
                        case let .failure(error):
                            Logger.error("API Request Failed: \(error)", tag: "BaseAPI")
                            continuation.resume(throwing: error)
                        }
                    }
            }
        }
    }
    // handle if there is a token in response
    func handleTokenResponse(response: AFDataResponse<Data>) {
        switch response.result {
        case .success:
            if let json = response.data {
                
//                print(response.request)
//                json.printFormattedJSON()
        
               
                

                do {
                    let data = try JSON(data: json)
//                    print(data)
                    if let status = data["status"].int, status == 200 {
                        let data = data["data"]
                        let accessToken = data["token"].string
                        if let accessToken = accessToken {
                            // Store token temporarily in the old storage for SignInViewModel to handle
                            // The Remember Me logic in SignInViewModel will decide final storage location
                            UserDefaultsSecure.sharedInstance.setGeneratedTokenStringValue(value: accessToken)
                            Logger.info("🔐 Token received from API and temporarily stored for Remember Me processing", tag: "BaseAPI")
                        }
                    } else {
//                        print(data)
                    }
                } catch {
                    Logger.error("JSON Error: \(error.localizedDescription)", tag: "BaseAPI")
                }
            }
        case let .failure(error):
            response.data?.printFormattedJSON()
            Logger.error("Token Response Error: \(error)", tag: "BaseAPI")
        }
        
        NetworkLogger.log(response: response.response, data: response.data, error: response.error)
    }
}

private func buildParams(task: NetworkTask) -> ([String: Any], ParameterEncoding) {
    switch task {
    case .requestPlain:
        return ([:], URLEncoding.default)
    case let .requestParameters(parameters: parameters, encoding: encoding):
        return (parameters, encoding)
    }
}


