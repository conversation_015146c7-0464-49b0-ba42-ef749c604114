//
//  Route.swift
//  Wasfa
//
//  Created by Apple on 20/09/2024.
//

import SwiftUI

class CallBack<T: RoutableProtocol>: RoutableProtocol {
    static func == (lhs: CallBack, rhs: CallBack) -> Bool {
        ObjectIdentifier(lhs) == ObjectIdentifier(rhs)
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(ObjectIdentifier(self))
    }

    let onBack: TypeCallback<T>
    init(onBack: @escaping TypeCallback<T>) {
        self.onBack = onBack
    }
}

enum Route: Equatable, Identifiable, Hashable {
    case splash,
         login,
         dashboard,
         prescriptionDetails(prescriptionId: String),
         medicationSelection,
         prescriptionReview,
         productDetail(productId: Int),
         medicationsFilter(initialFilters: FilterState)

    // Static method to create medicationsFilter route with callback
    static func medicationsFilter(
        initialFilters: FilterState,
        onFiltersApplied: @escaping (FilterState) -> Void
    ) -> Route {
        // Store callback in a global dictionary or use a different approach
        // For now, we'll handle this in the view implementation
        return .medicationsFilter(initialFilters: initialFilters)
    }

    var id: String { rawValue }

    var rawValue: String {
        switch self {
        case .splash: "splash"
        case .login: "login"
        case .dashboard: "dashboard"
        case .prescriptionDetails(let prescriptionId):
            "prescriptionDetails_\(prescriptionId)"
        case .medicationSelection:
            "medicationSelection"
        case .prescriptionReview:
            "prescriptionReview"
        case .productDetail(let productId):
            "productDetail_\(productId)"
        case .medicationsFilter(let initialFilters):
            "medicationsFilter_\(initialFilters.hashValue)"
        }
    }
}

extension Route: View {
    var body: some View {
        switch self {
        case .splash:
            Text("Splash")
        case .login:
            Text("Login")
        case .dashboard:
            Text("Dashboard")
        case .prescriptionDetails(let prescriptionId):
            PrescriptionDetailView(prescriptionId: prescriptionId)
        case .medicationSelection:
            MedicationsView()
        case .prescriptionReview:
            PrescriptionReviewView()
        case .productDetail(let productId):
            ProductDetailView(productId: productId)
        case .medicationsFilter(let initialFilters):
            MedicationsFilterView(
                initialFilters: initialFilters,
                onFiltersApplied: { _ in
                    // This will be handled differently in the actual implementation
                }
            )
        }
    }
}
