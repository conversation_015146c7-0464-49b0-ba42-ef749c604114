// //
// //  RouterManagerPerformanceTest.swift
// //  WasfaAdmin
// //
// //  Created by Optimization on 2025-06-26.
// //

// import Foundation
// import SwiftUI

// /// Performance testing utility for RouterManager optimizations
// struct RouterManagerPerformanceTest {

//     /// Tests the performance improvements of the optimized RouterManager
//     static func runPerformanceTests() {
//         print("🚀 Starting RouterManager Performance Tests")
//         print("=" * 50)

//         let routerManager = RouterManager()

//         // Run all tests asynchronously to handle @MainActor requirements
//         Task { @MainActor in
//             // Test 1: Route Push Performance
//             await testRoutePushPerformance(routerManager)

//             // Test 2: Duplicate Detection Performance
//             await testDuplicateDetectionPerformance(routerManager)

//             // Test 3: Memory Usage Test
//             await testMemoryUsage(routerManager)

//             // Test 4: Async Navigation Performance
//             await testAsyncNavigationPerformance(routerManager)

//             // Display final performance summary
//             routerManager.logPerformanceSummary()

//             Logger.info("=" + String(repeating: "=", count: 49), tag: "PerformanceTest")
//             Logger.info("✅ Performance Tests Completed", tag: "PerformanceTest")
//         }

//         // Wait for tests to complete (simple synchronous wait for demo purposes)
//         Thread.sleep(forTimeInterval: 2.0)
//     }

//     /// Synchronous wrapper for easier testing integration
//     static func runPerformanceTestsSync() {
//         let semaphore = DispatchSemaphore(value: 0)

//         Logger.info("🚀 Starting RouterManager Performance Tests (Sync)", tag: "PerformanceTest")
//         Logger.info("=" + String(repeating: "=", count: 49), tag: "PerformanceTest")

//         let routerManager = RouterManager()

//         Task { @MainActor in
//             // Test 1: Route Push Performance
//             await testRoutePushPerformance(routerManager)

//             // Test 2: Duplicate Detection Performance
//             await testDuplicateDetectionPerformance(routerManager)

//             // Test 3: Memory Usage Test
//             await testMemoryUsage(routerManager)

//             // Test 4: Async Navigation Performance
//             await testAsyncNavigationPerformance(routerManager)

//             // Display final performance summary
//             routerManager.logPerformanceSummary()

//             Logger.info("=" + String(repeating: "=", count: 49), tag: "PerformanceTest")
//             Logger.info("✅ Performance Tests Completed", tag: "PerformanceTest")

//             semaphore.signal()
//         }

//         // Wait for completion
//         semaphore.wait()
//     }
    
//     /// Test route push performance with O(1) operations
//     @MainActor
//     private static func testRoutePushPerformance(_ routerManager: RouterManager) async {
//         Logger.info("\n📊 Test 1: Route Push Performance", tag: "PerformanceTest")

//         let testRoutes: [Route] = [.dashboard, .login, .splash]
//         let iterations = 1000

//         let startTime = CFAbsoluteTimeGetCurrent()

//         for i in 0..<iterations {
//             let route = testRoutes[i % testRoutes.count]
//             routerManager.push(to: route, where: .homeRoute)
//         }

//         let endTime = CFAbsoluteTimeGetCurrent()
//         let totalTime = endTime - startTime

//         Logger.info("  • Pushed \(iterations) routes in \(String(format: "%.4f", totalTime))s", tag: "PerformanceTest")
//         Logger.info("  • Average time per push: \(String(format: "%.6f", totalTime / Double(iterations)))s", tag: "PerformanceTest")
//         Logger.info("  • Routes per second: \(String(format: "%.0f", Double(iterations) / totalTime))", tag: "PerformanceTest")

//         // Cleanup
//         routerManager.popToRoot(where: .homeRoute)
//     }
    
//     /// Test O(1) duplicate detection vs O(n) array search
//     @MainActor
//     private static func testDuplicateDetectionPerformance(_ routerManager: RouterManager) async {
//         Logger.info("\n🔍 Test 2: Duplicate Detection Performance", tag: "PerformanceTest")

//         // Pre-populate with routes
//         let baseRoutes: [Route] = [.dashboard, .login, .splash]
//         for route in baseRoutes {
//             routerManager.push(to: route, where: .medicationsRoute, forcefully: true)
//         }

//         let testRoute = Route.dashboard
//         let iterations = 10000

//         let startTime = CFAbsoluteTimeGetCurrent()

//         for _ in 0..<iterations {
//             // This will trigger duplicate detection using O(1) Set lookup
//             routerManager.push(to: testRoute, where: .medicationsRoute, forcefully: false)
//         }

//         let endTime = CFAbsoluteTimeGetCurrent()
//         let totalTime = endTime - startTime

//         Logger.info("  • Performed \(iterations) duplicate checks in \(String(format: "%.4f", totalTime))s", tag: "PerformanceTest")
//         Logger.info("  • Average time per check: \(String(format: "%.6f", totalTime / Double(iterations)))s", tag: "PerformanceTest")
//         Logger.info("  • Checks per second: \(String(format: "%.0f", Double(iterations) / totalTime))", tag: "PerformanceTest")

//         // Cleanup
//         routerManager.popToRoot(where: .medicationsRoute)
//     }
    
//     /// Test memory usage and cleanup mechanisms
//     @MainActor
//     private static func testMemoryUsage(_ routerManager: RouterManager) async {
//         Logger.info("\n💾 Test 3: Memory Usage Test", tag: "PerformanceTest")

//         // Fill up route stacks
//         let routes: [Route] = [.dashboard, .login, .splash]

//         for routeType in RoutesType.allCases {
//             for i in 0..<60 { // Exceed maxRoutesPerStack (50)
//                 let route = routes[i % routes.count]
//                 routerManager.push(to: route, where: routeType, forcefully: true)
//             }
//         }

//         let beforeCleanup = routerManager.getMemoryStats()
//         Logger.info("  • Memory usage before cleanup:", tag: "PerformanceTest")
//         for (routeType, count) in beforeCleanup {
//             Logger.info("    - \(routeType.displayName): \(count) routes", tag: "PerformanceTest")
//         }

//         // Trigger cleanup (already on MainActor)
//         await routerManager.performAutomaticCleanup()

//         let afterCleanup = routerManager.getMemoryStats()
//         Logger.info("  • Memory usage after cleanup:", tag: "PerformanceTest")
//         for (routeType, count) in afterCleanup {
//             Logger.info("    - \(routeType.displayName): \(count) routes", tag: "PerformanceTest")
//         }

//         let totalBefore = beforeCleanup.values.reduce(0, +)
//         let totalAfter = afterCleanup.values.reduce(0, +)
//         let reduction = totalBefore - totalAfter
//         let reductionPercent = Double(reduction) / Double(totalBefore) * 100

//         Logger.info("  • Total routes reduced: \(reduction) (\(String(format: "%.1f", reductionPercent))%)", tag: "PerformanceTest")
//     }
    
//     /// Test async navigation performance
//     @MainActor
//     private static func testAsyncNavigationPerformance(_ routerManager: RouterManager) async {
//         Logger.info("\n⚡ Test 4: Async Navigation Performance", tag: "PerformanceTest")

//         let iterations = 100
//         let startTime = CFAbsoluteTimeGetCurrent()

//         for i in 0..<iterations {
//             let route = i % 2 == 0 ? Route.dashboard : Route.login
//             await routerManager.pushAsync(to: route, where: .centerRoute, animated: false)
//         }

//         let endTime = CFAbsoluteTimeGetCurrent()
//         let totalTime = endTime - startTime

//         Logger.info("  • Completed \(iterations) async navigations in \(String(format: "%.4f", totalTime))s", tag: "PerformanceTest")
//         Logger.info("  • Average time per async navigation: \(String(format: "%.6f", totalTime / Double(iterations)))s", tag: "PerformanceTest")

//         // Cleanup
//         routerManager.popToRoot(where: .centerRoute)
//     }
// }

// /// Extension for string repetition
// extension String {
//     static func * (left: String, right: Int) -> String {
//         return String(repeating: left, count: right)
//     }
// }
