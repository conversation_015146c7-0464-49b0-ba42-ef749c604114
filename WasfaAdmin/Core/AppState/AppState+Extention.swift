//
//  AppState+Extention.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import UIKit



extension AppState {
    static var user: User? {
        get { UserDefaults.user }
        set { UserDefaults.user = newValue }
    }

    static var isLoggedIn: Bool {
        get { UserDefaults.isLoggedIn }
        set { UserDefaults.isLoggedIn = newValue }
    }

    static var language: String {
        get { UserDefaults.language }
        set { UserDefaults.language = newValue }
    }

    static var deviceID: String {
        UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString
    }

    static var fcmToken: String {
        get { UserDefaults.fcmToken }
        set { UserDefaults.fcmToken = newValue }
    }
}

private extension UserDefaults {
    static var user: User? {
        get { standard.codableValue(forKey: #function) }
        set { standard.setCodable(newValue, forKey: #function) }
    }

    static var isLoggedIn: Bool {
        get { standard.value(forKey: #function) as? Bool ?? false }
        set { standard.set(newValue, forKey: #function) }
    }

    static var language: String {
        get { standard.value(forKey: #function) as? String ?? Locale.current.language.languageCode?.identifier ?? "" }
        set { standard.set(newValue, forKey: #function) }
    }

    static var fcmToken: String {
        get { standard.value(forKey: #function) as? String ?? "" }
        set { standard.set(newValue, forKey: #function) }
    }
}


extension UserDefaults {
    func setCodable<T: Codable>(_ value: T?, forKey key: String) {
        guard let value = value else {
            removeObject(forKey: key)
            return
        }

        if let data = try? JSONEncoder().encode(value) {
            set(data, forKey: key)
        }
    }

    func codableValue<T: Codable>(forKey key: String) -> T? {
        guard let data = data(forKey: key) else { return nil }
        return try? JSONDecoder().decode(T.self, from: data)
    }
}
