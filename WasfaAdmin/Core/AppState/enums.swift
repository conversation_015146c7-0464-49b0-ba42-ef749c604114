//
//  enums.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

enum InitialScreen {
    case splash, dashboard, authentication
}


enum AppStorageKey {
    static let cartCount = "cartQuantity"
    static let wishlistCount = "wishlistQuantity"
    static let notificationCount = "wishlistQuantity"
    static let startUpLanguageShown = "showStartUpLanguage"
    static let appLocale = "appLocale"
}


enum AppLocale: String, CaseIterable, Identifiable {
    case english = "en"
    case arabic = "ar"

    var id: Self {self}

    var title: String {
        switch self {
        case .english:
            "english"
        case .arabic:
            "عربي"
        }
    }

    var locale: Locale {
        switch self {
        case .english:
            Locale(identifier: rawValue)
        case .arabic:
            Locale(identifier: rawValue)
        }
    }

    var direction: LayoutDirection {
        switch self {
        case .english:
            .leftToRight
        case .arabic:
            .rightToLeft
        }
    }
}
