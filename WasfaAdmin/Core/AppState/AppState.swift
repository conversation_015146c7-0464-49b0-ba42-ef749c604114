//
//  AppState.swift
//  WasfaAdmin
//
//  Created by Apple on 26/06/2025.
//

import SwiftUI

class AppState: ObservableObject {
    @Published var toast: [Toast] = .init()
    @Published private(set) var initialScreen: InitialScreen = .splash
    @Published private(set) var selectedTab: Tab = .home
    @Published var rootViewId = UUID()
    @AppStorage(AppStorageKey.appLocale) private(set) var appLocale: AppLocale = .english

    public func updateInitialScreen(_ value: InitialScreen) {
        initialScreen = value
    }

    public func updateSelectedTab(_ value: Tab) {
        selectedTab = value
    }

    func showToast(_ value: Toast) {
//        toast.removeAll()
        toast.append(value)
    }
}
