<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>Document</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.data</string>
			</array>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
		</dict>
	</array>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UIAppFonts</key>
	<array>
		<string>InterMedium.ttf</string>
		<string>InterRegular.ttf</string>
		<string>NunitoBold.ttf</string>
		<string>NunitoExtraBold.ttf</string>
		<string>NunitoLight.ttf</string>
		<string>NunitoMedium.ttf</string>
		<string>NunitoRegular.ttf</string>
		<string>NunitoSemiBold.ttf</string>
		<string>PlusJakartaSansBold.ttf</string>
		<string>PoppinsBold.ttf</string>
		<string>PoppinsLight.ttf</string>
		<string>PoppinsMedium.ttf</string>
		<string>PoppinsRegular.ttf</string>
		<string>PoppinsSemiBold.ttf</string>
		<string>RobotoBold.ttf</string>
		<string>RobotoLight.ttf</string>
		<string>RobotoMedium.ttf</string>
		<string>RobotoRegular.ttf</string>
		<string>RobotoRomanLight.ttf</string>
		<string>RobotoRomanMedium.ttf</string>
		<string>RobotoRomanRegular.ttf</string>
	</array>
    <key>UIUserInterfaceStyle</key>
    <string>Light</string>
</dict>
</plist>
