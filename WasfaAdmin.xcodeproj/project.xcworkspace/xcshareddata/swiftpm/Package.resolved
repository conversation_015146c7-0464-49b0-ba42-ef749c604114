{"originHash": "4d527a87c369ca153c1183abd2adfc7a6a6b829eb0b63fe98e8e5d127f1eeabe", "pins": [{"identity": "alamofire", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/Alamofire.git", "state": {"revision": "513364f870f6bfc468f9d2ff0a95caccc10044c5", "version": "5.10.2"}}, {"identity": "cocoalumberjack", "kind": "remoteSourceControl", "location": "https://github.com/CocoaLumberjack/CocoaLumberjack.git", "state": {"revision": "4b8714a7fb84d42393314ce897127b3939885ec3", "version": "3.8.5"}}, {"identity": "daterangepicker", "kind": "remoteSourceControl", "location": "https://github.com/MrAsterisco/DateRangePicker.git", "state": {"revision": "8bad437603a0559cfe29e69df4ef1981ddfb5f2b", "version": "1.0.4"}}, {"identity": "opendateinterval", "kind": "remoteSourceControl", "location": "https://github.com/MrAsterisco/OpenDateInterval", "state": {"revision": "acf88f98adae4ef2f7040d3fcc23685b094b219b", "version": "1.0.0"}}, {"identity": "richtext", "kind": "remoteSourceControl", "location": "https://github.com/NuPlay/RichText.git", "state": {"revision": "3910393f3837a21fd2e414b758942ffd2d62aaa2", "version": "2.5.0"}}, {"identity": "sdwebimage", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImage.git", "state": {"revision": "b62cb63bf4ed1f04c961a56c9c6c9d5ab8524ec6", "version": "5.21.1"}}, {"identity": "sdwebimageswiftui", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImageSwiftUI.git", "state": {"revision": "451c6dfd5ecec2cf626d1d9ca81c2d4a60355172", "version": "3.1.3"}}, {"identity": "securedefaults", "kind": "remoteSourceControl", "location": "https://github.com/vpeschenkov/SecureDefaults.git", "state": {"revision": "3f9d5d19f7401250791840bff857ed5f6b8c8ddf", "version": "1.2.2"}}, {"identity": "svgkit", "kind": "remoteSourceControl", "location": "https://github.com/SVGKit/SVGKit.git", "state": {"revision": "58152b9f7c85eab239160b36ffdfd364aa43d666", "version": "3.0.0"}}, {"identity": "swift-log", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-log", "state": {"revision": "3d8596ed08bd13520157f0355e35caed215ffbfa", "version": "1.6.3"}}, {"identity": "<PERSON><PERSON><PERSON><PERSON>", "kind": "remoteSourceControl", "location": "https://github.com/SwiftyJSON/SwiftyJSON.git", "state": {"revision": "af76cf3ef710b6ca5f8c05f3a31307d44a3c5828", "version": "5.0.2"}}, {"identity": "swipeactions", "kind": "remoteSourceControl", "location": "https://github.com/aheze/SwipeActions.git", "state": {"revision": "41e6f6dce02d8cfa164f8c5461a41340850ca3ab", "version": "1.1.0"}}], "version": 3}