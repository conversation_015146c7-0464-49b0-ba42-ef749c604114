// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		13A1F07435B2455AA46A8E1F /* PermissionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = C85553953C3A441B9D6BBAF1 /* PermissionManager.swift */; };
		14C9AECB1E164C90A5BD2167 /* Nunito-ExtraBold.ttf in Sources */ = {isa = PBXBuildFile; fileRef = 4E57AF7108684E9891C4861E /* Nunito-ExtraBold.ttf */; };
		18A7D3F72E0C895400E4ACA8 /* SwipeActions in Frameworks */ = {isa = PBXBuildFile; productRef = 18A7D3F62E0C895400E4ACA8 /* SwipeActions */; };
		18A7D40E2E0C8B0400E4ACA8 /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 18A7D40D2E0C8B0400E4ACA8 /* Alamofire */; };
		18A7D60C2E0C947600E4ACA8 /* SwiftyJSON in Frameworks */ = {isa = PBXBuildFile; productRef = 18A7D60B2E0C947600E4ACA8 /* SwiftyJSON */; };
		18A7D61D2E0C94FF00E4ACA8 /* SecureDefaults in Frameworks */ = {isa = PBXBuildFile; productRef = 18A7D61C2E0C94FF00E4ACA8 /* SecureDefaults */; };
		29A4AC63650E40AAB34D3245 /* Nunito-Regular.ttf in Sources */ = {isa = PBXBuildFile; fileRef = 43AB7428C5D640D191FA441E /* Nunito-Regular.ttf */; };
		2E29C53578EE4E42B462CF4E /* PlusJakartaSans-Bold.ttf in Sources */ = {isa = PBXBuildFile; fileRef = 183D2E3228D74E8190AAC90B /* PlusJakartaSans-Bold.ttf */; };
		329B2BABE1FA4C6B9A8FA891 /* Inter-Regular.ttf in Sources */ = {isa = PBXBuildFile; fileRef = 2E44BABC7D93417D9D86D78D /* Inter-Regular.ttf */; };
		43027DC67EAB491A98D75255 /* Inter-Medium.ttf in Sources */ = {isa = PBXBuildFile; fileRef = 30BBB416941F4B2F8ED10622 /* Inter-Medium.ttf */; };
		4E07C82220BE4B858D196B96 /* Roboto-Medium.ttf in Sources */ = {isa = PBXBuildFile; fileRef = C8F522280BCF4839AB20DCAB /* Roboto-Medium.ttf */; };
		5CFE4803BB0141F7994A95CF /* Roboto-Regular.ttf in Sources */ = {isa = PBXBuildFile; fileRef = D546559218CD4D378F1910D5 /* Roboto-Regular.ttf */; };
		5D4A6F8B19084CDB8F86473E /* Nunito-Bold.ttf in Sources */ = {isa = PBXBuildFile; fileRef = 91BB9C92DBE34520B27B73ED /* Nunito-Bold.ttf */; };
		83FCC182924248A191FB8B86 /* Roboto-Bold.ttf in Sources */ = {isa = PBXBuildFile; fileRef = 97E74BBFD28343969CC82E3A /* Roboto-Bold.ttf */; };
		969D9D67049845AC8405D974 /* Roboto-Light.ttf in Sources */ = {isa = PBXBuildFile; fileRef = 8D8F6E2201B9482B897EA192 /* Roboto-Light.ttf */; };
		A7FFF0708D444BA1B73F971F /* ImageSelectionSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2E8212F1E8D94F21A4159901 /* ImageSelectionSheet.swift */; };
		B4D6509BCF494747860F6F72 /* Poppins-Regular.ttf in Sources */ = {isa = PBXBuildFile; fileRef = DC17006F152E4680976309EF /* Poppins-Regular.ttf */; };
		BFB5FACA720A43BBB851D4BC /* Nunito-Medium.ttf in Sources */ = {isa = PBXBuildFile; fileRef = 993D41AD181C46C99936B9EF /* Nunito-Medium.ttf */; };
		C2A30D76782E498A9BC778FC /* ImagePicker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 926332F3D5044BEA8A8EF47F /* ImagePicker.swift */; };
		C496B67BE1D24918BA615D77 /* Nunito-Light.ttf in Sources */ = {isa = PBXBuildFile; fileRef = F7235CB4D26E4DF0909242F6 /* Nunito-Light.ttf */; };
		F13D294BC88A4BCBB4A90038 /* Nunito-SemiBold.ttf in Sources */ = {isa = PBXBuildFile; fileRef = DAE42E7855424EA48B6691B9 /* Nunito-SemiBold.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		183D2E3228D74E8190AAC90B /* PlusJakartaSans-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "PlusJakartaSans-Bold.ttf"; sourceTree = "<group>"; };
		18A7D3B52E0C857700E4ACA8 /* WasfaAdmin.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = WasfaAdmin.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2E44BABC7D93417D9D86D78D /* Inter-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Inter-Regular.ttf"; sourceTree = "<group>"; };
		2E8212F1E8D94F21A4159901 /* ImageSelectionSheet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageSelectionSheet.swift; sourceTree = "<group>"; };
		30BBB416941F4B2F8ED10622 /* Inter-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Inter-Medium.ttf"; sourceTree = "<group>"; };
		43AB7428C5D640D191FA441E /* Nunito-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Nunito-Regular.ttf"; sourceTree = "<group>"; };
		4E57AF7108684E9891C4861E /* Nunito-ExtraBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Nunito-ExtraBold.ttf"; sourceTree = "<group>"; };
		8D8F6E2201B9482B897EA192 /* Roboto-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Light.ttf"; sourceTree = "<group>"; };
		91BB9C92DBE34520B27B73ED /* Nunito-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Nunito-Bold.ttf"; sourceTree = "<group>"; };
		926332F3D5044BEA8A8EF47F /* ImagePicker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePicker.swift; sourceTree = "<group>"; };
		97E74BBFD28343969CC82E3A /* Roboto-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Bold.ttf"; sourceTree = "<group>"; };
		993D41AD181C46C99936B9EF /* Nunito-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Nunito-Medium.ttf"; sourceTree = "<group>"; };
		C85553953C3A441B9D6BBAF1 /* PermissionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PermissionManager.swift; sourceTree = "<group>"; };
		C8F522280BCF4839AB20DCAB /* Roboto-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Medium.ttf"; sourceTree = "<group>"; };
		D546559218CD4D378F1910D5 /* Roboto-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Regular.ttf"; sourceTree = "<group>"; };
		DAE42E7855424EA48B6691B9 /* Nunito-SemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Nunito-SemiBold.ttf"; sourceTree = "<group>"; };
		DC17006F152E4680976309EF /* Poppins-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Poppins-Regular.ttf"; sourceTree = "<group>"; };
		F7235CB4D26E4DF0909242F6 /* Nunito-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Nunito-Light.ttf"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		18A7D8B82E0DE21600E4ACA8 /* Exceptions for "WasfaAdmin" folder in "WasfaAdmin" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 18A7D3B42E0C857700E4ACA8 /* WasfaAdmin */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		18A7D3B72E0C857700E4ACA8 /* WasfaAdmin */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				18A7D8B82E0DE21600E4ACA8 /* Exceptions for "WasfaAdmin" folder in "WasfaAdmin" target */,
			);
			path = WasfaAdmin;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		18A7D3B22E0C857700E4ACA8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				18A7D60C2E0C947600E4ACA8 /* SwiftyJSON in Frameworks */,
				18A7D61D2E0C94FF00E4ACA8 /* SecureDefaults in Frameworks */,
				18A7D3F72E0C895400E4ACA8 /* SwipeActions in Frameworks */,
				18A7D40E2E0C8B0400E4ACA8 /* Alamofire in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		18A7D3AC2E0C857700E4ACA8 = {
			isa = PBXGroup;
			children = (
				18A7D3B72E0C857700E4ACA8 /* WasfaAdmin */,
				18A7D3B62E0C857700E4ACA8 /* Products */,
				18A7D9CC2E0DE87E00E4ACA8 /* Recovered References */,
			);
			sourceTree = "<group>";
		};
		18A7D3B62E0C857700E4ACA8 /* Products */ = {
			isa = PBXGroup;
			children = (
				18A7D3B52E0C857700E4ACA8 /* WasfaAdmin.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		18A7D9CC2E0DE87E00E4ACA8 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
				30BBB416941F4B2F8ED10622 /* Inter-Medium.ttf */,
				2E44BABC7D93417D9D86D78D /* Inter-Regular.ttf */,
				91BB9C92DBE34520B27B73ED /* Nunito-Bold.ttf */,
				4E57AF7108684E9891C4861E /* Nunito-ExtraBold.ttf */,
				F7235CB4D26E4DF0909242F6 /* Nunito-Light.ttf */,
				993D41AD181C46C99936B9EF /* Nunito-Medium.ttf */,
				43AB7428C5D640D191FA441E /* Nunito-Regular.ttf */,
				DAE42E7855424EA48B6691B9 /* Nunito-SemiBold.ttf */,
				183D2E3228D74E8190AAC90B /* PlusJakartaSans-Bold.ttf */,
				DC17006F152E4680976309EF /* Poppins-Regular.ttf */,
				97E74BBFD28343969CC82E3A /* Roboto-Bold.ttf */,
				8D8F6E2201B9482B897EA192 /* Roboto-Light.ttf */,
				C8F522280BCF4839AB20DCAB /* Roboto-Medium.ttf */,
				D546559218CD4D378F1910D5 /* Roboto-Regular.ttf */,
				926332F3D5044BEA8A8EF47F /* ImagePicker.swift */,
				2E8212F1E8D94F21A4159901 /* ImageSelectionSheet.swift */,
				C85553953C3A441B9D6BBAF1 /* PermissionManager.swift */,
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		18A7D3B42E0C857700E4ACA8 /* WasfaAdmin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 18A7D3C02E0C857900E4ACA8 /* Build configuration list for PBXNativeTarget "WasfaAdmin" */;
			buildPhases = (
				18A7D3B12E0C857700E4ACA8 /* Sources */,
				18A7D3B22E0C857700E4ACA8 /* Frameworks */,
				18A7D3B32E0C857700E4ACA8 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				18A7D3B72E0C857700E4ACA8 /* WasfaAdmin */,
			);
			name = WasfaAdmin;
			packageProductDependencies = (
				18A7D3F62E0C895400E4ACA8 /* SwipeActions */,
				18A7D40D2E0C8B0400E4ACA8 /* Alamofire */,
				18A7D60B2E0C947600E4ACA8 /* SwiftyJSON */,
				18A7D61C2E0C94FF00E4ACA8 /* SecureDefaults */,
			);
			productName = WasfaApixrx;
			productReference = 18A7D3B52E0C857700E4ACA8 /* WasfaAdmin.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		18A7D3AD2E0C857700E4ACA8 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					18A7D3B42E0C857700E4ACA8 = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = 18A7D3B02E0C857700E4ACA8 /* Build configuration list for PBXProject "WasfaAdmin" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 18A7D3AC2E0C857700E4ACA8;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				18A7D3F52E0C895400E4ACA8 /* XCRemoteSwiftPackageReference "SwipeActions" */,
				18A7D40C2E0C8B0400E4ACA8 /* XCRemoteSwiftPackageReference "Alamofire" */,
				18A7D60A2E0C947600E4ACA8 /* XCRemoteSwiftPackageReference "SwiftyJSON" */,
				18A7D61B2E0C94FF00E4ACA8 /* XCRemoteSwiftPackageReference "SecureDefaults" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 18A7D3B62E0C857700E4ACA8 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				18A7D3B42E0C857700E4ACA8 /* WasfaAdmin */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		18A7D3B32E0C857700E4ACA8 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		18A7D3B12E0C857700E4ACA8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				43027DC67EAB491A98D75255 /* Inter-Medium.ttf in Sources */,
				329B2BABE1FA4C6B9A8FA891 /* Inter-Regular.ttf in Sources */,
				5D4A6F8B19084CDB8F86473E /* Nunito-Bold.ttf in Sources */,
				14C9AECB1E164C90A5BD2167 /* Nunito-ExtraBold.ttf in Sources */,
				C496B67BE1D24918BA615D77 /* Nunito-Light.ttf in Sources */,
				BFB5FACA720A43BBB851D4BC /* Nunito-Medium.ttf in Sources */,
				29A4AC63650E40AAB34D3245 /* Nunito-Regular.ttf in Sources */,
				F13D294BC88A4BCBB4A90038 /* Nunito-SemiBold.ttf in Sources */,
				2E29C53578EE4E42B462CF4E /* PlusJakartaSans-Bold.ttf in Sources */,
				B4D6509BCF494747860F6F72 /* Poppins-Regular.ttf in Sources */,
				83FCC182924248A191FB8B86 /* Roboto-Bold.ttf in Sources */,
				969D9D67049845AC8405D974 /* Roboto-Light.ttf in Sources */,
				4E07C82220BE4B858D196B96 /* Roboto-Medium.ttf in Sources */,
				5CFE4803BB0141F7994A95CF /* Roboto-Regular.ttf in Sources */,
				C2A30D76782E498A9BC778FC /* ImagePicker.swift in Sources */,
				A7FFF0708D444BA1B73F971F /* ImageSelectionSheet.swift in Sources */,
				13A1F07435B2455AA46A8E1F /* PermissionManager.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		18A7D3BE2E0C857900E4ACA8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 5Z39VS6626;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		18A7D3BF2E0C857900E4ACA8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 5Z39VS6626;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		18A7D3C12E0C857900E4ACA8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5Z39VS6626;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = WasfaAdmin/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = WasfaRXDoctor;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wasfa.dashboard.app.s.WasfaApixrx;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		18A7D3C22E0C857900E4ACA8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5Z39VS6626;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = WasfaAdmin/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = WasfaRXDoctor;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wasfa.dashboard.app.s.WasfaApixrx;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		18A7D3B02E0C857700E4ACA8 /* Build configuration list for PBXProject "WasfaAdmin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18A7D3BE2E0C857900E4ACA8 /* Debug */,
				18A7D3BF2E0C857900E4ACA8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		18A7D3C02E0C857900E4ACA8 /* Build configuration list for PBXNativeTarget "WasfaAdmin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				18A7D3C12E0C857900E4ACA8 /* Debug */,
				18A7D3C22E0C857900E4ACA8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		18A7D3F52E0C895400E4ACA8 /* XCRemoteSwiftPackageReference "SwipeActions" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/aheze/SwipeActions.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.1.0;
			};
		};
		18A7D40C2E0C8B0400E4ACA8 /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.10.2;
			};
		};
		18A7D60A2E0C947600E4ACA8 /* XCRemoteSwiftPackageReference "SwiftyJSON" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SwiftyJSON/SwiftyJSON.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.2;
			};
		};
		18A7D61B2E0C94FF00E4ACA8 /* XCRemoteSwiftPackageReference "SecureDefaults" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/vpeschenkov/SecureDefaults.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.2.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		18A7D3F62E0C895400E4ACA8 /* SwipeActions */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18A7D3F52E0C895400E4ACA8 /* XCRemoteSwiftPackageReference "SwipeActions" */;
			productName = SwipeActions;
		};
		18A7D40D2E0C8B0400E4ACA8 /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18A7D40C2E0C8B0400E4ACA8 /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		18A7D60B2E0C947600E4ACA8 /* SwiftyJSON */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18A7D60A2E0C947600E4ACA8 /* XCRemoteSwiftPackageReference "SwiftyJSON" */;
			productName = SwiftyJSON;
		};
		18A7D61C2E0C94FF00E4ACA8 /* SecureDefaults */ = {
			isa = XCSwiftPackageProductDependency;
			package = 18A7D61B2E0C94FF00E4ACA8 /* XCRemoteSwiftPackageReference "SecureDefaults" */;
			productName = SecureDefaults;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 18A7D3AD2E0C857700E4ACA8 /* Project object */;
}
