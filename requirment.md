I need you to recreate the Android Doctor Dashboard "Home" (not the admin dashboard) tab UI in my iOS project with pixel-perfect accuracy. Here's what I need:
1. **Important things to do before making any changes:**
   - make a deep reasoning before making any changes
   - think of plans before making any changes
   - make sure you understand the requirements before making any changes
   - make sure you understand the code before making any changes
   - make sure you understand the architecture before making any changes
   - make sure you understand the design before making any changes
   - make sure you understand the Android code before making any changes
  - make sure you understand the iOS code before making any changes
  - safely iterate through the changes one by one
  - make sure to not make any syntax or compile errors when making changes
  - refer other screens or sections to understand the code and logic implementation patterns and architecture patterns
  - do not write comment that mention android when adding any new code or making any changes
2. **Analysis Phase:**
   - Examine the Android project's "Home" tab content screen to understand the exact UI layout, components, and styling
   - Analyze the existing iOS HomeView.swift file (currently selected) to understand the current implementation
   - Review other iOS tab implementations (specifically SettingsView) as a reference for consistent patterns and architecture

3. **Implementation Requirements:**
   - Replace the existing content in iOS HomeView.swift with the Android-equivalent UI
   - Maintain pixel-perfect visual fidelity to the Android design
   - Preserve the existing SuperViewModel/MVVM architecture patterns
   - Reuse existing iOS UI components where they match the Android design (like CustomInputField)
   - Follow the established async/await networking patterns for any data operations
   - there is a section i made already which is the top section and you don't need to change it you have to preserve it when making other change 
   - make the all changes one by one without making any syntax error or compile error be careful with the implementation take your time to understand the code and the requirements before making any changes

4. **Asset Migration (if needed):**
   - Migrate any required fonts from Android res/font/ to iOS Global/Resources/fonts/
   - Convert drawable assets to iOS Assets.xcassets with proper naming conventions
   - Consider SF Symbols alternatives where appropriate
   - Ensure proper Xcode target membership for all new assets
   - this is the exact path of the android project "/Users/<USER>/Desktop/HashimAndroid/WasfaAdminAndroid" use if you needed

5. **Validation:**
   - Ensure the implementation builds successfully without compilation errors
   - Verify visual consistency with the Android source
   - Maintain compatibility with existing iOS navigation and state management patterns
   - files and folders no need to be added to the Xcode project because xcode latest version will automatically detect them 
6. **make a accurate reference check:**
   - when checking for model class in android project make sure to implement exact model class with all its property with correct type for api parsing
   - Ensure proper JSON parsing by using the correct Swift data types (Int, String, Bool, Double) that match the actual API response by refering to the api response parsing models in the android project
7. **Build Test:**
- run a build test to check for any compilation errors and catch all errors at once and resolve all at once
- when resolving any errors make sure to not make any new errors and resolve all errors at once

Please start by analyzing the Android Home tab UI and the current iOS implementation before making any changes. with making a detailed Tasks to execute step by steps using task management tools